import json

import pandas as pd
import pymysql
import logging
import time


def mysql_log(session_id):
    max_attempts = 3
    attempt = 0

    while attempt < max_attempts:
        conn = None
        cursor = None
        try:
            # 建立数据库连接
            conn = pymysql.connect(
                host="***********",
                user="root",
                password="root123321",
                database="decision_ai",
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            cursor = conn.cursor()
            cursor.execute(
                "SELECT trace_logs FROM trace_logs WHERE query=%s AND created_at > %s;",
                (session_id, '2024-09-19 22:00:00')
            )
            result = cursor.fetchone()
            # print(result["trace_logs"])

            return result["trace_logs"]

        except pymysql.MySQLError as err:
            logging.error(f"Error: {err}")
            attempt += 1
            time.sleep(20)
        finally:
            # 确保游标和连接被关闭
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    return None


def get_log(path):
    try:
        pf = pd.read_excel(path)
        # logging.INFO("Excel file loaded successfully")

        logs = []

        for index, query in enumerate(pf["query"]):
            # logging.info(f"Processing query {index + 1}: {query}")
            log = mysql_log(query)
            # print(log)
            logs.append(log)
            # logging.INFO(f"正在拉取第 {index + 1}条log")
        # print(logs)
        print(logs)
        pf["log"] = logs
        pf.to_excel(path, index=False, engine='openpyxl')
        # logging.info("Execution completed and results saved to Excel")
    except Exception as e:
        logging.error(f"An error occurred: {e}")


if __name__ == '__main__':
    get_log(r"F:\code\jytest\decisionAI\data\测试集\AMBI_数据问答测试集 (5).xlsx")
    # a="""('====原始问题====\n6月收入超过100w的品牌占比？\nsession_id: 66a40116be369aa8-7bab-11ef-899b-0242ac1100041727316303\n====LLM四要素抽取====\n====LLM四要素抽取结果====\n{\'question\': \'6月收入超过100w的品牌占比？\', \'time_range\': [\'2024-06-01 00:00:00\', \'2024-06-30 23:59:59\'], \'aggregation_type\': \'求和\', \'debug_info\': [\'由于是目标意图，按时间粒度补充维度分组\'], \'intent\': \'占比\', \'indicator\': [{\'text\': \'收入\', \'indicator_id\': \'170\', \'indicator_model_id\': \'886_815f154b-a654-4492-b470-b234ee0b43a6\', \'analysis_dimensions\': [[\'城市\', \'328\'], [\'区\', \'329\'], [\'省份\', \'330\'], [\'性别\', \'331\'], [\'品牌\', \'332\'], [\'类目\', \'333\'], [\'商品\', \'334\']]}], \'time_granularity\': \'month\', \'original_filter_time\': [{\'text\': \'6月\', \'time\': [\'2024-06-01 00:00:00\', \'2024-06-30 23:59:59\']}], \'filter\': [[{\'type\': \'indicator\', \'text\': \'收入\', \'indicator_id\': \'170\', \'cond\': \'>\', \'value\': \'100w\'}, {\'type\': \'dimension\', \'text\': \'品牌\', \'dimension_id\': \'332\', \'intent_dimension\': True}, {\'text\': \'6月\', \'type\': \'time\', \'time\': [\'2024-06-01 00:00:00\', \'2024-06-30 23:59:59\']}]], \'proportion_dimension\': [{\'type\': \'dimension\', \'text\': \'品牌\', \'dimension_id\': \'332\', \'intent_dimension\': True}], \'original_filter\': [[{\'type\': \'indicator\', \'text\': \'收入\', \'indicator_id\': \'170\', \'cond\': \'>\', \'value\': \'100w\'}, {\'type\': \'dimension\', \'text\': \'品牌\', \'dimension_id\': \'332\', \'intent_dimension\': True}]], \'time_type\': \'single_point\', \'time_granularity_unit\': 6, \'group_by\': [{\'type\': \'dimension\', \'text\': \'品牌\', \'dimension_id\': \'332\'}], \'original_parse\': {\'意图\': \'占比\', \'指标\': \'收入\', \'时间范围\': [{\'查询时间\': \'6月\', \'时间粒度\': \'月\', \'同环比或对比时间\': []}], \'时间聚合粒度\': \'\', \'筛选\': [[{\'筛选类型\': \'指标\', \'筛选字段\': \'收入\', \'操作符\': \'>\', \'是否需要分组\': \'否\', \'筛选值\': [\'100w\']}, {\'筛选类型\': \'维度\', \'筛选字段\': \'品牌\', \'操作符\': \'=\', \'是否需要分组\': \'否\', \'筛选值\': [\'EMPTY\']}]], \'占比维度\': [\'品牌\'], \'排序方式\': \'\', \'limit\': \'\'}}====开始执行sql查询====\n====获取指标sql====\n"select DATE_FORMAT(`fact_orders_for_qa`.`date`, \'%Y-%m\') as data_time,  `fact_orders_for_qa`.`total_amount`, `dim_product`.`brand` from `fact_orders_for_qa` inner join `dim_product` on `fact_orders_for_qa`.`product_id` = `dim_product`.`product_id` where `fact_orders_for_qa`.`total_amount` > 100w and (`fact_orders_for_qa`.`date` >= \'2024-06-01\' and `fact_orders_for_qa`.`date` <= \'2024-06-30\')"\n====获取指标sql异常====\nTraceback (most recent call last):\n  File "/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1970, in _exec_single_context\n    self.dialect.do_execute(\n  File "/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/default.py", line 924, in do_execute\n    cursor.execute(statement, parameters)\n  File "/usr/local/lib/python3.10/site-packages/pymysql/cursors.py", line 153, in execute\n    result = self._query(query)\n  File "/usr/local/lib/python3.10/site-packages/pymysql/cursors.py", line 322, in _query\n    conn.query(q)\n  File "/usr/local/lib/python3.10/site-packages/pymysql/connections.py", line 558, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n  File "/usr/local/lib/python3.10/site-packages/pymysql/connections.py", line 822, in _read_query_result\n    result.read()\n  File "/usr/local/lib/python3.10/site-packages/pymysql/connections.py", line 1200, in read\n    first_packet = self.connection._read_packet()\n  File "/usr/local/lib/python3.10/site-packages/pymysql/connections.py", line 772, in _read_packet\n    packet.raise_for_error()\n  File "/usr/local/lib/python3.10/site-packages/pymysql/protocol.py", line 221, in raise_for_error\n    err.raise_mysql_exception(self._data)\n  File "/usr/local/lib/python3.10/site-packages/pymysql/err.py", line 143, in raise_mysql_exception\n    raise errorclass(errno, errval)\npymysql.err.OperationalError: (1054, "Unknown column \'100w\' in \'where clause\'")\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File "/decision-ai/app/chat/work_flow/unit/sql/sql.py", line 255, in execute_sql\n    sql_result = sql_run(app_id=app_id, enterprise_id=enterprise_id, sql=sql)\n  File "/decision-ai/app/chat/utils/utils.py", line 32, in sql_run\n    result = out_session.execute(text(sql)).fetchall()\n  File "/usr/local/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 2306, in execute\n    return self._execute_internal(\n  File "/usr/local/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 2200, in _execute_internal\n    result = conn.execute(\n  File "/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1421, in execute\n    return meth(\n  File "/usr/local/lib/python3.10/site-packages/sqlalchemy/sql/elements.py", line 514, in _execute_on_connection\n    return connection._execute_clauseelement(\n  File "/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1643, in _execute_clauseelement\n    ret = self._execute_context(\n  File "/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1849, in _execute_context\n    return self._exec_single_context(\n  File "/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1989, in _exec_single_context\n    self._handle_dbapi_exception(\n  File "/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 2356, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File "/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1970, in _exec_single_context\n    self.dialect.do_execute(\n  File "/usr/local/lib/python3.10/site-packages/sqlalchemy/engine/default.py", line 924, in do_execute\n    cursor.execute(statement, parameters)\n  File "/usr/local/lib/python3.10/site-packages/pymysql/cursors.py", line 153, in execute\n    result = self._query(query)\n  File "/usr/local/lib/python3.10/site-packages/pymysql/cursors.py", line 322, in _query\n    conn.query(q)\n  File "/usr/local/lib/python3.10/site-packages/pymysql/connections.py", line 558, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n  File "/usr/local/lib/python3.10/site-packages/pymysql/connections.py", line 822, in _read_query_result\n    result.read()\n  File "/usr/local/lib/python3.10/site-packages/pymysql/connections.py", line 1200, in read\n    first_packet = self.connection._read_packet()\n  File "/usr/local/lib/python3.10/site-packages/pymysql/connections.py", line 772, in _read_packet\n    packet.raise_for_error()\n  File "/usr/local/lib/python3.10/site-packages/pymysql/protocol.py", line 221, in raise_for_error\n    err.raise_mysql_exception(self._data)\n  File "/usr/local/lib/python3.10/site-packages/pymysql/err.py", line 143, in raise_mysql_exception\n    raise errorclass(errno, errval)\nsqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column \'100w\' in \'where clause\'")\n[SQL: select DATE_FORMAT(`fact_orders_for_qa`.`date`, \'%%Y-%%m\') as data_time,  `fact_orders_for_qa`.`total_amount`, `dim_product`.`brand` from `fact_orders_for_qa` inner join `dim_product` on `fact_orders_for_qa`.`product_id` = `dim_product`.`product_id` where `fact_orders_for_qa`.`total_amount` > 100w and (`fact_orders_for_qa`.`date` >= \'2024-06-01\' and `fact_orders_for_qa`.`date` <= \'2024-06-30\')]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\n====开始执行报告====\n====报告参数:{\'start_time\': \'2024-06-01\', \'end_time\': \'2024-06-30\', \'indicator_name\': \'收入\', \'time_granularity\': \'month\', \'intention\': \'占比\', \'aggregation_type\': \'求和\', \'time_granularity_unit\': 6, \'query\': \'6月收入超过100w的品牌占比？\', \'app_id\': \'a403ed4348764f2aa9b07173ba4cce55\', \'chat_id\': \'1727316303\'}====\n====报告执行结果====\n{\'growth_rate_analysis\': {\'start_time\': \'2024-06\', \'start_value\': \'23,257,020,810.00元\', \'end_time\': \'2024-06\', \'end_value\': \'23,257,020,810.00元\', \'diff_display\': 0, \'growth_rate\': \'0.00%\', \'diff\': \'0.00元\'}, \'extreme_value_analysis\': {\'interval_max\': \'23,257,020,810.00元\', \'interval_min\': \'23,257,020,810.00元\', \'interval_average\': \'23,257,020,810.00元\', \'start_time\': \'2024-06\', \'end_time\': \'2024-06\'}, \'mom_yoy_analysis\': {\'mom\': \'16.45%\', \'yoy\': \'1416.10%\', \'mom_diff\': 3284606638, \'yoy_diff\': 21723021451, \'mom_value\': 19972414172, \'yoy_value\': 1533999359, \'base_value\': 23257020810, \'mom_date\': [\'2024-05-02\', \'2024-05-31\'], \'yoy_date\': [\'2023-06-01\', \'2023-06-30\'], \'current_time\': [\'2024-06-01\', \'2024-06-30\']}, \'attribution_analysis\': [{\'dimension_column_display_name\': \'性别\', \'dimension_data\': {\'positive_contribution_ranking\': [{\'维度值\': \'男\', \'2024-05-02到2024-05-31\': \'13,250,359,790.00元\', \'2024-06-01到2024-06-30\': \'15,039,658,543.00元\', \'变化值\': \'1,789,298,753.00元\', \'贡献值\': \'54.48%\'}, {\'维度值\': \'女\', \'2024-05-02到2024-05-31\': \'6,722,054,382.00元\', \'2024-06-01到2024-06-30\': \'8,217,362,267.00元\', \'变化值\': \'1,495,307,885.00元\', \'贡献值\': \'45.52%\'}], \'negative_contribution_ranking\': [], \'total_change\': 3284606638}, \'dimension_column_id\': \'column_20240919182324255519_okteD5\', \'dimension_column_id_name\': \'gender_text\', \'self_drill_down\': False, \'cross_drill_down\': True}], \'dimension_list\': [{\'table_name\': \'dim_customer\', \'column_name\': \'gender_text\', \'column_id\': \'column_20240919182324255519_okteD5\', \'column_type\': \'2\', \'display_name\': \'性别\'}, {\'table_name\': \'dim_customer\', \'column_name\': \'province\', \'column_id\': \'column_20240919182324255557_6MoEiW\', \'column_type\': \'2\', \'display_name\': \'省份\'}, {\'table_name\': \'dim_product\', \'column_name\': \'brand\', \'column_id\': \'column_20240919182324287487_SAq141\', \'column_type\': \'2\', \'display_name\': \'品牌\'}, {\'table_name\': \'dim_product\', \'column_name\': \'category\', \'column_id\': \'column_20240919182324287570_V9mrdY\', \'column_type\': \'2\', \'display_name\': \'类目\'}, {\'table_name\': \'dim_product\', \'column_name\': \'product_name\', \'column_id\': \'column_20240919182324287621_huHlY9\', \'column_type\': \'2\', \'display_name\': \'商品\'}], \'show_time\': [\'2024-06-01\', \'2024-06-30\'], \'intention\': \'占比\', \'indicator_name\': \'收入\', \'time_granularity_unit\': 6, \'time_granularity\': \'month\'}\n====报告执行耗时====\n耗时: 1.1450984477996826\n====生成洞察结果====\n根据查询条件求和,收入,品牌,6月未查询到数据。请检查查询要素是否准确，并确认已具备相应数据权限。如有问题请随时联系管理员处理。',)"""
    # print(a.replace("\n","\n"))
