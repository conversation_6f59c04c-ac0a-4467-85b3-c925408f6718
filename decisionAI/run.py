import sys
from data_answers import main
from assessment_score import resp_answers_gpt


def main2(file_case_name):
    # 这里是 main2 方法的实现
    print(f"Executing main2 with {file_case_name}")


if __name__ == '__main__':
    if len(sys.argv) < 3:
        print("请提供表格名称和是否评测参数作为参数")
        sys.exit(1)

    file_case_name = sys.argv[1]
    gpt_flag_str = sys.argv[2].lower()

    if gpt_flag_str not in ['true', 'false']:
        print("第二个参数必须是 'true' 或 'false'")
        sys.exit(1)

    gpt_flag = gpt_flag_str == 'true'

    path = main(file_case_name)

    if gpt_flag:
        resp_answers_gpt(path)
