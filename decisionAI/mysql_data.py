import re
import pymysql
import pandas as pd
from model_llm import get_response_gpt4o


def mysql_data(sql):
    result, columns, conn, cursor = None, None, None, None
    try:
        conn = pymysql.connect(
            host="************",
            user="root",
            password="yourmysql8password",
            database="ecommerce_bi",
            charset='utf8mb4'
        )
        cursor = conn.cursor()
        cursor.execute(sql)
        result = cursor.fetchall()
        columns = [desc[0] for desc in cursor.description]

    except pymysql.MySQLError as e:
        print(f"数据库错误: {e}")
        return None, None
    finally:
        if cursor is not None:
            cursor.close()
        if conn is not None:
            conn.close()
    return result, columns


def extract_sql_from_response(response):
    sql_pattern = r'```sql\n(.*?)```'
    matches = re.findall(sql_pattern, response, re.DOTALL)
    if matches:
        return re.sub(r"\n", " ", matches[0])
    return None


def process_queries(pf, prompt):
    data = []
    sql_queries = []

    for index, query in enumerate(pf["query"]):
        print(f"Processing query {index+1}: {query}")
        formatted_prompt = prompt.format(query)
        response = get_response_gpt4o(formatted_prompt)
        # print(response)
        sql_query = extract_sql_from_response(response)
        sql_queries.append(sql_query)

        if sql_query:
            result, columns = mysql_data(sql_query)
            if result is not None:
                df = pd.DataFrame(result, columns=columns)
                data.append(df)
                pf.loc[index, 'sql'] = sql_query
                pf.loc[index, '结果'] = df.to_string(index=False)
            else:
                data.append("查询失败或无结果")
                pf.loc[index, 'sql'] = sql_query
                pf.loc[index, '结果'] = "查询失败或无结果"
        else:
            print("未找到 SQL 查询。")
            result = None  # 给 result 一个默认值
            data.append("未找到 SQL 查询。")
            pf.loc[index, 'sql'] = "未找到 SQL 查询。"
            pf.loc[index, '结果'] = "未找到 SQL 查询。"

    return sql_queries, data


def save_to_excel(pf, output_path):
    pf.to_excel(output_path, index=False)


if __name__ == '__main__':
    input_path = r"C:\Users\<USER>\Downloads\Decision AI 测试集.xlsx"
    output_path = r"C:\Users\<USER>\Downloads\Decision AI v6结果.xlsx"

    pf = pd.read_excel(input_path)
    prompt = """### 角色能力 ###
你是一个专业的算法工程人员，善于使用MySQL，你的主要工作是提出的数据问题，写成相对应的MySQL语句，去数据库查询到他的数据。

##示例
-----------------
示例1：
问题为：广州市过去1-3天的收入是多少？
对应的SQL语句为：
SELECT
	SUM( fact_orders_for_qa.total_amount ) AS '收入' 
FROM
	fact_orders_for_qa
	INNER JOIN dim_customer ON fact_orders_for_qa.customer_id = dim_customer.customer_id 
WHERE
	DATE BETWEEN '2024-07-08' 
	AND '2024-07-10' 
	AND city = '广州市' 
-----------------
示例2：
问题为：6月2日，北京市小米的平均每单优惠金额同环比是？ 
对应的SQL语句为：
SELECT DATE
	,
	sum( fact_orders_for_qa.total_amount ) AS 收入,
	sum( fact_orders_for_qa.pay_amount ) AS 支付金额,
	sum( fact_orders_for_qa.order_count ) AS 订单数,(
	fact_orders_for_qa.total_amount - ( fact_orders_for_qa.pay_amount * 0.9 ))/ fact_orders_for_qa.order_count AS 平均每单优惠金 
FROM
	fact_orders_for_qa
	INNER JOIN dim_customer ON fact_orders_for_qa.customer_id = dim_customer.customer_id
	INNER JOIN dim_product ON fact_orders_for_qa.product_id = dim_product.product_id 
WHERE
	DATE IN ( '2024-06-02', '2023-06-02', '2024-06-01' ) 
	AND city = '北京市' 
	AND dim_product.brand = '小米' 
GROUP BY
DATE 
ORDER BY
DATE
-----------------

 ## 注意事项

 1. 你只需要输出sql语句即可，不要输出任何sql语句以外的内容
 2. 多个字段计算的问题请根据示例2必须分别输出原始字段和计算后的字段数据
 3.问题中存在同环比时，同比时间段取上一年的当前时间段，环比取当前时间段的前一个时间段
 4. 多个字段计算的问题请根据示例2必须分别输出原始字段和计算后的字段数据
 5.占比类型query不用计算占比只输出展示维度的数值和全量的数值


##时间规则
1.同环比类型的query不需要进行日期分组
	例如：上个月  同比时间：2023.06.01-2023.06.30   环比时间 ： 2024.05.01-2024.05.31
		   最近3天   同比时间： 2023.07.08-2023.07.10  环比时间：2024.07.05-2024.07.07
2.如果query内没有指定具体时间，默认取最近两周
3.周粒度的时间从周一到周日为一周
4.模糊时间取值枚举
	例如：过去一个月   date BETWEEN '2024-06-10' AND '2024-07-10'
5. 趋势类的query如果包含每季度、每月、每周、每天按照指定的季度进行分组；
6.趋势类的query如果没有指定分组类型则分析时间段，不足三个月的则按照周进行分组，不足三周的则按照日进行分组	，
	例如：最近两周                按照日进行分组	   GROUP BY date
		   过去一个月            按照周进行分组    GROUP BY week(date,1)
7.趋势类的query如果时间段为整月按照天进行分组
	例如：6月1日到6月30日   按照日进行分组	   GROUP BY date
		   5月1日到6月30日   按照周进行分组    GROUP BY week(date,1)
		   
# 数据表 

## 商品信息表 

-----------------
### 表名称：dim_product

### 表字段：
product_id：商品id
product_name：商品名称
category：类目
category_id：类目id
brand：品牌
brand_id：品牌id
-------------------

### 订单表 

-----------------
### 表名称：fact_orders_for_qa

### 表字段：

id：订单id
date：订单日期
customer_id：用户id
product_id：商品id
quantity：购买数量
unit_price：平均单价
total_amount：收入
discount_amount：优惠金额
pay_amount：支付金额
order_count：订单数
profit：利润
-------------------

## 用户信息表

-----------------
### 表名称：dim_customer

### 表字段：

customer_id：用户id
customer_name：用户姓名
gender：性别编码
gender_text：性别
email：邮箱
phone：电话
province：省份
city：城市
district：区
registration_date：注册日期


-------------------
## 库表关联关系
-----------------
事实表:fact_orders_for_qa
维度表1:dim_product(商品) 关联关系:fact_orders_for_qa,product_id=dim_product.product_id
维度表2:dim_customer(用户) 关联关系:fact_orders_qa.customer_id=dim_customer.customer_id
每一个sql进行三表关联

-------------------
##维度字段对应
商品名称：智能手表Pro、瑜伽垫、儿童益智玩具、口琴、跑步机、纯银手镯、北欧风格沙发、超薄笔记本电脑、女士连衣裙、古典音乐CD、电吉他、专业绘图板、架子鼓、多功能料理机、民谣吉他、男士休闲夹克、口琴、笔记本、男士休闲夹克、儿童绘本套装、架子鼓、投影仪、羽绒服、高跟鞋、智能门锁、跑步机、智能冰箱、防滑浴室垫、牛仔裤、高跟鞋、多功能料理机
类目：电子产品、运动户外、母婴、乐器、运动户外、珠宝配饰、家具、电子产品、服装、图书音像、乐器、办公用品、乐器、家用电器、乐器、服装、乐器、办公用品、服装、母婴、乐器
办公用品、服装、鞋类、安防、运动户外、家用电器、家居日用、服装、鞋类、家用电器



## 指标名称及计算公式

-----------------
销售额 = SUM(支付金额)+SUM(优惠金额)

毛利 = SUM(收入)-SUM(支付金额)*0.9

实际营收 = AVG(平均单价）*SUM(购买数量)-SUM(优惠金额)

优惠率 = （SUM(购买数量)*AVG(平均单价）)/ SUM(支付金额)

平均订单价 = SUM(支付金额)/SUM(订单数)

平均每单优惠金额 = (SUM(收入)-SUM(支付金额)*0.9)/ SUM(订单数)

每单商品利润 = AVG(平均单价）-（SUM(优惠金额)/ SUM(购买数量))

动态优惠金额 = AVG(平均单价）*（SUM(订单数)/ SUM(购买数量)）*0.2

折后支付金额 = SUM(收入)*0.8+（SUM(优惠金额)/10）

总营收 = （SUM(支付金额)+SUM(优惠金额)）* SUM(订单数)

负债率 = 1-(（1-SUM(优惠金额)/SUM(支付金额)）-（1-(SUM(收入)-SUM(支付金额)）/ (SUM(支付金额)+SUM(优惠金额)))

有效支付金额 = （SUM(支付金额)-SUM(优惠金额))/ SUM(订单数)+（SUM(收入)+ SUM(优惠金额))/ AVG(平均单价)

折后平均单价 = AVG(平均单价)- SUM(支付金额) /（SUM(优惠金额)*SUM(订单数)）


-------------------
今天的日期为：2024.7.11号，请你根据提到的表信息使用INNER JOIN编写查询的SQL语句，
查询内容为：{}
"""

    sql_queries, data = process_queries(pf, prompt)
    save_to_excel(pf, output_path)
