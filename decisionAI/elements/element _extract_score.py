import pandas as pd
import json


# 读取Excel文件到DataFrame
def read_excel(file_path):
    try:
        df = pd.read_excel(file_path)
        return df
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None


# 排序字典列表
def sort_list_of_dicts(lst):
    return sorted(lst, key=lambda x: json.dumps(x, sort_keys=True))


# 比较两个JSON对象
def compare_json(json1, json2, path=""):
    differences = []

    if isinstance(json1, dict) and isinstance(json2, dict):
        for key in set(json1.keys()).union(json2.keys()):
            new_path = f"{path}.{key}" if path else key
            if key in json1 and key in json2:
                differences.extend(compare_json(json1[key], json2[key], new_path))
            elif key in json1:
                differences.append(f"第二个JSON缺少路径：{new_path}")
            else:
                differences.append(f"第一个JSON缺少路径：{new_path}")

    elif isinstance(json1, list) and isinstance(json2, list):
        json1_sorted = sort_list_of_dicts(json1)
        json2_sorted = sort_list_of_dicts(json2)
        for index, (item1, item2) in enumerate(zip(json1_sorted, json2_sorted)):
            new_path = f"{path}[{index}]"
            differences.extend(compare_json(item1, item2, new_path))
        if len(json1_sorted) > len(json2_sorted):
            for index in range(len(json2_sorted), len(json1_sorted)):
                new_path = f"{path}[{index}]"
                differences.append(f"第二个JSON缺少路径：{new_path}")
        elif len(json2_sorted) > len(json1_sorted):
            for index in range(len(json1_sorted), len(json2_sorted)):
                new_path = f"{path}[{index}]"
                differences.append(f"第一个JSON缺少路径：{new_path}")

    else:
        if json1 != json2:
            differences.append(f"路径 {path} 的值不同：{json1} vs {json2}")

    return differences


def main(path_file):
    df = read_excel(path_file)
    if df is None:
        return

    score = []
    cause = []

    for json_str1, json_str2 in zip(df["预期四要素"], df["实际结果_四要素"]):
        try:
            json1 = json.loads(json_str1)
            json2 = json.loads(json_str2)
            print(json1[0])
            print(json2)
            differences = compare_json(json1[0], json2)
            if differences:
                result = '\n'.join(differences) + '\n'
                score.append(0)
                cause.append(result)
            else:
                score.append(1)
                cause.append("")
        except Exception as e:
            print(f"JSON解析错误: {e}")
            score.append(0)
            cause.append("JSON解析错误")

    df["分数"] = score
    df["原因"] = cause

    try:
        df.to_excel(path_file, index=False)
        print("数据回写完成")
    except Exception as e:
        print(f"写入Excel文件时出错: {e}")


if __name__ == '__main__':
    # main(r"C:\Users\<USER>\Downloads\Decision AI效果评测_原子指标测试集_100 (3).xlsx")

    # pf=pd.read_excel(r"C:\Users\<USER>\Downloads\Decision AI效果评测_四要素抽取_gpt4o_1231.xlsx")
    # a=[]
    # b=[]
    # for  row in pf["预期四要素"]:
    #     b = []
    #     b.append(row)
    #     a.append(b)
    # pf["新预期四要素"]=a
    # pf.to_excel(r"C:\Users\<USER>\Downloads\Decision AI效果评测_四要素抽取_gpt4o_1231.xlsx",index=False)
    # import pandas as pd
    #
    # # 读取 Excel 文件
    # pf1 = pd.read_excel(r"F:\code\jytest\decisionAI\data\测试集\Decision AI效果评测_原子指标测试集_100.xlsx",
    #                     sheet_name='Qwen25')
    # pf2 = pd.read_excel(r"F:\code\jytest\decisionAI\data\测试集\Decision AI效果评测_四要素抽取_复合指标_GPT-4o.xlsx",
    #                     sheet_name='Qwen25')
    # pf3 = pd.read_excel(r"F:\code\jytest\decisionAI\data\测试集\Decision AI效果评测_四要素抽取_标签_GPT-4o (2).xlsx",
    #                     sheet_name='Qwen25')
    #
    # # 合并运行时间列并确保数据类型为浮点型
    # time1 = pd.to_numeric(pf1["YiLightning_运行时间"].fillna(0), errors='coerce')
    # time2 = pd.to_numeric(pf2["YiLightning_运行时间"].fillna(0), errors='coerce')
    # # time3 = pd.to_numeric(pf3["YiLightning_运行时间"].fillna(0), errors='coerce')
    #
    # # 合并所有时间
    # time = pd.concat([time1, time2])
    # # 去除可能的NaN值
    # time = time.dropna()
    #
    # # 计算平均值、最大值和最小值
    # avgtime = time.mean()
    # maxtime = time.max()
    # mintime = time.min()
    #
    # # 打印结果
    # print(f"平均时间: {avgtime}")
    # print(f"最大时间: {maxtime}")
    # print(f"最小时间: {mintime}")
    import pandas as pd
    import json

    # 读取 Excel 文件
    file_path = r"C:\Users\<USER>\Downloads\Decision AI效果评测_原子指标测试集_100 (3).xlsx"
    df = pd.read_excel(file_path, engine='openpyxl')


    # 假设 JSON 数据在某一列中，比如 'json_column'
    def format_json(json_str):
        try:
            # 解析并格式化 JSON 数据
            parsed_json = json.loads(json_str)
            formatted_json = json.dumps(parsed_json, indent=4, ensure_ascii=False)
            return formatted_json
        except (json.JSONDecodeError, TypeError):
            return json_str  # 返回原始数据以防解析失败


    # 应用格式化函数
    df['分析报告计算结果_实际'] = df['分析报告计算结果_实际'].apply(format_json)

    # 将格式化后的数据写回 Excel
    df.to_excel(r"C:\Users\<USER>\Downloads\Decision AI效果评测_原子指标测试集_100 (3).xlsx", index=False, engine='openpyxl')
