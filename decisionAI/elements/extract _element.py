import pandas as pd
import re
import time

from decisionAI.model_llm import get_response_gpt4o, get_response_yi_lightning, get_response_deepseek, \
    get_response_qwen25



def is_valid_utf8(data):
    try:
        # 尝试解码为 UTF-8
        data.encode('utf-8').decode('utf-8')
    except UnicodeDecodeError:
        return False
    return True


def extract_element(path):
    query_prompt = """
    你是一个电子商务领域的数据分析专家，请参考领域知识并按照提取规则对用户的查询进行信息提取以及意图分类；请按照指定格式输出。
        # 注意:“指标名”，“维度”，“标签”是不同的概念，不要混淆，不需要用常识进行补充。

## 领域知识
### 下面介绍分析的指标，每个指标包括如下字段“指标名”、“指标描述”和“分析维度”。“指标名”：即指标名称； “指标描述”：描述指标信息，也包括指标的别称； “分析维度”：表述指标可以从哪些维度进行分析
[{{"指标名":"订单数","指标描述":"","分析维度":["城市","区","省份","品牌","类目","用户ID","商品","商品ID"]}},
{{"指标名":"支付金额","指标描述":"","分析维度":["城市","区","省份","品牌","类目","商品","商品ID","用户ID"]}},
{{"指标名":"收入","指标描述":"","分析维度":["城市","区","省份","品牌","类目","商品","商品ID","用户ID"]}},
{{"指标名":"优惠金额","指标描述":"","分析维度":["城市","区","省份","品牌","类目","商品","商品ID","用户ID"]}},
{{"指标名":"平均单价","指标描述":"","分析维度":["省份","城市","区","品牌","类目","商品","商品ID","用户ID"]}},
{{"指标名":"购买数量","指标描述":"","分析维度":["省份","城市","区","品牌","类目","商品","商品ID","用户ID"]}},
{{"指标名":"销售额","指标描述":"支付金额+优惠金额","分析维度":["城市","区","省份","品牌","类目","商品"]}},
{{"指标名":"毛利","指标描述":"收入-支付金额*0.9","分析维度":["城市","区","省份","品牌","类目","商品"]}},
{{"指标名":"实际营收","指标描述":"平均单价*购买数量-优惠金额","分析维度":["省份","城市","区","品牌","类目","商品","商品ID","用户ID"]}},
{{"指标名":"优惠率","指标描述":"(购买数量*平均单价)/支付金额","分析维度":["省份","城市","区","品牌","类目","商品","商品ID","用户ID"]}},
{{"指标名":"平均订单价","指标描述":"支付金额/订单数","分析维度":["城市","区","省份","品牌","类目","商品","商品ID","用户ID"]}},
{{"指标名":"平均每单优惠金额","指标描述":"(收入-支付金额*0.9)/订单数","分析维度":["城市","区","省份","品牌","类目","商品","商品ID","用户ID"]}},
{{"指标名":"每单商品利润","指标描述":"平均单价-(优惠金额/购买数量)","分析维度":["省份","城市","区","品牌","类目","商品","商品ID","用户ID"]}},
{{"指标名":"动态优惠金额","指标描述":"平均单价*(订单数/购买数量)*0.2","分析维度":["省份","城市","区","品牌","类目","商品","商品ID","用户ID"]}},
{{"指标名":"折后支付金额","指标描述":"收入*0.8+(优惠金额/10)","分析维度":["城市","区","省份","品牌","类目","商品","商品ID","用户ID"]}},
{{"指标名":"总营收","指标描述":"(支付金额+优惠金额)*订单数","分析维度":["城市","区","省份","品牌","类目","商品","商品ID","用户ID"]}},
{{"指标名":"负债率","指标描述":"1-((1-优惠金额/支付金额)-(1-(收入-支付金额)/(支付金额+优惠金额)))","分析维度":["城市","区","省份","品牌","类目","商品","商品ID","用户ID"]}},
{{"指标名":"有效支付金额","指标描述":"(支付金额-优惠金额)/订单数+(收入+优惠金额)/平均单价","分析维度":["城市","区","省份","品牌","类目","商品","商品ID","用户ID"]}},
{{"指标名":"折后单价","指标描述":"平均单价-(支付金额/(优惠金额*订单数))","分析维度":["省份","城市","区","品牌","类目","商品","商品ID","用户ID"]}}]

### 下面介绍各个分析维度，每个分析维度包括如下字段：“维度名”、“可取值”和“备注”。“维度名”：即维度名称； “可取值”：该维度的可取值，有些并没有列举所有的值，在备注中会说明； “备注”：维度备注信息，请注意该字段信息；千万注意，当标签中包含有维度值时，依然按标签解析
[{{"维度名":"城市","可取值":["宿州市","北京市","青岛市","深圳市","上海市","广州市","南京市","杭州市","成都市","武汉市"],"备注":"可取值只列举了部分"}},
{{"维度名":"区","可取值":["砀山县","通州区","朝阳区","青岛区","宝安区","浦东新区","天河区","玄武区","西湖区","武侯区","江汉区","市南区","思明区","和平区","雁塔区","金水区","岳麓区","蜀山区","东湖区","小店区","长安区","南关区","道里区","天山区","回民区","青秀区","美兰区","南明区","五华区","兴庆区","城东区","城关区","渝中区","海淀区","静安区","南山区","姑苏区","海曙区","涪城区","西陵区","历下区","鼓楼区","中山区","渭滨区","西工区","天元区","镜湖区","章贡区","平城区","路南区","船营区","龙沙区","克拉玛依区","昆都仑区","城中区","吉阳区","红花岗区","麒麟区","大武口区","平安区"],"备注":"可取值只列举了部分"}},
{{"维度名":"省份","可取值":["安徽省","北京市","山东省","广东省","上海市","江苏省","浙江省","四川省","湖北省","福建省"],"备注":"可取值只列举了部分"}},
{{"维度名":"品牌","可取值":["Apple","华为","海尔","小米","Nike","鳄鱼","小高","探路者","KFC","西冷","三只松鼠","安利","青岛","农夫山泉","头等舱","宜家","喜临门","捷安特","骆驼","安踏","兰蔻","中信出版社","美燃","周大福","宝岛","中国黄金","雅马哈","网易严选","kindle","好妈妈","速康","海氏海诺"],"备注":"可取值只列举了部分"}},
{{"维度名":"类目","可取值":["电子产品","家用电器","服装","鞋类","食品","酒水","家具","安防","运动户外","母婴","美妆个护","珠宝配饰","乐器","办公用品","图书音像","家居日用","健康医疗"],"备注":"可取值只列举了部分"}},
{{"维度名":"商品","可取值":["超薄笔记本电脑","智能手表Pro","无线蓝牙耳机","4K超高清电视","智能冰箱","多功能料理机","空气净化器","智能扫地机器人","恒温电水壶","男士休闲夹克","女士连衣裙","儿童运动套装","真丝睡衣","羽绒服","牛仔裤","运动鞋","商务皮鞋","高跟鞋","儿童学步鞋","户外登山鞋","防水雨靴","小米粥","有机蔬菜套装","进口牛排","坚果零食礼盒","有机奶粉","全麦面包","经典红酒","精酿啤酒套装","高山矿泉水","果汁饮料礼盒","养生花茶x`","功能性运动饮料","豪华按摩椅","实木餐桌椅组合","北欧风格沙发","儿童书桌椅套装","多功能衣柜","乳胶床垫","智能门锁","无线监控摄像头","家用保险箱","烟雾报警器","防盗报警系统","儿童安全座椅","瑜伽垫","自行车","登山背包","帐篷","跑步机","滑雪装备套装","婴儿推车","婴儿床","儿童益智玩具","婴儿洗护套装","儿童绘本套装","婴儿背带","面部精华液","电动牙刷","护发精油"],"备注":"可取值只列举了部分"}},
{{"维度名":"城市","可取值":[],"备注":"可取值只列举了部分"}},
{{"维度名":"区","可取值":[],"备注":"可取值只列举了部分"}},
{{"维度名":"省份","可取值":[],"备注":"可取值只列举了部分"}},
{{"维度名":"品牌","可取值":[],"备注":"可取值只列举了部分"}},
{{"维度名":"类目","可取值":[],"备注":"可取值只列举了部分"}},
{{"维度名":"商品","可取值":[],"备注":"可取值只列举了部分"}},
{{"维度名":"商品ID","可取值":[],"备注":"可取值只列举了部分"}},
{{"维度名":"用户ID","可取值":[],"备注":"可取值只列举了部分"}},
{{"维度名":"一级部门","可取值":["北部CBU"],"备注":"可取值只列举了部分"}},
{{"维度名":"三级部门","可取值":["天津分区","河北分区","北京分区"],"备注":"可取值只列举了部分"}},
{{"维度名":"下单进展","可取值":[],"备注":"可取值只列举了部分"}}]

### 下面介绍各个标签，每个标签包括如下字段：“标签名”、“可取值”和“备注”。可取值只有”是“和”否“；“标签名”：即标签名称，不要自己发挥，例如我提到“直辖市”这个标签，禁止提取为“北京市、上海市”，依旧保持标签的形式，；“备注”：标签备注信息
[{{"标签名":"单次购买客户","可取值":["是","否"],"备注":null}},
{{"标签名":"回头客","可取值":["是","否"],"备注":null}},
{{"标签名":"高价值","可取值":["是","否"],"备注":null}},
{{"标签名":"高潜力","可取值":["是","否"],"备注":null}},
{{"标签名":"限时折扣","可取值":["是","否"],"备注":null}},
{{"标签名":"热销","可取值":["是","否"],"备注":null}},
{{"标签名":"价格敏感客户","可取值":["是","否"],"备注":null}},
{{"标签名":"长尾客户","可取值":["是","否"],"备注":null}},
{{"标签名":"品牌偏好客户","可取值":["是","否"],"备注":null}},
{{"标签名":"节日客户","可取值":["是","否"],"备注":null}},
{{"标签名":"羊毛党","可取值":["是","否"],"备注":null}},
{{"标签名":"育儿达人","可取值":["是","否"],"备注":null}},
{{"标签名":"新客","可取值":["是","否"],"备注":null}}]

## 提取规则
1. 查询意图提取：对用户查询进行意图类型提取，查询可能包含多种意图类型；类型定义及描述如下：
    数值：具体数值类的查询。（例如：“xxx是多少？”，“xxx有多少？”，“xxx有哪些？”，“xx时间点xxx怎么样？”，“xx时间点xxx如何？”)
    平均：对时间段和某个维度求平均值。注意：如果指标中有对应的平均指标，那么我们不需要提取“平均”的意图（例如：“xxx平均是多少”）
    占比：分布、占比、组成、构成类的查询（例如：“xxx构成如何?”，“xxx由哪些组成？”或“xxx分布如何?”）
    对比：不同时间点/段、维度等的比较类查询（例如：“xx相比xx”，“xx相比xx如何”，“xx对比xx”）
    同比：仅是同比的查询（例如：“xx同比，xx同比xx”，“xx同期相比”）
    环比：仅是环比的查询（例如：“xx环比，xx环比xx”）
    同环比：仅是同环比的查询（例如：“xx同环比”）
    归因：归因分析类的查询（例如：“xxx原因是什么？”，“为什么xxx”，“xxx的贡献度”)
    趋势：一时间段内的趋势类的查询（例如：“xxx趋势?”，“xxx变化情况？”，“xx时间段xxx怎么样？”，“xx时间段xxx如何？”)
2. 查询可能是多指标、多时间段/点、多筛选条件，请按指标粒度拆成多条查询，每条查询包含查询的意图类型、指标、时间范围（包括时间段/点和时间粒度）、时间聚合粒度、筛选、占比维度、排序方式和limit字段，相同指标的查询放到一条。每个字段具体解释如下
    意图类型：意图类型可取值为 数值、平均、占比、对比、同比、环比、同环比、归因、趋势
    指标：指标可选值即为上面领域知识中列举的指标值
    时间范围：时间范围是一个list结构,表示多个时间段/点，每个list元素是一个dict结构，包括如下字段：查询时间、时间粒度、同环比或对比时间。
        --查询时间：表示query中提到的时间文本信息,请只需要提取query的原始文本即可，不需要展开，“Q1”，“Q2”，“Q3”，“Q4”通常标识第一季度、第二季度、第三季度、第四季度，“H1”，“H2”通常标识上半年、下半年，也需要提取；
        --时间粒度：可选值为日、周、月、季、半年、年。表示query中提及的时间对应的时间单位（注意没有“上半年”、“下半年”等这些时间单位）；
        --同环比或对比时间：分别是相对于当前查询时间query中提及的同比时间、环比时间或者对比时间，如果query中没有明确提及这些时间，则值为""
    时间聚合粒度：对时间进行聚合的粒度，可选值为：日、周、月、季、年和""，如果query中有“每天/每日”则为"日"，“每周”则为"周"，“每月”则为"月"，“每季/每季度”则为"季"，“每年”则为"年"，如果query中没有提到“每”字如每日、每月、每年等，则置为""
    筛选：筛选是由多个“条件组”组成，每个“条件组”是由多个“条件”组成，“条件组”之间的关系是“或”的关系，“条件组”内的“条件”之间是“且”的关系。筛选、条件组和条件的关系以及条件的组成结构分别如下：
         筛选：[条件组1,条件组2,...]
         条件组：[条件1,条件2,...]
         条件：{{
            "筛选类型": "筛选类型11",  ##筛选类型可选值为“维度”和“指标”，如果筛选字段是某个维度，则该值为“维度”，如果筛选字段是具体的指标，则该值为“指标”
            "筛选字段": "筛选字段11",  ##筛选字段即为query中提到的具体维度和指标，注意维度一定是query中提取的“指标”下面的“分析维度”，请注意不需要选错了维度，筛选值和筛选字段必须是对应上面领域知识中的维度名和维度下的可取值
            "操作符": "筛选操作符11",  ##操作符可选值为"="或者">"或者"<"或者"!="或者">="或者"<=" 
            "是否需要分组":"是/否",    ## 是否需要对筛选值做分组操作，可选值为“是”或者“否”，如果query中该筛选字段需要分别计算各个值，则该字段值为“是”，否则即为“否”
            "筛选值": ["具体筛选值11"]  ##具体的筛选值，如果“筛选类型”是“维度”，则该值即为具体的维度值，如果“筛选类型”是“指标”，则该值为指标筛选值。另外还有有两个特殊值："ALL"和"EMPTY"（"ALL"：如果该筛选维度前面有量词如每个、所有、各个等这些词则筛选值为"ALL"。"EMPTY":如果筛选维度前面没有任何量词，也即没有提及具体的维度值，从意义上判断，该筛选维度是该query最终需要查询的维度，则该筛选值为"EMPTY"，例如 最高的房型/门店？，大于xxx的渠道，预订来源有哪些? 等等).
         }}
    占比维度：计算占比的时候，是对该指标下的哪个维度进行占比计算，如果query中没有指定，则为""，注意这里必须是领域知识中query中提取的“指标”下面的“分析维度”，不能是指标，如果query中存在，则每个条件组需要提供一个占比维度，如果用户输入的问题中一个条件组中有多个维度，请取该条件组中提及的所有维度的最后一个维度
    排序方式：可选值为降序、升序或者""(空字符串，表示不需要排序)
    limit：选取值的数量，即topk的值，默认为""

## 输出格式
请严格按照下面描述的JSON格式进行输出，不需要解释，输出JSON格式如下:
[
    {{   
        "意图": "意图类型",
        "指标": "具体指标名", 
        "时间范围": [{{"查询时间":"查询时间1","时间粒度":"时间粒度1","同环比或对比时间":["同环比或对比时间11","同环比或对比时间12",....]}}, {{"查询时间":"查询时间2","时间粒度":"时间粒度2","同环比或对比时间":["同环比或对比时间21","同环比或对比时间22",....}},, ...],
        "时间聚合粒度":"时间聚合粒度",
        "筛选":[
            [{{"筛选类型": "筛选类型11","筛选字段": "筛选字段11","操作符": "筛选操作符11", "是否需要分组":"是/否","筛选值": ["具体筛选值11"]}},
             {{"筛选类型": "筛选类型12","筛选字段": "筛选字段12","操作符": "筛选操作符12", "是否需要分组":"是/否","筛选值": ["具体筛选值12"]}}],
            [{{"筛选类型": "筛选类型21","筛选字段": "筛选字段21","操作符": "筛选操作符21", "是否需要分组":"是/否","筛选值": ["具体筛选值21"]}},
             {{"筛选类型": "筛选类型22","筛选字段": "筛选字段22","操作符": "筛选操作符22", "是否需要分组":"是/否","筛选值": ["具体筛选值22"]}}]
        ],
        "占比维度":["占比维度1",...],
        "排序方式":"降序/升序/",
        "limit":"选取多少个值"
    }},
    ...
]
确保输出的格式可以被Python的json.loads方法解析。

## 参考示例
注意：下面是示例用到的领域知识，注意这里只是示例，实际的领域知识请使用上面的领域知识。
# 示例中可能用到的指标如下：
[{{"指标名":"平均房价",  "指标描述":"简称ADR，平均房价=已售房间价格汇总/已售房间数量",  "分析维度":["客源"]}},
{{"指标名":"总营收","指标描述":"也叫实际营收、实际营收额、收入、营业收入、订单金额，即实际获得的总收入", "分析维度":["房型","客源","渠道"]}},
{{"指标名":"客房营收",  "指标描述":"也叫净客房收入、客房收入，通过出租客房所获得的收入", "分析维度":["房型","客源","渠道","省份","门店"]}}]
# 示例中可能用到的分析维度如下：
[{{"维度名":"房型", "可取值":["大床房","双床房","家庭房","流金岁月房","商务双床房","普通双床房","高级双床房","商务大床房","普通大床房","高级大床房"], "备注":"可取值只列举了部分房型"}},
{{"维度名":"客源", "可取值":["个人会员","中介","企业会员","非会员","时租","团队","长包","其他"], "备注":"散客也叫非会员"}},
{{"维度名":"渠道", "可取值":["OTA","IDS","酒店直销","分销","其他"], "备注":"渠道即订单来源"}},
{{"维度名":"省份", "可取值":["北京市","江苏省","浙江省","江西省","安徽省","新疆","黑龙江"], "备注":"可取值只列举了部分省份的值，该维度取值包括所有的省、直辖市和自治区"}},
{{"维度名":"门店", "可取值":["望京店","王府井店"], "备注":"具体的门店"}}]
# 示例中可能用到的分析标签如下：
[{{"标签名":"羊毛党", "可取值":["是","否"], "备注":"指利用规则漏洞或者通过钻研规则，在规则之内获取一些小利益，俗称占便宜。"}}]

### 示例1：分析：意图是同环比，大床房和双床房属于房型维度的取值，所以筛选类型为维度，筛选字段为房型，筛选值为大床房和双床房，需要分组
用户输入：2023年2月1日至2月15日大床房和双床房的同环比情况？
你的输出：[{{"意图": "同环比", "指标": "", "时间范围": [{{"查询时间":"2023年2月1日至2023年2月15日","时间粒度":"日","同环比或对比时间":[]}}], "时间聚合粒度":"", "筛选":[[{{"筛选类型": "维度","筛选字段": "房型","操作符": "=", "是否需要分组":"是","筛选值": ["大床房","双床房"]}}]], "占比维度":[], "排序方式":"", "limit":""}}]

### 示例2： 分析：意图是对比，平均房价是指标，2024年4月是查询时间，2024年5月是对比时间，由于时间粒度只能取日、周、月、季、年，所以时间粒度是月
用户输入：2024年4月的平均房价较5月上升还是下降？
你的输出：[{{"意图": "对比", "指标": "平均房价", "时间范围": [{{"查询时间":"2024年4月","时间粒度":"月","同环比或对比时间":["2024年5月"]}}], "时间聚合粒度":"", "筛选":[], "占比维度":[], "排序方式":"", "limit":""}}]

### 示例3：分析：意图是数值，指标是客房营收，查询时间取query的原始文本为“2024年5月1日至2024年5月10日”，房型和客源是属于客房营收这个指标的分析维度，所以筛选类型是维度，筛选字段是房型和客源，筛选值是所有的房型和客源，需要分组
用户输入：2024年5月1日至2024年5月10日的不同房型、不同客源的客房营收分别是多少
你的输出：[{{"意图": "数值", "指标": "客房营收", "时间范围": [{{"查询时间":"2024年5月1日至2024年5月10日","时间粒度":"日","同环比或对比时间":[]}}], "时间聚合粒度":"", "筛选":[[{{"筛选类型": "维度","筛选字段": "房型","操作符": "=", "是否需要分组":"是","筛选值": ["ALL"]}}],[{{"筛选类型": "维度","筛选字段": "客源","操作符": "=", "是否需要分组":"是","筛选值": ["ALL"]}}]], "占比维度":[], "排序方式":"", "limit":""}}]

### 示例4：分析：意图是趋势，指标是总营收和平均房价，查询时间取query的原始文本为“2024年5月1日~2024年5月20日”，时间聚合粒度是日，需要分别查询总营收和平均房价，所以需要两条查询
用户输入：2024年5月1日~2024年5月20日每天营收、平均房价的情况
你的输出：[{{"意图": "趋势", "指标": "总营收", "时间范围": [{{"查询时间":"2024年5月1日~2024年5月20日","时间粒度":"日","同环比或对比时间":[]}}], "时间聚合粒度":"日", "筛选":[], "占比维度":"", "排序方式":"", "limit":""}},{{"意图": "趋势", "指标": "平均房价", "时间范围": [["2024年5月1日~2024年5月20日","日"]], "时间聚合粒度":"天", "筛选":[[]], "占比维度":[], "排序方式":"", "limit":""}}]

### 示例5：分析：意图是数值，指标是客房营收，查询时间取query的原始文本“去年”，由于省份、房型和门店都是客房营收的分析维度，所以筛选类型为维度，由于门店这个维度是最后需要查询的，所以门店的筛选值为“EMPTY”这个值，房型需要分组，需要按照客房营收降序排序，取客房营收前三的门店
用户输入：去年安徽省不同房型客房营收最高的三个门店
你的输出：[{{"意图": "数值", "指标": "客房营收", "时间范围": [{{"查询时间":"去年","时间粒度":"年","同环比或对比时间":[]}}], "时间聚合粒度":"", "筛选":[[{{"筛选类型": "维度","筛选字段": "省份","操作符": "=", "是否需要分组":"否","筛选值": ["安徽省"]}},{{"筛选类型": "维度","筛选字段": "房型","操作符": "=", "是否需要分组":"是","筛选值": ["ALL"]}},{{"筛选类型": "维度","筛选字段": "门店","操作符": "=", "是否需要分组":"否","筛选值": ["EMPTY"]}}]], "占比维度":[], "排序方式":"降序", "limit":"3"}}]

### 示例6：分析：意图是数值，指标是总营收，查询时间是去年，由于房型是总营收的分析维度，所以筛选类型为维度，由于房型是最后需要查询的，所以筛选值为“EMPTY”这个值，需要按照总营收降序排序，取总营收最大的房型
用户输入：去年收入最高的房型？
你的输出：[{{"意图": "数值", "指标": "总营收", "时间范围": [{{"查询时间":"去年","时间粒度":"年","同环比或对比时间":[]}}], "时间聚合粒度":"", "筛选":[[{{"筛选类型": "维度","筛选字段": "房型","操作符": "=", "是否需要分组":"否","筛选值": ["EMPTY"]}}]], "占比维度":[], "排序方式":"降序", "limit":"1"}}]

### 示例7：分析：意图是占比，指标是客房营收，时间是过去4周，由于该query涉及到了多个筛选维度，所以占比维度取query中最后一个维度“房型”
用户输入：过去4周北京市望京店大床房客房营收占比情况？
你的输出：[{{"意图": "占比", "指标": "客房营收", "时间范围": [{{"查询时间":"过去4周","时间粒度":"周","同环比或对比时间":[]}}], "时间聚合粒度":"", "筛选":[[{{"筛选类型": "维度","筛选字段": "省份","操作符": "=", "是否需要分组":"否","筛选值": ["北京市"]}},{{"筛选类型": "维度","筛选字段": "门店","操作符": "=", "是否需要分组":"否","筛选值": ["望京店"]}},{{"筛选类型": "维度","筛选字段": "房型","操作符": "=", "是否需要分组":"否","筛选值": ["大床房"]}}]], "占比维度":["房型"], "排序方式":"", "limit":""}}]

### 示例8：分析：意图是数值，指标是客房营收，时间是上个季度，由于房型是客房营收的分析维度，所以筛选类型为维度，由于query最后需要查询某个房型，所以筛选值为“EMPTY”这个值。需要筛选客房营收大于100万，所以筛选字段为客房营收，筛选类型为指标
用户输入：上个季度客房营收超过100万的房型
你的输出：[{{"意图": "数值", "指标": "客房营收", "时间范围": [{{"查询时间":"上个季度","时间粒度":"季","同环比或对比时间":[]}}], "时间聚合粒度":"", "筛选":[[{{"筛选类型": "指标","筛选字段": "客房营收","操作符": ">", "是否需要分组":"否","筛选值": ["100万"]}},{{"筛选类型": "维度","筛选字段": "房型","操作符": "=", "是否需要分组":"否","筛选值": ["EMPTY"]}}]], "占比维度":[], "排序方式":"", "limit":""}}]

### 示例9：分析：用户输入的为无效问题，无法提取出意图，指标和分析维度
用户输入：◐
你的输出：[{{"意图": "", "指标": "", "时间范围": [], "时间聚合粒度":"", "筛选":[], "占比维度":[], "排序方式":"", "limit":""}}]

### 示例10：分析：意图是数值，指标是总营收，时间是2024年，由于羊毛党是总营收的分析标签，所以筛选类型为标签，由于query为非羊毛党，所以筛选值为“否”这个值
用户输入：2024年非羊毛党总营收是多少？
你的输出：[{{"意图": "数值", "指标": "总营收", "时间范围": [{{"查询时间":"2024年","时间粒度":"年","同环比或对比时间":[]}}], "时间聚合粒度":"年", "筛选":[[{{"筛选类型": "标签","筛选字段": "羊毛党","操作符": "!=", "是否需要分组":"否","筛选值": ["否"]}}]], "占比维度":[], "排序方式":"", "limit":""}}]

##用户的输入query如下：
{query}
请给出输出结果：
"""
    # 读取 Excel 文件
    pf = pd.read_excel(path)

    # 定义模型和对应的函数
    models = [
        # ("GPT4O", get_response_gpt4o),
        # ("YiLightning", get_response_yi_lightning),
        # ("DeepSeek", get_response_deepseek),
        # ("Qwen25", get_response_qwen25),
        ("Qwen_max", get_response_qwen25)

    ]

    # 遍历每个模型
    for model_name, model_func in models:
        results = []
        run_times = []

        # 遍历每个查询
        for index, query in enumerate(pf["query"]):
            # time.sleep(10)
            start_time = time.time()
            try:
                # 格式化查询提示
                formatted_prompt = query_prompt.format(query=query)
                # print(formatted_prompt)
                response = model_func(formatted_prompt)
                # 清理响应数据
                if is_valid_utf8(response):
                    results.append(response)
                else:
                    results.append("Invalid UTF-8 response")
                print(f"Model {model_name}: 正在处理数据 {index + 1}")
            except Exception as e:
                results.append(f"Error: ")
                print(f"Model {model_name}: 发生错误 - {e}")
            finally:
                # 记录运行时间
                run_times.append(time.time() - start_time)

        # 将结果添加到 DataFrame 中
        pf[f"{model_name}_四要素抽取结果"] = results
        pf[f"{model_name}_运行时间"] = run_times

        # 将结果写入 Excel 的新 sheet，若已存在则替换
        with pd.ExcelWriter(path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
            pf.to_excel(writer, sheet_name=model_name, index=False)
        print(f"Model {model_name}: 执行完成")


if __name__ == '__main__':
    # extract_element(r"F:\code\jytest\decisionAI\data\测试集\Decision AI效果评测_四要素抽取_复合指标_GPT-4o.xlsx")

    # extract_element(r"C:\Users\<USER>\Downloads\Decision AI效果评测_四要素抽取_标签_GPT-4o (4).xlsx")
    extract_element(r"F:\code\jytest\decisionAI\data\测试集\Decision AI效果评测_四要素抽取_复合指标_GPT-4o.xlsx")
    extract_element(r"F:\code\jytest\decisionAI\data\测试集\Decision AI效果评测_四要素抽取_标签_GPT-4o (2).xlsx")


