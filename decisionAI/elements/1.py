import json

import pandas as pd
import re

# 读取Excel文件
pf = pd.read_excel(r"C:\Users\<USER>\Downloads\Decision AI效果评测_原子指标测试集_100 (1).xlsx")

# 进行json格式化处理


# # 对表格里面得实际四要素也进行格式化
# try:
#     # 尝试将实际四要素转换为json格式
#     pf["实际四要素"] = pf["实际四要素"].apply(lambda x: json.dumps(json.loads(x), ensure_ascii=False) if isinstance(x, str) else x)
# except json.JSONDecodeError:
#     # 如果实际四要素无法被json解析
#     print("实际四要素无法被json解析")
#     # 处理无法解析的情况，例如设置为默认值或跳过
#     pf["实际四要素"] = pf["实际四要素"].apply(lambda x: "{}" if isinstance(x, str) and x.strip() else x)
#
# # 对比预期四要素和实际四要素now，如果一致就在对比结果里面写入一致，否则写入不一致
# pf["对比结果"] = pf.apply(lambda row: "一致" if row["预期四要素"] == row["实际四要素"] else "不一致", axis=1)
#
# # 将处理后的数据写回到Excel文件
# pf.to_excel(r"F:\code\jytest\decisionAI\data\数据结果\11-15\数据问答_结果10_29.xlsx", index=False)
# a = []
# row = pf["实际结果"]
# for i in row:
#     row = pf["实际结果"]
# for i in row:
#     # print(i[0:8])
#     if isinstance(i, str):
#         if i[0:8] == "markdown":
#             answer = i[8:]
#             print(answer)
#         else:
#             answer = i
#         answer = answer.replace("\\n", "\n")
#         answer = re.sub(r'[\\*]', '', answer)
#         # print(answer)
#     else:
#         answer = ""
#     a.append(answer)
pf["格式化实际结果"] = a
pf.to_excel(r"C:\Users\<USER>\Downloads\Decision AI效果评测_洞察分析_DeepSeek-V3_0106.xlsx", index=False)
