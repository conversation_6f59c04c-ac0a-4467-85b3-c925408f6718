from dev_load_zhibiao import *


def run_command(request_nums):
    """
        run_number: 运行次数
        concurrency: django 并发数
        request_nums: 请求次数并发数
        type_w: 1 原子指标 2 混合指标 3 标签 4 混合场景
        """
    path = ''
    # 批量执行的并发数
    c = ['1', '5', '10', '15']
    # 1 原子指标 2 混合指标 3 标签 4 混合场景
    type_ws = [1, 2, 3, 4]
    for type_w in type_ws:
        for i in c:
            path = run_single_scene(run_number=type_w, thread=i,
                                    loop=request_nums, s_type='primary', type_w=type_w)
        calc_data(path)

    pass


if __name__ == "__main__":
    run_command(5)
    # path = run_single_scene(run_number=1, thread=1,
    #                         loop=request_nums, s_type='primary', type_w=1)
    # calc_data(path)
