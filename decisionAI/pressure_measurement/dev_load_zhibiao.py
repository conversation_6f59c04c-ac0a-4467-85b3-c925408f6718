# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : load_test.py
<AUTHOR> <EMAIL>
@Time   : 2023-12-06 10:29:16
@Version: v1.0
"""
import threading
import copy
import traceback
from test_data_zhibiao import user_chat
from concurrent.futures.thread import Thread<PERSON>ool<PERSON>xecutor
import uuid
import pandas as pd
from datetime import datetime
import concurrent.futures
import time
import json
import requests
import os
import sys
import argparse

BASE_DIR = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
print(BASE_DIR)
sys.path.append(BASE_DIR)

lock = threading.Lock()
send_url = "https://open.feishu.cn/open-apis/bot/v2/hook/bf9c14c9-5d6c-4094-9547-a957dabbcefb"


def get_exe(max_works=1):
    exe = ThreadPoolExecutor(max_workers=max_works)
    return exe


def get_now(format=None):
    if format:
        return datetime.now().strftime("%Y%m%d")
    else:
        return datetime.now().strftime("%Y%m%d%H%M%S")


user_chat_data = {
    "question": "今年上半年北京市通州区皮带商品的负债率是多少？",
    "session_id": ""
}

single_task_list = {
    #   原子
    "primary": {
        "service": "user_chat",
        "path": "/api/v1/chat/user_chat"
    },
    #   复合
    "composite": {
        "service": "user_chat",
        "path": "/api/v1/chat/user_chat"
    }
}
mixed_task_list = {
    "user_chat": {
        "service": "mixed_user_chat",
        "path": "/api/v1/chat/user_chat"
    }

}


def send_stream(service, data, path):
    """

    :param service:
    :param data:
    :param path:
    :return:
    """
    url = "https://decisionai-api.cmcm.com" + path
    headers = {"content-Type": "application/json",
               "token": "fd6824002f5134b15b757f83c0a2c80e"}
    # 记录请求发送的时间（精确到毫秒）
    request_send_time = time.time()
    response = requests.post(url, headers=headers, json=data, stream=True)
    first_packet_time = None
    first_packet_time_2 = None
    total_character_count = 0
    answer = ""
    intent_list = []
    # 处理响应数据
    # error = 0
    msg = ""
    content = ""

    if (service == "user_chat"):
        package_list = []
        for line in response.iter_lines():
            package_list.append(line)
            if not first_packet_time:
                # 记录第一个数据包返回的时间（精确到毫秒）
                first_packet_time = time.time()
            # 处理响应数据
            if line.startswith(b"data: "):
                json_data = line[6:]
                try:
                    data_dict = json.loads(json_data)
                except json.JSONDecodeError:
                    print("json loads error:->{}".format(line))
                    continue
                try:
                    return_type = data_dict.get(
                        "data", {}).get("return_type", "")
                    return_data = data_dict.get("data", {}).get(
                        "return_data", {})
                except BaseException:
                    return_type = ""
                    return_data = {}
                    print("获取return_type或者return_data报错")
                    print(f"{data_dict}")
                    print(traceback.format_exc())
                if 'insight' == return_type:
                    content = return_data.get("text")
                    #   计算第一个有效数据返回的时间
                    if content and not first_packet_time_2:
                        first_packet_time_2 = time.time()
                    content_length = len(content)
                    total_character_count += content_length
                    answer += content
                elif return_type == "rag":
                    return_data = data_dict.get("data", {}).get(
                        "return_data", {}).get(return_type, {})
                    content = return_data.get("answer_text")
                    if content and not first_packet_time_2:
                        first_packet_time_2 = time.time()
                    # print(content)
                    content_length = len(content)
                    total_character_count += content_length
                    answer += content
                elif return_type == "tips":
                    return_data = data_dict.get("data", {}).get(
                        "return_data", {}).get(return_type, {})
                    content = return_data
                    if content and not first_packet_time_2:
                        first_packet_time_2 = time.time()
                    content_length = len(content)
                    total_character_count += content_length
                    answer += content
                elif return_type == "progress":
                    intent_list.append(data_dict.get("data", {}).get(
                        "return_data", {}).get(return_type))
                elif data_dict.get("code") != 0:
                    msg = data_dict.get("msg")

            # else:
            #     print(f"{get_time()}数据不是以data开头{line}")
        if not answer:
            msg = package_list
            if not first_packet_time_2:
                first_packet_time_2 = time.time()
            print(f"{get_now()},{service} 没有获取到数据\n{package_list}")

    # 等待整个请求完全返回
    response.close()
    # 记录请求完成的时间（精确到毫秒）
    request_complete_time = time.time()
    # 转换为易读的时分秒格式（精确到毫秒）并保留毫秒的前3位
    request_send_time_str = datetime.fromtimestamp(request_send_time).strftime(
        '%Y-%m-%d %H:%M:%S.%f')[:23]

    first_packet_time_str = datetime.fromtimestamp(first_packet_time).strftime(
        '%Y-%m-%d %H:%M:%S.%f')[:23]
    request_complete_time_str = datetime.fromtimestamp(
        request_complete_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    if first_packet_time_2:
        first_packet_time_str_2 = datetime.fromtimestamp(
            first_packet_time_2).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
        first_packet_duration_2 = (first_packet_time_2 - request_send_time)
        tokens_time_2 = (request_complete_time - first_packet_time_2)
        performance_metric_2 = (total_character_count) / tokens_time_2
    else:
        performance_metric_2 = 0
        first_packet_duration_2 = 0
        first_packet_time_str_2 = ""

    # 计算并打印首包耗时和总耗时
    first_packet_duration = (first_packet_time - request_send_time)

    total_duration = request_complete_time - request_send_time
    tokens_time = (request_complete_time - first_packet_time)
    # 计算性能指标
    performance_metric = (total_character_count) / tokens_time
    # print("性能指标: {:.3f} 字/秒".format(performance_metric))
    # print("=" * 50)
    result = {
        "请求发送的时间": request_send_time_str,
        "第一个数据包返回的时间": first_packet_time_str,
        "第一个有效数据包返回的时间": first_packet_time_str_2,
        "请求完成的时间": request_complete_time_str,
        "首包耗时(s)": first_packet_duration,
        "有效数据_首包耗时(s)": first_packet_duration_2,
        "总耗时(s)": total_duration,
        # "输入": len(qa_prompt),
        "输出字数": total_character_count,
        "性能指标(字/秒)": performance_metric,
        "有效数字_性能指标(字/秒)": performance_metric_2,
        "answer": answer,
        "query": data.get("question", ""),
        # "request_id": data["request_id"],
        "msg": msg,
        "progress": json.dumps(intent_list, ensure_ascii=False),
        "session_id": data.get("session_id", "")
    }
    return result


def save_print_to_excel(thread, loop, service, path, run_number, action, type_w):
    results = []
    output_path = "{T}{sep}{run_number}{sep}{action}{sep}{service}".format(
        sep=os.sep,
        T=get_now("day"),
        action=action,
        service=service,
        run_number=run_number
    )
    output_file = f"{thread}-{loop}-{get_now()}-{service}.xlsx"

    excel_path = f"{output_path}{os.sep}{output_file}"
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    with concurrent.futures.ThreadPoolExecutor(max_workers=thread) as executor:
        futures = []
        index = 0

        for index in range(loop):
            data = None
            if service == "user_chat":
                chat_data = copy.deepcopy(user_chat_data)
                chat_data["question"] = user_chat[type_w-1][index % len(user_chat[type_w-1])]
                chat_data["session_id"] = str(uuid.uuid4())
                data = chat_data

            futures.append(executor.submit(send_stream, service, data, path))
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as exc:
                print(f"Error: {exc}\n{traceback.format_exc()}")
    df = pd.DataFrame(results)
    df.to_excel(excel_path, index=False)
    print(f"输出报告路径: {output_path}/{output_file}")
    return f'{output_path}'


def get_all_files_in_dir(file_path: str) -> list:
    """
    递归获取指定文件夹及其子文件夹下的所有文件，不包含隐藏文件
    :param file_path: 文件夹路径
    :return: 文件名列表
    """
    filename_list = []
    mixed_file_list = []
    for root, dirs, files in os.walk(file_path):
        for file_name in files:
            if not file_name.startswith("."):
                if root.__contains__("mixed"):
                    mixed_file_list.append(os.path.join(root, file_name))
                elif not file_name.__contains__("all"):
                    filename_list.append(os.path.join(root, file_name))
    return filename_list, mixed_file_list


def get_info_file(file_name):
    """
    根据文件名获取文件信息
    """
    info = file_name.split("/")[-1].split("-")
    info[3] = info[3].split(".")[0]
    return info


def calc_data(file_path):
    if os.path.exists(file_path):
        #    遍历获取文件夹下所有文件路径
        single_files, mixed_files = get_all_files_in_dir(file_path)
    else:
        raise FileNotFoundError(f'{file_path} does not exist')
    # 读取 Excel 文件并创建 DataFrame
    # 先处理单场景文件
    single_res_list = []
    mixed_res_list = []
    for sf in single_files:
        df = pd.read_excel(sf)
        res = calc_df(df)
        info = get_info_file(sf)
        api_info = ""
        if info[3] == "user_chat":
            api_info = "/api/v1/chat/user_chat"
        else:
            api_info = "/api/v1/chat/user_chat"
        add_data = {
            "接口": api_info,
            "since": info[3],
            "并发数": info[0],
            "执行次数": info[1],
        }
        res.update(add_data)
        single_res_list.append(res)
    if single_res_list:
        df_single = pd.DataFrame(single_res_list)
        # 将单场景数据写入 Excel
        df_single.to_excel(f"{file_path}/single_all_result.xlsx", index=False)
        keep_cols = ["since", "并发数", "执行次数", "首包90Line", "首包95line",
                     "总耗时90line", "总耗时95line"]
        df = df_single[keep_cols]
        pd.set_option('display.max_columns', None)  # 显示所有列
        print(f"单场景压测结果:\n{df}\n")
    for mf in mixed_files:
        df = pd.read_excel(mf)
        res = calc_df(df)
        info = get_info_file(mf)
        api_info = ""
        if info[3] == "user_chat":
            api_info = "/api/v1/chat/user_chat"
        else:
            api_info = "/api/v1/chat/user_chat"
        add_data = {
            "接口": api_info,
            "since": info[3],
            "并发数": info[0],
            "执行次数": info[1],
        }
        res.update(add_data)
        mixed_res_list.append(res)
    if mixed_res_list:
        df_mixed = pd.DataFrame(mixed_res_list)
        # 将混合场景数据写入 Excel
        df_mixed.to_excel(f"{file_path}/mixed_all_result.xlsx", index=False)
        keep_cols = ["since", "并发数", "执行次数", "首包90Line", "首包95line",
                     "总耗时90line", "总耗时95line"]
        df = df_mixed[keep_cols]
        pd.set_option('display.max_columns', None)  # 显示所有列
        print(f"混合场景压测结果:\n{df}\n")


def calc_df(df):
    try:
        # 将字符串时间列转换为 datetime 类型
        start = pd.to_datetime(df["请求发送的时间"].min())
        end = pd.to_datetime(df["请求完成的时间"].max())
        cost = round((end - start).total_seconds(), 1)
        #   获取执行开始时间
        begin_time = df["请求发送的时间"].min()
        #   获取执行结束时间
        end_time = df["请求完成的时间"].max()
        #   计算执行总耗时
        # all_cost=end_time-begin_time
        # print(f"start:{start},end:{end},cost:{cost}\nbegin_time:{begin_time},end_time:{end_time}")
        # 首包耗时
        # first_cost = round(df["首包耗时(s)"].mean(), 2)
        #   有效首包耗时
        # real_first_cost = round(df["有效数据_首包耗时(s)"].mean(), 2)

        #   有效首包耗时最大值
        real_first_max = round(df["有效数据_首包耗时(s)"].max(), 2)

        #   有效首包耗时最小值
        # real_first_min = round(df["有效数据_首包耗时(s)"].min(), 2)
        #   去除为0的数据
        real_first_min = round(
            df.loc[df["有效数据_首包耗时(s)"] != 0, "有效数据_首包耗时(s)"].min(), 2)

        #   有效首包耗时中位数
        real_first_median = round(df["有效数据_首包耗时(s)"].median(), 2)

        #   总耗时平均值
        # total_cost = round(df["总耗时(s)"].mean(), 2)

        #   总耗时最大值
        total_cost_max = round(df["总耗时(s)"].max(), 2)

        #   总耗时最小值
        total_cost_min = round(df.loc[df["总耗时(s)"] != 0, "总耗时(s)"].min(), 2)

        #   总耗时中位数
        total_cost_median = round(df["总耗时(s)"].median(), 2)
        #   性能指标字/秒
        speed_word = round(df["性能指标(字/秒)"].mean(), 2)
        #   有效数据性能指标字/秒
        real_speed_word = round(df["有效数字_性能指标(字/秒)"].mean(), 2)
        #   首包耗时计算
        orion_first_cost = sorted(df["有效数据_首包耗时(s)"].to_list())
        first_line_90 = int(len(orion_first_cost) * 0.90)
        first_line_95 = int(len(orion_first_cost) * 0.95)
        first_line_99 = int(len(orion_first_cost) * 0.99)

        first_cost_90 = round(orion_first_cost[first_line_90], 2)
        first_cost_99 = round(orion_first_cost[first_line_99], 2)
        first_cost_95 = round(orion_first_cost[first_line_95], 2)
        #   总耗时计算
        orion_total_cost = sorted(df["总耗时(s)"].to_list())
        total_line_90 = int(len(orion_total_cost) * 0.90)
        total_line_95 = int(len(orion_total_cost) * 0.95)
        total_line_99 = int(len(orion_total_cost) * 0.99)
        total_cost_90 = round(orion_total_cost[total_line_90], 2)
        total_cost_99 = round(orion_total_cost[total_line_99], 2)
        total_cost_95 = round(orion_total_cost[total_line_95], 2)

        average_row_to_all = {
            "接口": "",
            "since": "",
            "并发数": 0,
            "执行次数": "",
            "总耗时": cost,
            "开始时间": begin_time,
            "结束时间": end_time,
            "首包99line": first_cost_99,
            "首包95line": first_cost_95,
            "首包90Line": first_cost_90,
            "首包中位数": real_first_median,
            "首包最大值": real_first_max,
            "首包最小值": real_first_min,
            "总耗时99line": total_cost_99,
            "总耗时95line": total_cost_95,
            "总耗时90line": total_cost_90,
            "总耗时中位数": total_cost_median,
            "总耗时最大值": total_cost_max,
            "总耗时最小值": total_cost_min,
            "性能指标(字/秒)": speed_word,
            "有效数字_性能指标(字/秒)": real_speed_word
        }
        return average_row_to_all
    except Exception as e:
        print(f"统计数据失败，原因：{e}")


def run_single_scene(run_number, thread, loop, s_type, type_w):
    task = single_task_list.get(s_type)
    thread = int(thread)
    loop = int(loop)
    service = task.get("service")
    path = task.get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    path_file = save_print_to_excel(
        thread, loop, service, path, run_number, "single", type_w)
    print(f"单场景{service}执行结束")
    return path_file


def run_mixed_scene(run_number, thread, loop, s_type):
    task = mixed_task_list.get(s_type)
    thread = int(thread)
    loop = int(loop)
    service = task.get("service")
    path = task.get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    save_print_to_excel(
        thread, loop, service, path, run_number, "mixed")
    print(f"混合场景{service}执行结束")


if __name__ == "__main__":

    # run_single_scene(run_number=1, thread=1,
    #                  loop=1, s_type="primary")
    #   单场景压测时,设置为single,多场景混合压测时,设置为mixed
    action = "single"
    #   压测结束后,获取请求对应的log信息,通过数据库查询,如无法连数据库,设置false
    query_log_info = False
    #   根据环境动态取数据库,同时,只能在dev环境发送消息
    env = "dev"
    #   新增控制发送飞书消息开关,不依赖环境
    is_send_msg = False

    parser = argparse.ArgumentParser(description="处理命令行参数")
    # 单场景压测时,设置为single,多场景混合压测时,设置为mixed
    parser.add_argument("--action", type=str, required=False, help="执行方法")
    # 根据环境动态取数据库,默认dev环境
    parser.add_argument("--env", type=str, required=False, help="执行环境")
    # 压测并发数
    parser.add_argument("--c", type=int, required=False, help="并发数")
    # 压测执行次数
    parser.add_argument("--n", type=int, required=False, help="执行次数")
    # 压测接口
    parser.add_argument("--i", type=str, required=False,
                        help="混合压测接口,可选值ai_chat/simulate_chat/course_qa,\
                            单场景压测接口,可选值primary_indicator/no_primary_indicator/\
                                rag/dood_primary_indicator/dood_no_primary_indicator/\
                                    dood_rag/simulate_chat/course_qa")
    # 当天第几次执行
    parser.add_argument("--run_number", type=int,
                        required=False, help="当天第几次执行")

    # 解析参数
    args = parser.parse_args()
    if args.env is not None:
        env = args.env
    else:
        env = "dev"

    if args.action is not None:
        action = args.action
    else:
        action = "single"
    if args.c is not None:
        concurrency = args.c
    else:
        concurrency = 1
    if args.n is not None:
        request_nums = args.n
    else:
        request_nums = 1
    if args.i is not None:
        s_type = args.i
    else:
        s_type = ""
    if args.run_number is not None:
        run_number = args.run_number
    else:
        run_number = 1

    res_path = f"./{get_now('day')}{os.sep}{run_number}"
    if not os.path.exists(res_path):
        os.makedirs(res_path)

    start_time = datetime.now()
    start_time_str = start_time.strftime("%Y-%m-%d:%H-%M-%S")
    if action == "single":
        # 单场景压测
        if s_type in ("primary_indicator", "no_primary_indicator",
                      "rag", "good_primary_indicator",
                      "good_no_primary_indicator",
                      "good_rag", "simulate_chat", "course_qa", "primary"):
            run_single_scene(run_number=run_number, thread=concurrency,
                             loop=request_nums, s_type=s_type)
        else:
            raise Exception("请输入正确的接口服务")

    elif action == "mixed":
        # 混合场景压测
        if s_type in ("ai_chat", "simulate_chat", "course_qa"):
            run_mixed_scene(run_number=run_number, thread=concurrency,
                            loop=request_nums, s_type=s_type)
        else:
            raise Exception("请输入正确的接口服务")
    elif action == "calc":
        #   计算结果
        calc_data(res_path)
    elif action == "send":
        from tool.util import send_card_message

        #   测试群
        load_msg = "压测结束！\n"
        title = "压测结果通知"
        send_card_message(send_url=send_url, load_msg=load_msg, title=title)
    else:
        raise Exception("请输入正确的操作类型")
