import pandas as pd
import re


def extract_time(log, pattern):
    """
    从日志中提取时间信息。

    参数:
        log (str): 要搜索的日志。
        pattern (re.Pattern): 用于搜索时间信息的正则表达式模式。

    返回:
        str: 提取到的时间信息，如果未找到则返回 None。
    """
    time_match = re.search(pattern, log)
    return time_match.group(1) if time_match else None


def process_log(log, time_patterns):
    """
    处理单个日志，提取各种时间信息。

    参数:
        log (str): 要处理的日志。
        time_patterns (dict): 包含各种时间信息正则表达式模式的字典。

    返回:
        dict: 包含提取到的各种时间信息的字典。
    """
    log_results = {}
    for time_type, pattern in time_patterns.items():
        log_results[time_type] = extract_time(log, pattern)
    return log_results


def get_log_time(output_file_path):
    """
    从指定的 Excel 文件中读取日志数据，并提取每条日志中的时间信息。

    参数:
        output_file_path (str): 要读取的 Excel 文件的路径。

    返回:
        None
    """
    time_patterns = {
        "LLM 四要素抽取时间": re.compile(
            r"====LLM四要素抽取时间====\\n时间为：\s*(.*?)\s*\\n====", re.DOTALL),
        "sql 执行时间": re.compile(r"====sql执行时间====\\n时间为：\s*(.*?)\s*\\n====", re.DOTALL),
        "code 执行时间": re.compile(r"====code执行时间====\\n时间为：\s*(.*?)\s*\\n====", re.DOTALL),
        "chart 执行时间": re.compile(r"====chart执行时间====\\n时间为：\s*(.*?)\s*\\n====",re.DOTALL)
    }

    # 读取 Excel 文件
    df = pd.read_excel(output_file_path)

    # 初始化结果列表
    llm_times = []
    sql_times = []
    code_times = []
    chart_times = []

    # 处理每条日志
    for log in df['log']:
        log_results = process_log(log, time_patterns)
        llm_times.append(log_results["LLM 四要素抽取时间"])
        sql_times.append(log_results["sql 执行时间"])
        code_times.append(log_results["code 执行时间"])
        chart_times.append(log_results["chart 执行时间"])

    # 将结果添加到 DataFrame
    df["LLM 四要素抽取时间"] = llm_times
    df["sql 执行时间"] = sql_times
    df["code 执行时间"] = code_times
    df["chart 执行时间"] = chart_times

    # 将结果保存回 Excel 文件
    df.to_excel(output_file_path, index=False)


if __name__ == '__main__':
    # 调用 get_log_time 函数，传入要处理的 Excel 文件路径
    get_log_time(r"F:\code\jytest\decisionAI\data\数据结果\12-19\数据问答_结果10_24.xlsx")
