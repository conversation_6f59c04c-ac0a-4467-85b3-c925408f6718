import json
import os
from datetime import datetime
from model_llm import get_response_gpt4o
import pandas as pd


def resp_answers_gpt(path):
    cwd_path = os.getcwd()
    fail_path = os.path.join(cwd_path, "data", "prompt", "洞察分析prompt.txt")
    print(fail_path)
    with open(fail_path, 'r', encoding='utf-8') as file:
        prompt = file.read()
        print(prompt)
    pf = pd.read_excel(path)
    print(pf)
    score = []
    desc = []
    prompt1 = []
    for index, row in pf.iterrows():
        try:
            print(f"第{index + 1}条数据")
            compare_answer = row["期望结果"]
            answer = row["实际结果"]
            p_prompt = prompt.format(compare_answer, answer)
            # print("prompt是：")
            prompt1.append(p_prompt)
            text = get_response_gpt4o(p_prompt)
            print(text)
            if text[0:7] == "```json":
                res_json = json.loads(text[7:-3])
            else:
                res_json = json.loads(text)
            score.append(res_json["分数"])
            if "原因" in res_json:
                desc.append(res_json["原因"])
            else:
                desc.append("")
        except Exception as e:
            score.append("")
            desc.append("")
            print(f"第{index + 1}条报错了 : {e}")
    pf["prompt"] = prompt1
    pf["评分"] = score
    pf["评分理由"] = desc
    now = datetime.now()
    current_dir = os.getcwd()
    raw_path = os.path.join(current_dir, 'data', '评测结果')
    output_dir = os.path.join(raw_path, now.strftime('%m-%d'))
    os.makedirs(output_dir, exist_ok=True)  # 确保目录存在
    output_file_path = os.path.join(output_dir, f'洞察分析_测评结果{now.strftime("%H_%M")}.xlsx')
    pf.to_excel(output_file_path, index=False)
    print(f"结果已保存到 '{output_file_path}'")
    return
    # pf.to_excel(path, index=False)
    # print("数据写入完成")


if __name__ == '__main__':
    path = r"F:\code\jytest\decisionAI\data\数据结果\12-16\数据问答_结果10_20.xlsx"
    resp_answers_gpt(path)
