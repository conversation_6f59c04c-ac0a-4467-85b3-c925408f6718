import json
from datetime import datetime
import re

import pandas as pd
import requests
import os
import sys

from analysis_get_score import resp_answers_gpt


def get_token():
    url = "https://dev-decisionai-api.cmcm.com/api/v1/user/login"
    headers = {
        "Content-Type": "application/json"
    }
    data = {"account": "DecisionAI", "password": "5690dddfa28ae085d23518a035707282"}
    try:
        resp = requests.post(url=url, headers=headers, json=data)

        # 检查响应状态码
        if resp.status_code == 200:
            try:
                return resp.json()["data"]["token"]
            except (KeyError, TypeError) as e:
                print(f"Error extracting token: {e}")
                return None
        else:
            print(f"Error: {resp.status_code}")
            return None
    except requests.exceptions.JSONDecodeError as e:
        print(f"JSON decode error: {e}")
        return None


def get_answers(file_case_name):
    file_case_name = os.path.join('data', '测试集', file_case_name)
    current_dir = os.getcwd()
    file_path = os.path.join(current_dir, file_case_name)
    if not os.path.exists(file_path):
        print(f"路径下没有测试集 {file_path}")
        return
    pf = pd.read_excel(file_path)
    url = "https://dev-decisionai-api.cmcm.com/api/v1/report/config/attribution_analysis_insight"
    header = {
        "Content-Type": "application/json",
        "token": get_token()
    }

    print("开始跑数据")

    answers = []
    for index, data in enumerate(pf["请求参数"]):
        try:
            answer = ""
            print(f"正在处理第{index + 1}条数据")
            data = json.loads(data)
            # print(data)

            resp = requests.post(url=url, headers=header, json=data)
            print(resp.text)
            # 检查响应状态码
            # if resp.status_code != 200:
            #     print(f"请求失败，状态码: {resp.status_code}")
            #     continue
            #
            # # 检查响应内容是否为空
            # if not resp.content:
            #     print("响应为空")
            #     continue

            # 逐行解析响应
            for rew in resp.iter_lines():
                json1 = rew[6:]
                print(json1)
                if rew:
                    json1 = json.loads(json1.decode('utf-8'))
                    answer += json1["data"]["return_data"]["text"]
                    answer = answer.replace("\\n", "\n")
                    answer = re.sub(r'[\\*]', '', answer)
            answers.append(answer)
        except Exception as e:
            print(f"第{index + 1}条报错了 : {e}")
            answers.append("")

    pf["实际结果"] = answers
    now = datetime.now()
    raw_path = os.path.join(current_dir, 'data', '数据结果')
    output_dir = os.path.join(raw_path, now.strftime('%m-%d'))
    os.makedirs(output_dir, exist_ok=True)  # 确保目录存在
    output_file_path = os.path.join(output_dir, f'洞察分析_结果{now.strftime("%H_%M")}.xlsx')
    pf.to_excel(output_file_path, index=False)
    print(f"结果已保存到 '{output_file_path}'")
    return output_file_path


if __name__ == '__main__':
    # a = get_answers("Decision AI效果评测_洞察分析_gpt4o_V2.3_1223.xlsx")
    # resp_answers_gpt(a)
    if len(sys.argv) < 3:
        print("请提供表格名称和是否评测参数作为参数")
        sys.exit(1)

    file_case_name = sys.argv[1]
    gpt_flag_str = sys.argv[2].lower()

    if gpt_flag_str not in ['true', 'false']:
        print("'true' or 'false'")
        sys.exit(1)

    gpt_flag = gpt_flag_str == 'true'

    path = get_answers(file_case_name)

    if gpt_flag:
        resp_answers_gpt(path)
