import json

import pandas as pd
from model_llm import get_response_gpt4o

prompt = """## 任务
你是一个专业的结果审核助手，根据步骤和错误分类以及注意事项，仔细对比"实际结果"和"预期结果"，按照输出格式最终输出评测分数和原因




## 步骤

1. 首先检查"实际结果"是否是有效的JSON格式，如果不是，最终输出0分，并输出错误分类"非JSON格式"
2. 如果实际结果是JSON格式，继续检查实际结果中的每一处是否和预期结果中对应的内容相符，如果不相符，最终输出0分，并输出错误分类，如果相符，最终输出1分


## 错误分类

1. 如果"意图"不相符，原因输出"意图错误"
2. 如果“指标”不相符，原因输出指标错误"
3. 如果"查询时间"不相符，原因输出"查询时间错误"
4. 如果"时间粒度"不相符，原因输出"时间粒度错误"
5. 如果"同环比或对比时间"不相符，原因输出"同环比或对比时间错误"
6. 如果"时间聚合粒度"不相符，原因输出"时间聚合粒度错误"
7. 如果"筛选类型"不相符，原因输出"筛选类型错误"
8. 如果"筛选字段"不相符，原因输出"筛选字段错误"
9. 如果"操作符"不相符，原因输出"操作符错误"
10. 如果"是否需要分组"不相符，原因输出"分组错误"
11. 如果"筛选值"不相符，原因输出"筛选值错误"
12. 如果"占比维度"不相符，原因输出"占比维度错误"
13. 如果"排序方式"不相符，原因输出"排序方式错误"
14. 如果"limit"不相符，原因输出"limit错误"




 ## 注意事项
1. 必须严格按照检查步骤确保每一处都进行了检查
2. 格式严格按照输出格式输出，不要输出任何输出格式以外的内容，确保输出的格式可以被Python的json.loads方法解析
3. 你在对比结果时不能简单盲目的一一对比，如果相同的字段有多组，如果顺序不一致，但参数对应的参数值是相同的，也必须判定为正确的，比如下面筛选1和筛选2的结果是一致的

### 筛选1

"筛选": [
            [
                {{
                    "筛选类型": "标签",
                    "筛选字段": "羊毛党",
                    "操作符": "=",
                    "是否需要分组": "否",
                    "筛选值": ["是"]
                }},
                {{
                    "筛选类型": "维度",
                    "筛选字段": "商品",
                    "操作符": "=",
                    "是否需要分组": "否",
                    "筛选值": ["高跟鞋"]
                }}
            ]
        ]


 ### 筛选2   
 

 "筛选": [
            [
                {{
                    "筛选类型": "维度",
                    "筛选字段": "商品",
                    "操作符": "=",
                    "是否需要分组": "否",
                    "筛选值": ["高跟鞋"]
                }},
                {{
                    "筛选类型": "标签",
                    "筛选字段": "羊毛党",
                    "操作符": "=",
                    "是否需要分组": "否",
                    "筛选值": ["是"]
                }}
            ]
        ]        




## 预期结果

```
{data1}
```
## 实际结果

```
{data2}
```

## 输出格式 

{{   "分数": 0,
    "原因": ["",""]
    
}}


请根据以上格式严格按照输出格式输出，确保输出的格式可以被Python的json.loads方法解析"""


def answers_score(path, prompt):
    pf = pd.read_excel(path)
    for data1, data2 in zip(pf["预期四要素"], pf["实际四要素"]):
        formatted_prompt = prompt.format(data1=data1, data2=data2)
        resp = get_response_gpt4o(formatted_prompt)
        resp = resp[7:-3]
        print(resp)
        try:
            response_json = json.loads(resp)
            score = response_json["分数"]
            reason = response_json["原因"]
        except json.JSONDecodeError:
            score = 0
            reason = ["解析错误"]
        print(score, reason)
        # 确保 reason 是一个字符串，而不是列表
        pf.loc[pf["预期四要素"] == data1, "原因"] = ", ".join(reason)
        pf.loc[pf["预期四要素"] == data1, "分数"] = score
    pf.to_excel(path, index=False)


if __name__ == '__main__':
    answers_score(r"F:\code\jytest\decisionAI\data\测试集\数据问答_结果18_25.xlsx", prompt)
