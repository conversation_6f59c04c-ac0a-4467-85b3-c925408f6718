import json
import os
from datetime import datetime
import pymysql
import requests
import pandas as pd
import time
from get_log_time import get_log_time


def get_token():
    url = "https://dev-decisionai-api.cmcm.com/api/v1/user/login"
    headers = {
        "Content-Type": "application/json"
    }
    data = {"account": "wxj_test", "password": "5690dddfa28ae085d23518a035707282"}
    try:
        resp = requests.post(url=url, headers=headers, json=data)

        # 检查响应状态码
        if resp.status_code == 200:
            try:
                return resp.json()["data"]["token"]
            except (KeyError, TypeError) as e:
                print(f"Error extracting token: {e}")
                return None
        else:
            print(f"Error: {resp.status_code}")
            return None
    except requests.exceptions.JSONDecodeError as e:
        print(f"JSON decode error: {e}")
        return None


def mysql_log(sql):
    max_attempts = 3
    attempt = 0

    while attempt < max_attempts:
        try:
            # 连接到数据库
            conn = pymysql.connect(
                host="***********",
                user="root",
                password="root123321",
                database="decision_ai",
                charset='utf8mb4'  # 确保字符集正确
            )

            # 创建游标对象
            cursor = conn.cursor()

            # 执行查询
            cursor.execute(
                sql)

            # 获取结果
            result = cursor.fetchone()

            # 关闭游标和连接
            cursor.close()
            conn.close()

            return result
        except pymysql.MySQLError as err:
            print(f"Error: {err}")

        # 等待 20 秒后重试
        attempt += 1
        time.sleep(20)

    # 如果尝试三次仍然没有结果，返回空
    return None


def resp_test(query, token):
    data = {
        "question": query,
        "session_id": ""
    }
    base_URL = "https://dev-decisionai-api.cmcm.com/api/v1/chat/user_chat"
    headers = {
        "Content-Type": "application/json",
        "token": token
    }
    resp = requests.post(url=base_URL, headers=headers, json=data)
    answer = ""
    session_id = ""
    return_data = ""
    original_parse = ""
    report_data = None
    for rew in resp.iter_lines():
        print(rew)
        if rew.startswith(b"data: "):
            json1 = rew[6:]
            # print(json1)
            json2 = json.loads(json1.decode('utf-8'))
            # print(json2)
            if "return_data" in json2["data"] and "text" in json2["data"]["return_data"]:
                answer += json2["data"]["return_data"]["text"]
                # if json2["data"]["status"] == "end":
                #     break
            if json2["data"]["return_type"] == "report" and "growth_rate_analysis" in json2["data"]["return_data"]:
                report_data = json2["data"]["return_data"]
                break
            session_id = json2["data"]["session_id"]
            if "echart_data" in json2["data"]["return_data"]:
                # print(json2)
                return_data = json2["data"]["return_data"]
            if "original_parse" in json2["data"]["return_data"]:
                original_parse = json2["data"]["return_data"]["original_parse"]

    return answer, session_id, return_data, original_parse, report_data


def get_xlsx(fail_case_path):
    current_dir = os.getcwd()
    fail_case_path = os.path.join("data", "测试集", fail_case_path)
    fail_path = os.path.join(current_dir, fail_case_path)
    if not os.path.exists(fail_path):
        print(f"路径下没有测试集 {fail_path}")
        return
    return pd.read_excel(fail_path)


def process_query(query, token):
    try:
        answer, session_id, echart_data, original_parse, report_data = resp_test(query, token)
        # print(echart_data)
        log_mysql = mysql_log(f"SELECT trace_logs FROM trace_logs WHERE session_id='{session_id}' order by chat_id;")
        if log_mysql is None:
            print(f"Session ID {session_id} did not return any log data.")
        return answer, session_id, echart_data, log_mysql, original_parse, report_data
    except Exception as e:
        print(f"报错了: {e}")
        return None, None, None, None, None, None


def get_data_report(path):
    datas = []
    pf = pd.read_excel(path)
    for index, row in pf.iterrows():
        data = {"开始值": None, "结束值": None, "变化值": None, "变化率": None,
                "环比": None, "同比": None, "区间平均值": None, "区间最大值": None,
                "区间最小值": None}
        # print(f"第{index + 1}条数据")
        query_type = row["计算范式"]
        report_data = row["report_data"]
        try:
            report_data = json.loads(report_data)
            # print(report_data)
            data["同比"] = report_data["mom_yoy_analysis"]["yoy"]
            data["环比"] = report_data["mom_yoy_analysis"]["mom"]
            data["区间平均值"] = report_data["extreme_value_analysis"]["interval_average"]
            data["区间最大值"] = report_data["extreme_value_analysis"]["interval_max"]
            data["区间最小值"] = report_data["extreme_value_analysis"]["interval_min"]
            if query_type in ["环比", "同环比"]:
                data["开始值"] = report_data["growth_rate_analysis_mom"]["start_value"]
                data["结束值"] = report_data["growth_rate_analysis_mom"]["end_value"]
                data["变化值"] = report_data["growth_rate_analysis_mom"]["diff"]
                data["变化率"] = report_data["growth_rate_analysis_mom"]["growth_rate"]
            elif query_type == "同比":
                data["开始值"] = report_data["growth_rate_analysis_yoy"]["start_value"]
                data["结束值"] = report_data["growth_rate_analysis_yoy"]["end_value"]
                data["变化值"] = report_data["growth_rate_analysis_yoy"]["diff"]
                data["变化率"] = report_data["growth_rate_analysis_yoy"]["growth_rate"]
            else:
                data["开始值"] = report_data["growth_rate_analysis"]["start_value"]
                data["结束值"] = report_data["growth_rate_analysis"]["end_value"]
                data["变化值"] = report_data["growth_rate_analysis"]["diff"]
                data["变化率"] = report_data["growth_rate_analysis"]["growth_rate"]
        except Exception as e:
            print(f"报错了: {e}")
        data = json.dumps(data, ensure_ascii=False)
        datas.append(data)
    pf["分析报告参数"] = datas
    pf.to_excel(path, index=False)


def main(name):
    token = get_token()
    pf = get_xlsx(name)
    # print(pf)
    queries = pf["query"]
    answers = []
    session_ids = []
    logs = []
    echart_datas = []
    original_parses = []
    report_datas = []
    for index, query in enumerate(queries):
        print("=" * 100, "\n", f"第{index + 1}条数据")
        print(query)
        answer, session_id, echart_data, log_mysql, original_parse, report_data = process_query(query, token)
        # log_mysql = log_mysql[0].replace("\n", "\n")
        report_data = json.dumps(report_data, ensure_ascii=False)
        original_parse = json.dumps(original_parse, ensure_ascii=False, indent=4)
        echart_data = json.dumps(echart_data, ensure_ascii=False)

        if session_id is None or log_mysql is None:
            print(f"Failed to process query: {query}")
        answers.append(answer)
        session_ids.append(session_id)
        logs.append(log_mysql)
        echart_datas.append(echart_data)
        original_parses.append(original_parse)
        report_datas.append(report_data)

    pf["实际结果"] = answers
    pf["session_id"] = session_ids
    pf["log"] = logs
    pf["echart_data"] = echart_datas
    pf["实际四要素"] = original_parses
    pf["report_data"] = report_datas
    now = datetime.now()
    current_dir = os.getcwd()
    raw_path = os.path.join(current_dir, 'data', '数据结果')
    output_dir = os.path.join(raw_path, now.strftime('%m-%d'))
    os.makedirs(output_dir, exist_ok=True)  # 确保目录存在
    output_file_path = os.path.join(output_dir, f'数据问答_结果{now.strftime("%H_%M")}.xlsx')
    pf.to_excel(output_file_path, index=False)
    try:
        get_log_time(output_file_path)
        get_data_report(output_file_path)

    except Exception as e:
        print(f"抽取日志时间失败: {e}")
    print(f"结果已保存到 '{output_file_path}'")
    return output_file_path


if __name__ == '__main__':
    main("Decision AI效果评测_原子指标测试集_100 (4).xlsx")
    # main('Decision AI效果评测_复合指标测试集.xlsx')
    # main('Decision AI效果评测_标签测试集.xlsx')
    # get_data_report(r"F:\code\jytest\decisionAI\data\数据结果\01-13\数据问答_结果19_26.xlsx")