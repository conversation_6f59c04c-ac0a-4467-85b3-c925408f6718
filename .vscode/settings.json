{"python.defaultInterpreterPath": "/Users/<USER>/miniconda3/envs/jytest/bin/python", "python.analysis.extraPaths": ["/Users/<USER>/miniconda3/envs/jytest/lib/python3.12/site-packages"], "python.terminal.activateEnvironment": true, "python.terminal.activateEnvInCurrentTerminal": true, "python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.formatting.provider": "black", "editor.formatOnSave": true, "python.analysis.typeCheckingMode": "basic"}