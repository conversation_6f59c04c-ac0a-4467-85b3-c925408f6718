{"version": "0.2.0", "configurations": [{"name": "Python: <PERSON><PERSON><PERSON>", "type": "python", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": [], "django": true, "justMyCode": true, "python": "/Users/<USER>/miniconda3/envs/jytest/bin/python", "env": {"PYTHONPATH": "${workspaceFolder}", "CONDA_PREFIX": "/Users/<USER>/miniconda3/envs/jytest", "PATH": "/Users/<USER>/miniconda3/envs/jytest/bin:${env:PATH}"}, "cwd": "${workspaceFolder}", "console": "integratedTerminal", "stopOnEntry": false, "showReturnValue": true, "redirectOutput": true, "debugOptions": ["RedirectOutput", "DebugStdLib"]}]}