import json

import pandas as pd

# 读取 Excel 文件
pf = pd.read_excel(r"C:\Users\<USER>\Downloads\智能带教评测 (3).xlsx")

# 初始化一个空的 DataFrame
now_pf = pd.DataFrame()

# 遍历每一行数据
for i in range(len(pf)):
    # 获取当前行的“生成话术”列数据
    data = pf.iloc[i]["生成话术"]

    # 尝试将 JSON 字符串解析为 Python 对象
    try:
        data = json.loads(data)
    except json.JSONDecodeError:
        print(f"JSON 解码错误在行 {i}")
        continue

    # 遍历 JSON 数据
    for j in data:
        # 将当前行数据与 JSON 数据合并
        row_data = pf.iloc[i].copy()
        row_data["生成话术"] = j

        # 将当前行转换为 DataFrame
        row_df = pd.DataFrame([row_data])

        # 使用 pd.concat 方法添加数据
        now_pf = pd.concat([now_pf, row_df], ignore_index=True)

# 打印结果
now_pf.to_excel(r"C:\Users\<USER>\Desktop\工作簿1.xlsx")
