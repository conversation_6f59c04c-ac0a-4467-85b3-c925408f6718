import json
import pandas as pd

# JSON 数据
jsons = {
        "total_count": 55,
        "single_choices": [
            {
                "explanation": "《第二十条》是一部轻喜剧风格的法治题材电影，片名取自《刑法》第二十条，讲述普通人追求公平正义的故事。",
                "answer": "B",
                "id": 1,
                "question": "张艺谋的新作《第二十条》是哪种风格的电影？",
                "options": {
                    "D": "科幻片",
                    "A": "悲剧",
                    "C": "动作片",
                    "B": "轻喜剧"
                }
            },
            {
                "explanation": "在《红毯先生》中，刘德华饰演的香港天王巨星刘伟驰为了获得影帝奖项，深入农村体验生活。",
                "answer": "B",
                "id": 1,
                "options": {
                    "D": "举办大型演唱会",
                    "A": "参加国际影展",
                    "C": "拍摄科幻电影",
                    "B": "深入农村体验生活"
                },
                "question": "《红毯先生》中，刘德华饰演的角色为了获得影帝奖项采取了什么行动？"
            },
            {
                "explanation": "《飞驰人生2》延续了2019年《飞驰人生》的赛车题材。",
                "answer": "B",
                "id": 1,
                "options": {
                    "A": "2018年",
                    "D": "2021年",
                    "C": "2020年",
                    "B": "2019年"
                },
                "question": "《飞驰人生2》延续了哪一年的《飞驰人生》的赛车题材？"
            },
            {
                "explanation": "《热辣滚烫》改编自安藤樱主演的日本电影《百元之恋》，讲述一个胖女孩通过打拳找回自我、重启人生的励志故事。",
                "answer": "A",
                "id": 1,
                "options": {
                    "D": "《海街日记》",
                    "A": "《百元之恋》",
                    "C": "《入殓师》",
                    "B": "《东京爱情故事》"
                },
                "question": "《热辣滚烫》这部电影改编自哪部日本电影？"
            },
            {
                "explanation": "《熊出没·逆转时空》主要探讨了人生重启的假设，询问观众是否选择活在他人定义的成功里，以及是否知道自己内心真正想要的是什么。",
                "answer": "B",
                "id": 1,
                "options": {
                    "A": "家庭关系",
                    "D": "动物保护",
                    "C": "环境保护",
                    "B": "人生重启"
                },
                "question": "动画电影《熊出没·逆转时空》主要探讨了什么主题？"
            },
            {
                "explanation": "《我们一起摇太阳》是韩延“生命三部曲”的最终章，即第三部作品。",
                "answer": "C",
                "id": 1,
                "options": {
                    "A": "第一部",
                    "D": "第四部",
                    "C": "第三部",
                    "B": "第二部"
                },
                "question": "《我们一起摇太阳》是韩延“生命三部曲”的第几部作品？"
            },
            {
                "explanation": "根据文档内容，人工智能（AI）的进步被认为是这些技术创新应用的核心。",
                "answer": "B",
                "id": 1,
                "options": {
                    "D": "深度伪造检测",
                    "A": "3D打印",
                    "C": "蛋白质设计",
                    "B": "人工智能（AI）"
                },
                "question": "《自然》网站发布的2024年值得关注的七大技术领域中，哪项技术的进步被认为是技术创新应用的核心？"
            },
            {
                "explanation": "深度学习在蛋白质设计中使用基于序列的算法，通过处理蛋白质序列来辨别出真实蛋白质结构背后的模式。",
                "answer": "B",
                "id": 1,
                "options": {
                    "A": "基于图像的算法",
                    "D": "基于音频的算法",
                    "C": "基于文本的算法",
                    "B": "基于序列的算法"
                },
                "question": "深度学习在蛋白质设计中主要通过什么方式来处理蛋白质序列？"
            },
            {
                "explanation": "在模型输出中嵌入水印是一种通过在生成内容中嵌入特定信息来识别深度伪造内容的方法。",
                "answer": "B",
                "id": 1,
                "question": "生成式AI在解决深度伪造内容问题时，哪种方法是通过在模型输出中嵌入特定信息来实现的？",
                "options": {
                    "D": "利用算法库分析视频内容",
                    "A": "使用深度学习算法识别伪影",
                    "C": "开发深度伪造分析工具箱",
                    "B": "在模型输出中嵌入水印"
                }
            },
            {
                "explanation": "麻省理工学院的研究人员在2022年首次描述了通过PASTE进行可编程添加，精确嵌入多达36000个碱基的DNA。",
                "answer": "B",
                "id": 1,
                "question": "哪所大学的研究团队首次描述了通过位点特异性靶向元件（PASTE）进行可编程添加，精确嵌入多达36000个碱基的DNA？",
                "options": {
                    "A": "斯坦福大学",
                    "D": "匹兹堡大学",
                    "C": "加州大学旧金山分校",
                    "B": "麻省理工学院"
                }
            },
            {
                "explanation": "斯坦福大学的设备通过在肌萎缩性侧索硬化症患者的大脑中植入电极，帮助他们恢复语言能力。",
                "answer": "B",
                "id": 1,
                "question": "斯坦福大学开发的脑机接口设备帮助哪类患者恢复语言能力？",
                "options": {
                    "D": "帕金森病患者",
                    "A": "中风患者",
                    "C": "四肢瘫痪者",
                    "B": "肌萎缩性侧索硬化症患者"
                }
            }
        ],
        "multiple_choices": [
            {
                "explanation": "电影《第二十条》的主演包括雷佳音、马丽、赵丽颖等，而刘德华并不是该电影的主演。",
                "answer": [
                    "A",
                    "B",
                    "C"
                ],
                "id": 1,
                "options": {
                    "D": "刘德华",
                    "A": "雷佳音",
                    "C": "赵丽颖",
                    "B": "马丽"
                },
                "question": "以下哪些演员是电影《第二十条》的主演？"
            },
            {
                "explanation": "《红毯先生》由宁浩执导，刘德华主演，影片讽刺演艺圈，刘德华在片中本色出演香港天王巨星，并深入农村体验生活。",
                "answer": [
                    "A",
                    "B",
                    "C"
                ],
                "id": 1,
                "question": "以下哪些是《红毯先生》影片的特点？",
                "options": {
                    "A": "讽刺演艺圈",
                    "D": "科幻元素",
                    "C": "农村题材",
                    "B": "刘德华独挑大梁"
                }
            },
            {
                "explanation": "《飞驰人生2》中由沈腾、尹正、张本煜等原班人马出演，而刘德华并未参演。",
                "answer": [
                    "A",
                    "B",
                    "C"
                ],
                "id": 1,
                "options": {
                    "A": "沈腾",
                    "D": "刘德华",
                    "C": "张本煜",
                    "B": "尹正"
                },
                "question": "以下哪些演员在《飞驰人生2》中出演？"
            },
            {
                "explanation": "《热辣滚烫》《飞驰人生2》《第二十条》均具有励志主题，而《红毯先生》主要是喜剧元素。",
                "answer": [
                    "A",
                    "B",
                    "C"
                ],
                "id": 1,
                "options": {
                    "D": "《红毯先生》",
                    "A": "《热辣滚烫》",
                    "C": "《第二十条》",
                    "B": "《飞驰人生2》"
                },
                "question": "以下哪些电影定档于2024年春节档，并且具有励志主题？"
            },
            {
                "explanation": "文档中提到的《熊出没·逆转时空》、《八戒之天蓬下界》、《黄貔：天降财神猫》和《破战》均定档于2024年春节档。",
                "answer": [
                    "A",
                    "B",
                    "C",
                    "D"
                ],
                "id": 1,
                "options": {
                    "A": "《熊出没·逆转时空》",
                    "D": "《破战》",
                    "C": "《黄貔：天降财神猫》",
                    "B": "《八戒之天蓬下界》"
                },
                "question": "以下哪些电影将在2024年春节档上映？"
            },
            {
                "explanation": "韩延“生命三部曲”包括《滚蛋吧！肿瘤君》、《送你一朵小红花》和《我们一起摇太阳》。",
                "answer": [
                    "A",
                    "B",
                    "C"
                ],
                "id": 1,
                "question": "以下哪些电影是韩延“生命三部曲”的作品？",
                "options": {
                    "A": "《滚蛋吧！肿瘤君》",
                    "D": "《熊出没·逆转时空》",
                    "C": "《我们一起摇太阳》",
                    "B": "《送你一朵小红花》"
                }
            },
            {
                "explanation": "文档中提到的技术领域包括蛋白质设计、3D打印和大片段DNA嵌入，量子计算未被提及。",
                "answer": [
                    "A",
                    "B",
                    "C"
                ],
                "id": 1,
                "options": {
                    "A": "蛋白质设计",
                    "D": "量子计算",
                    "C": "大片段DNA嵌入",
                    "B": "3D打印"
                },
                "question": "以下哪些技术领域在《自然》网站发布的2024年值得关注的七大技术中被提及？"
            },
            {
                "explanation": "深度学习在蛋白质设计中可以用于设计定制的酶、设计转录调节剂以及制造功能性生物材料，而生成深度伪造内容不属于蛋白质设计的应用。",
                "answer": [
                    "A",
                    "C",
                    "D"
                ],
                "id": 1,
                "options": {
                    "D": "制造功能性生物材料",
                    "A": "设计定制的酶",
                    "C": "设计转录调节剂",
                    "B": "生成深度伪造内容"
                },
                "question": "以下哪些是深度学习在蛋白质设计中的应用？"
            },
            {
                "explanation": "解决深度伪造内容的策略包括在模型输出中嵌入水印、通过算法识别伪影以及开发深度伪造分析工具箱。选项D与DNA嵌入技术相关，不属于解决深度伪造内容的策略。",
                "answer": [
                    "A",
                    "B",
                    "C"
                ],
                "id": 1,
                "question": "以下哪些是解决深度伪造内容的策略？",
                "options": {
                    "D": "使用单链退火蛋白嵌入DNA",
                    "A": "在模型输出中嵌入水印",
                    "C": "开发深度伪造分析工具箱",
                    "B": "通过算法识别伪影"
                }
            },
            {
                "explanation": "单链退火蛋白（SSAP）和基于CRISPR的先导编辑技术都被用于将大片段DNA精确嵌入基因组中，而深度伪造分析工具箱和脑机接口设备与此无关。",
                "answer": [
                    "A",
                    "B"
                ],
                "id": 1,
                "question": "以下哪些技术或方法被用于将大片段DNA精确嵌入基因组中？",
                "options": {
                    "A": "单链退火蛋白（SSAP）",
                    "D": "脑机接口设备",
                    "C": "深度伪造分析工具箱",
                    "B": "基于CRISPR的先导编辑技术"
                }
            },
            {
                "explanation": "斯坦福大学开发了帮助肌萎缩性侧索硬化症患者的设备，加州大学旧金山分校开发了帮助中风患者的设备，匹兹堡大学则开发了用于四肢瘫痪者的设备。",
                "answer": [
                    "A",
                    "B",
                    "C"
                ],
                "id": 1,
                "options": {
                    "A": "斯坦福大学",
                    "D": "麻省理工学院",
                    "C": "匹兹堡大学",
                    "B": "加州大学旧金山分校"
                },
                "question": "以下哪些大学的研究团队开发了脑机接口设备？"
            }
        ],
        "true_or_falses": [
            {
                "explanation": "张艺谋多年前曾拍过法治题材电影《秋菊打官司》，因此《第二十条》并不是他首次拍摄的法治题材电影。",
                "answer": "错",
                "id": 1,
                "question": "《第二十条》是张艺谋首次拍摄的法治题材电影。"
            },
            {
                "explanation": "《红毯先生》并不是宁浩和刘德华的首次合作，他们曾在《疯狂的石头》中合作过。",
                "answer": "错",
                "id": 1,
                "question": "《红毯先生》是宁浩和刘德华首次合作的影片。"
            },
            {
                "explanation": "《飞驰人生2》是由韩寒执导的电影，而不是张艺谋。",
                "answer": "错",
                "id": 1,
                "question": "《飞驰人生2》是由张艺谋执导的电影。"
            },
            {
                "explanation": "贾玲在《热辣滚烫》中饰演的是一个通过打拳找回自我、重启人生的胖女孩，而不是职业拳击手。",
                "answer": "错",
                "id": 1,
                "question": "贾玲在《热辣滚烫》中饰演的角色是一名职业拳击手。"
            },
            {
                "explanation": "《熊出没·逆转时空》是“熊出没”IP的第10部电影，而不是第9部。",
                "answer": "错",
                "id": 1,
                "question": "《熊出没·逆转时空》是“熊出没”IP的第9部电影。"
            },
            {
                "explanation": "《我们一起摇太阳》是一部讲述两个身患重症的年轻人治愈之旅的影片，延续了对平凡人生活处境的刻画，并不属于科幻题材。",
                "answer": "错",
                "id": 1,
                "question": "《我们一起摇太阳》是一部科幻题材的电影。"
            },
            {
                "explanation": "文档指出深度学习在蛋白质设计中发挥了重要作用，帮助生成定制的酶和其他蛋白质。",
                "answer": "错",
                "id": 1,
                "question": "深度学习在蛋白质设计中没有发挥重要作用。"
            },
            {
                "explanation": "RFdiffusion是一种基于结构的算法，而不是基于序列的算法。",
                "answer": "错",
                "id": 1,
                "question": "RFdiffusion是一种基于序列的算法，用于设计新蛋白质。"
            },
            {
                "explanation": "根据文档内容，美国斯坦福大学的科学家确实开发了一种脑机接口设备，能够帮助肌萎缩性侧索硬化症患者每分钟说出62个单词。",
                "answer": "对",
                "id": 1,
                "question": "美国斯坦福大学开发的脑机接口设备可以帮助肌萎缩性侧索硬化症患者每分钟说出62个单词。"
            },
            {
                "explanation": "PrimeRoot能在水稻和小麦中嵌入多达2万个碱基的DNA，而不是36000个碱基。",
                "answer": "错",
                "id": 1,
                "question": "中国科学院遗传发育所的研究团队开发了PrimeRoot，这种方法能在水稻和小麦中嵌入多达36000个碱基的DNA。"
            },
            {
                "explanation": "加州大学旧金山分校的设备确实能让中风患者以每分钟78个单词的速度交流。",
                "answer": "对",
                "id": 1,
                "question": "加州大学旧金山分校的脑机接口设备可以帮助中风患者每分钟交流78个单词。"
            }
        ],
        "fill_in_the_blanks": [
            {
                "explanation": "《第二十条》片名取自《刑法》第二十条，涉及正当防卫的法律规定。",
                "answer": "正当防卫",
                "id": 1,
                "question": "电影《第二十条》的片名取自《刑法》第二十条，涉及的法律概念是______。"
            },
            {
                "explanation": "《红毯先生》在平遥国际影展放映取得了不错口碑。",
                "answer": "平遥",
                "id": 1,
                "question": "《红毯先生》在________国际影展放映取得了不错口碑。"
            },
            {
                "explanation": "《飞驰人生2》讲述的是赛车手张驰率领车队参加巴音布鲁克拉力赛的故事。",
                "answer": "巴音布鲁克",
                "id": 1,
                "question": "《飞驰人生2》讲述赛车手张驰率领车队参加__________拉力赛的故事。"
            },
            {
                "explanation": "根据文档内容，贾玲为电影《热辣滚烫》瘦身了100斤，这成为影片的一大看点。",
                "answer": "100",
                "id": 1,
                "question": "贾玲为电影《热辣滚烫》瘦身了______斤。"
            },
            {
                "explanation": "根据文档内容，《熊出没·逆转时空》是“熊出没”IP的第10部大电影。",
                "answer": "10",
                "id": 1,
                "question": "《熊出没·逆转时空》是“熊出没”IP的第____部大电影。"
            },
            {
                "explanation": "《我们一起摇太阳》由彭昱畅和李庚希主演。",
                "answer": "彭昱畅",
                "id": 1,
                "question": "《我们一起摇太阳》由______和李庚希主演。"
            },
            {
                "explanation": "根据文档，美国斯坦福大学的研究探索了单链退火蛋白（SSAP）技术，能够将拥有2000个碱基的DNA精准嵌入人类基因组。",
                "answer": "2000",
                "id": 1,
                "question": "美国斯坦福大学正在探索的单链退火蛋白（SSAP）技术，能够将拥有______个碱基的DNA精准嵌入人类基因组。"
            },
            {
                "explanation": "ZymCTRL是由西班牙巴塞罗那分子生物学研究所开发的，能够利用序列和功能数据设计出天然酶。",
                "answer": "ZymCTRL",
                "id": 1,
                "question": "西班牙巴塞罗那分子生物学研究所开发的_________，能利用序列和功能数据设计出天然酶。"
            },
            {
                "explanation": "DeepFake-O-Meter是美国水牛城大学研究团队开发的算法库，用于从不同角度分析视频内容，找出深度伪造内容。",
                "answer": "DeepFake-O-Meter",
                "id": 1,
                "question": "美国水牛城大学研究团队开发的算法库名为________，用于分析视频内容以找出深度伪造内容。"
            },
            {
                "explanation": "美国斯坦福大学正在探索单链退火蛋白（SSAP），其能将拥有2000个碱基的DNA精准嵌入人类基因组。",
                "answer": "单链退火蛋白（SSAP）",
                "id": 1,
                "question": "美国斯坦福大学正在探索________，其能将拥有2000个碱基的DNA精准嵌入人类基因组。"
            },
            {
                "explanation": "匹兹堡大学的研究团队通过在四肢瘫痪者的运动和体感皮层植入电极，实现对机械臂的控制。",
                "answer": "运动和体感皮层",
                "id": 1,
                "question": "匹兹堡大学的研究团队将电极植入一名四肢瘫痪者的________，以提供对机械臂的快速、精确控制。"
            }
        ],
        "subjectives": [
            {
                "explanation": "轻喜剧风格在法治题材电影中可以通过幽默的方式传达严肃的法律主题，使观众在轻松的氛围中思考法律和正义问题，增加影片的吸引力和教育意义。",
                "id": 1,
                "reference_answer": "轻喜剧风格能够使法治题材电影更具观赏性和娱乐性，吸引更多观众关注法律问题。通过幽默和轻松的叙述方式，观众更容易接受和理解影片传达的法律知识和正义观念。同时，这种风格也能缓解严肃题材带来的沉重感，使影片在传递社会价值的同时保持轻松愉悦的观影体验。",
                "question": "结合张艺谋的电影《第二十条》，分析轻喜剧风格对法治题材电影的影响。"
            },
            {
                "explanation": "影片通过刘伟驰的角色设定和他在农村的种种经历，揭示了演艺圈的虚荣和对名利的追逐，讽刺了明星在追求奖项时的种种不切实际的行为。",
                "id": 1,
                "reference_answer": "《红毯先生》通过刘伟驰为了获得影帝奖项而深入农村体验生活的经历，揭示了演艺圈中一些明星为了奖项和名声不惜一切代价的现象。影片通过刘伟驰的荒诞经历，讽刺了演艺圈的浮华与虚伪，展现了明星在追求名利过程中可能面临的种种尴尬和困境。",
                "question": "结合《红毯先生》的剧情，分析影片是如何通过刘伟驰的经历讽刺演艺圈的。"
            },
            {
                "explanation": "韩寒的电影风格以幽默和轻松著称，尤其是通过沈腾的表演，影片中的幽默感得到了充分的展现。观众可以通过角色的对话和情节发展感受到这种独特的幽默风格。",
                "id": 1,
                "reference_answer": "韩寒在《飞驰人生2》中通过幽默的对白、夸张的情节以及沈腾的喜剧表演风格来体现“韩式幽默”和“含腾量”。影片中，沈腾饰演的角色张驰在面对挑战时的乐观态度和幽默反应，使得影片充满了轻松愉快的氛围。此外，影片中的赛车场景和人物互动也充满了喜剧元素，进一步增强了观众的观影体验。",
                "question": "结合《飞驰人生2》的主题，分析韩寒在影片中如何体现“韩式幽默”和“含腾量”的特点。"
            },
            {
                "explanation": "影片通过角色的成长和变化，传递了积极向上的价值观，能够引发观众的共鸣和思考，激励他们在生活中勇敢追求梦想。",
                "id": 1,
                "reference_answer": "《热辣滚烫》讲述了一个胖女孩通过打拳找回自我、重启人生的故事。贾玲在影片中通过角色的转变，展现了自我突破和坚持不懈的重要性。这种励志的主题可能会激励观众，尤其是那些面临自我怀疑或生活困境的人，鼓励他们勇敢面对挑战，努力改变现状，追求更好的自己。",
                "question": "结合《热辣滚烫》的剧情，分析贾玲在影片中通过打拳找回自我的过程对观众可能产生的影响。"
            },
            {
                "explanation": "该题目鼓励答题者结合电影主题进行深入思考，参考答案提供了一种对人生重启的理解，强调了反思和调整的重要性。",
                "id": 1,
                "reference_answer": "《熊出没·逆转时空》通过时空穿越的科幻元素，探讨了人生重启的假设。人生重启意味着有机会重新选择生活的方向和目标。对于人生重启，我认为这是一种理想化的设想，虽然现实中无法实现，但它促使我们思考当前的生活选择是否符合内心的真正愿望。我们应该在现有的生活中不断反思和调整，以追求更符合内心的生活方式。",
                "question": "结合《熊出没·逆转时空》的主题，谈谈你对人生重启的看法。"
            },
            {
                "explanation": "影片通过真实的情感和细腻的刻画，传达了对生命的深刻思考，鼓励观众在面对困境时保持积极的态度和对生活的热情。",
                "id": 1,
                "reference_answer": "《我们一起摇太阳》通过两个身患重症的年轻人的治愈之旅，展现了他们在面对生命困境时的勇气和坚持。影片不仅关注他们的身体康复，更深入探讨了他们在旅途中对生命意义的思考和对自我价值的重新认识。这种治愈不仅是身体上的，更是心灵上的，体现了对生命的尊重和对生活的热爱。",
                "question": "结合《我们一起摇太阳》的主题，谈谈你对影片中“治愈之旅”的理解。"
            },
            {
                "explanation": "参考答案分析了AI在多个技术领域的应用和影响，结合了文档中提到的具体实例和技术进步。",
                "id": 1,
                "reference_answer": "人工智能在2024年值得关注的技术领域中可能带来深远的影响。首先，AI的进步是技术创新应用的核心，特别是在蛋白质设计中，深度学习算法帮助生成定制的酶和蛋白质。其次，AI在检测深度伪造内容方面提供了解决方案，如嵌入水印和算法识别伪影。最后，AI在脑机接口技术中也发挥了重要作用，帮助神经损伤患者恢复技能。因此，AI的进步将推动多个技术领域的发展，带来更高效和创新的解决方案。",
                "question": "结合文档内容，分析人工智能在2024年值得关注的技术领域中可能带来的影响。"
            },
            {
                "explanation": "深度学习通过提高蛋白质设计的效率和精确度，推动了生物技术的创新，影响了多个相关领域的发展。",
                "id": 1,
                "reference_answer": "深度学习在蛋白质设计中的应用极大地推动了生物技术的发展。通过使用大型语言模型和基于序列或结构的算法，研究人员能够设计出定制的酶和其他蛋白质，这为生物材料的制造、药物开发以及基因工程等领域开辟了新的途径。深度学习的应用提高了蛋白质设计的效率和精确度，促进了生物技术的创新和进步。",
                "question": "结合深度学习在蛋白质设计中的应用，分析其对生物技术发展的影响。"
            },
            {
                "explanation": "生成式AI的强大能力带来了深度伪造内容的风险，这可能对社会造成负面影响。通过技术手段如水印嵌入和伪影识别，以及开发专门的分析工具，可以有效地识别和防范这些风险。",
                "id": 1,
                "reference_answer": "生成式AI能够在短时间内生成逼真的文本和图像，这可能被用于制造虚假信息、误导公众或进行网络欺诈等。为应对这些风险，开发者可以在生成内容中嵌入水印以便追踪源头，或者通过算法识别伪影来鉴定内容的真实性。此外，开发深度伪造分析工具箱和算法库，如SemaFor和DeepFake-O-Meter，也有助于检测和防范深度伪造内容。",
                "question": "结合文档内容，分析生成式AI在生成深度伪造内容方面的潜在风险及其可能的解决方案。"
            },
            {
                "explanation": "单链退火蛋白（SSAP）和基于CRISPR的先导编辑技术在基因组工程中提供了精确的DNA嵌入能力，能够应用于多种生物体的基因编辑，具有重要的科学和应用价值。",
                "id": 1,
                "reference_answer": "单链退火蛋白（SSAP）和基于CRISPR的先导编辑技术在基因组工程中具有广阔的应用前景。SSAP能够将大片段DNA精准嵌入人类基因组，这为基因治疗和遗传病的修复提供了新的可能性。基于CRISPR的先导编辑技术则通过精确的位点特异性靶向，能够在植物和动物中实现大规模的基因组编辑，赋予作物抗病性和病原体抗性。这些技术的进步将推动生物技术和医学领域的创新发展。",
                "question": "结合文档内容，分析单链退火蛋白（SSAP）和基于CRISPR的先导编辑技术在基因组工程中的应用前景。"
            },
            {
                "explanation": "脑机接口技术通过植入电极和深度学习算法，能够帮助神经损伤患者恢复语言和运动能力，显著提高他们的生活质量和独立性。",
                "id": 1,
                "reference_answer": "脑机接口技术在医疗领域的潜在影响包括：帮助神经损伤患者恢复失去的技能，提高他们的生活独立性；通过深度学习和AI技术的结合，提升设备的智能化水平；为瘫痪患者提供新的交流和控制方式，改善他们的生活质量。",
                "question": "结合文档内容，分析脑机接口技术在医疗领域的潜在影响。"
            }
        ]
    }

# 提取并转换每种类型的问题为 JSON 字符串
single_choices = jsons["single_choices"]
for i, j in enumerate(single_choices):
    single_choices[i] = json.dumps(j,ensure_ascii=False, indent=4)
multiple_choices = jsons["multiple_choices"]
for i, j in enumerate(multiple_choices):
    multiple_choices[i] = json.dumps(j,ensure_ascii=False, indent=4)
true_or_falses = jsons["true_or_falses"]
for i, j in enumerate(true_or_falses):
    true_or_falses[i] = json.dumps(j,ensure_ascii=False, indent=4)
fill_in_the_blanks = jsons["fill_in_the_blanks"]
for i, j in enumerate(fill_in_the_blanks):
    fill_in_the_blanks[i] = json.dumps(j,ensure_ascii=False, indent=4)
subjectives = jsons["subjectives"]
for i, j in enumerate(subjectives):
    subjectives[i] = json.dumps(j,ensure_ascii=False, indent=4)

# 读取 Excel 文件
pf = pd.read_excel(r"C:\Users\<USER>\Desktop\新建 XLSX 工作表 (3).xlsx")

# 将 JSON 字符串作为新列添加到 DataFrame 中
pf["单选题"] = single_choices
pf["多选题"] = multiple_choices
pf["判断题"] = true_or_falses
pf["填空题"] = fill_in_the_blanks
pf["简答题"] = subjectives

# 保存更新后的 DataFrame 到 Excel 文件
pf.to_excel(r"C:\Users\<USER>\Desktop\新建 XLSX 工作表 (3).xlsx")
