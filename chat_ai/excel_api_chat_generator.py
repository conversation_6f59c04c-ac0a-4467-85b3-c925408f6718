import json

import requests
import pandas as pd


def get_chat(course_content, character_desc, course_sense, role_play, digital_identity, student_identity, initiator):
    url = "http://************:8080/admin/generate/topic_script_info"
    headers = {
        "accept": "application/json",
        "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo2NCwidHlwZSI6InVzZXIiLCJleHAiOjE3MjkzMzE0MTZ9.tqXEZDAAjZPwVfaSKyIVtMRMJkgOoXqreBmhNlpdldU",
        "Content-Type": "application/json"
    }
    data = {
        "course_content": course_content,
        "character_desc": character_desc,
        "style": "轻松",
        "course_sense": course_sense,
        "role_play": role_play,
        "digital_identity": digital_identity,
        "student_identity": student_identity,
        "search_keyword": "心脏治疗",
        "search_flag": "0",
        "initiator": initiator
    }
    resp = requests.post(url=url, headers=headers, json=data)
    # print(resp.json()["data"])
    return resp.json()["data"]


if __name__ == '__main__':
    fail_path = r"C:\Users\<USER>\Downloads\智能带教评测 (3).xlsx"
    pf = pd.read_excel(fail_path)
    chats = []
    for course_content, character_desc, course_sense, role_play, digital_identity, student_identity, initiator in zip(
            pf["框架"], pf["role_desc"], pf["对话背景"],
            pf["角色"], pf["digital_identity"], pf["student_identity"], pf["initiator"]):
        try:
            chat = get_chat(course_content, character_desc, course_sense, role_play, digital_identity, student_identity,
                            initiator)
            chat = json.dumps(chat, ensure_ascii=False, indent=4)
            print(chat)
            chats.append(chat)
        except Exception as e:
            print(e)
            chats.append(e)
    pf["生成话术"] = chats
    pf.to_excel(fail_path)
