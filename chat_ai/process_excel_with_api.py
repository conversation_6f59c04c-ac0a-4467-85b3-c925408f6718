import requests
import pandas as pd


def get_role_desc(text):
    url = "http://************:8080/admin/generate/role_info"
    headers = {
        "accept": "application/json",
        "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo2NCwidHlwZSI6InVzZXIiLCJleHAiOjE3MjkzMzE0MTZ9.tqXEZDAAjZPwVfaSKyIVtMRMJkgOoXqreBmhNlpdldU",
        "Content-Type": "application/json"
    }
    data = {"character_desc": text}
    resp = requests.post(url=url, headers=headers, json=data)
    print(resp.text)
    return resp.json()["data"]["role_desc"]


def get_title(scene_description, doc_id):
    url = "http://************:8080/admin/generate/course_frame_info"
    headers = {
        "accept": "application/json",
        "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo2NCwidHlwZSI6InVzZXIiLCJleHAiOjE3MjkzMzE0MTZ9.tqXEZDAAjZPwVfaSKyIVtMRMJkgOoXqreBmhNlpdldU",
        "Content-Type": "application/json"
    }
    data = {"scene_description": scene_description, "doc_ids": [doc_id], "org_id": "24101414100031"}
    resp = requests.post(url=url, headers=headers, json=data)
    result = []
    for item in resp.json()["data"]:
        step = item["step"]
        title = item["title"]
        purpose = item["purpose"]
        inspection_points = " ".join(item["inspection_points"])
        result.append(f"{step}. 标题：{title} 目的：{purpose} 考察要点：{inspection_points}")
    final_output = "\n".join(result)
    print(final_output)
    return final_output


if __name__ == '__main__':
    role_descs = []
    titles = []
    pf = pd.read_excel(r"C:\Users\<USER>\Desktop\工作簿1.xlsx")
    for indix, i in enumerate(pf["人设信息"]):
        print(f"第{indix + 1}条")
        try:
            role_desc = get_role_desc(i)
            role_descs.append(role_desc)
        except Exception as e:
            role_descs.append(e)
            print(e)
    pf["role_desc"] = role_descs
    pf.to_excel(r"C:\Users\<USER>\Desktop\工作簿1.xlsx")
    a = 0
    for doc_id, desc in zip(pf["doc_id"], pf["人设信息"]):
        a += 1
        print(a)
        try:
            title = get_title(desc, doc_id)
            titles.append(title)
        except Exception as e:
            titles.append(e)
            print(e)
    pf["框架"] = titles
    pf.to_excel(r"C:\Users\<USER>\Desktop\工作簿1.xlsx")
