import pandas as pd
import requests

def resp(course_id, doc_id):
    url = "http://10.60.81.112:8080/admin/course/generate"
    data = {
        "course_id": course_id,
        "org_id": "24101414100031",
        "course_name": "民法典知识",
        "course_type": "mp4",
        "user_id": "1",
        "doc_ids": [doc_id]
    }
    headers = {
        "accept": "application/json",
        "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo2NCwidHlwZSI6InVzZXIiLCJleHAiOjE3MjkzMzE0MTZ9.tqXEZDAAjZPwVfaSKyIVtMRMJkgOoXqreBmhNlpdldU",
        "Content-Type": "application/json"
    }
    response = requests.post(url=url, headers=headers, json=data)
    return response

def main(path):
    pf = pd.read_excel(path)
    course_ids = pf["course_id"]
    doc_ids = pf["doc_id"]
    index = 0
    for course_id, doc_id in zip(course_ids, doc_ids):
        index += 1
        print(f"第{index}条: {course_id}, {doc_id}")
        try:
            response = resp(course_id, doc_id)
            print(response.text)
        except Exception as e:
            print(f"请求失败: {e}")

if __name__ == '__main__':
    path = r"C:\Users\<USER>\Desktop\新建 XLSX 工作表 (2).xlsx"
    main(path)
