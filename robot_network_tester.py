#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器人弱网环境测试工具
专门针对安卓机器人设备的网络测试框架
"""

import time
import json
import logging
import subprocess
from dataclasses import dataclass, asdict
from typing import Dict, List, Any
from datetime import datetime


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'robot_test_{int(time.time())}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class NetworkCondition:
    """网络条件配置"""
    name: str
    download_bandwidth: str  # "1Mbps", "512Kbps"
    upload_bandwidth: str
    latency: int            # 延迟毫秒
    packet_loss: float      # 丢包百分比
    jitter: int            # 抖动毫秒
    description: str


@dataclass
class TestResult:
    """测试结果"""
    test_case: str
    start_time: datetime
    end_time: datetime
    network_condition: str
    success: bool
    response_time_ms: float
    error_message: str = ""
    metrics: Dict[str, Any] = None

    def __post_init__(self):
        if self.metrics is None:
            self.metrics = {}


class RobotController:
    """机器人控制器"""

    def __init__(self, robot_ip: str, adb_path: str = "adb"):
        self.robot_ip = robot_ip
        self.adb_path = adb_path
        self.device_id = None
        self._connect_device()

    def _connect_device(self):
        """连接安卓设备"""
        try:
            # 通过ADB连接设备
            cmd = f"{self.adb_path} connect {self.robot_ip}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if "connected" in result.stdout:
                self.device_id = self.robot_ip
                logger.info(f"成功连接到机器人设备: {self.robot_ip}")
            else:
                raise Exception(f"无法连接到设备: {result.stderr}")

        except Exception as e:
            logger.error(f"设备连接失败: {e}")
            raise

    def send_voice_command(self, text: str) -> Dict[str, Any]:
        """发送语音指令"""
        start_time = time.time()

        try:
            # 模拟语音输入 - 通过ADB发送文本或调用机器人API
            cmd = f"{self.adb_path} -s {self.device_id} shell am broadcast -a android.intent.action.VOICE_COMMAND --es text '{text}'"
            subprocess.run(cmd, shell=True, capture_output=True, text=True)

            # 等待响应
            response = self._wait_for_response(timeout=30)

            end_time = time.time()
            response_time = (end_time - start_time) * 1000

            return {
                "success": response.get("success", False),
                "response_time": response_time,
                "response_text": response.get("text", ""),
                "accuracy": response.get("accuracy", 0.0)
            }

        except Exception as e:
            end_time = time.time()
            return {
                "success": False,
                "response_time": (end_time - start_time) * 1000,
                "error": str(e)
            }

    def trigger_action(self, action: str) -> Dict[str, Any]:
        """触发机器人动作"""
        start_time = time.time()

        try:
            # 发送动作指令
            cmd = f"{self.adb_path} -s {self.device_id} shell am broadcast -a robot.action.EXECUTE --es action '{action}'"
            subprocess.run(cmd, shell=True, capture_output=True, text=True)

            # 监控动作执行
            action_result = self._monitor_action_execution(action, timeout=15)

            end_time = time.time()
            execution_time = (end_time - start_time) * 1000

            return {
                "success": action_result.get("completed", False),
                "execution_time": execution_time,
                "action_status": action_result.get("status", "unknown")
            }

        except Exception as e:
            end_time = time.time()
            return {
                "success": False,
                "execution_time": (end_time - start_time) * 1000,
                "error": str(e)
            }

    def test_screen_interaction(self, coordinates: tuple) -> Dict[str, Any]:
        """测试屏幕交互"""
        start_time = time.time()

        try:
            x, y = coordinates
            # 发送触摸事件
            cmd = f"{self.adb_path} -s {self.device_id} shell input tap {x} {y}"
            subprocess.run(cmd, shell=True, capture_output=True, text=True)

            # 检测响应
            response = self._check_screen_response(timeout=5)

            end_time = time.time()
            response_time = (end_time - start_time) * 1000

            return {
                "success": response.get("responded", False),
                "response_time": response_time,
                "ui_updated": response.get("ui_changed", False)
            }

        except Exception as e:
            end_time = time.time()
            return {
                "success": False,
                "response_time": (end_time - start_time) * 1000,
                "error": str(e)
            }

    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统性能指标"""
        try:
            metrics = {}

            # CPU使用率
            cmd = f"{self.adb_path} -s {self.device_id} shell dumpsys cpuinfo | head -5"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            metrics["cpu_info"] = result.stdout

            # 内存使用
            cmd = f"{self.adb_path} -s {self.device_id} shell dumpsys meminfo"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            metrics["memory_info"] = result.stdout

            # 网络状态
            cmd = f"{self.adb_path} -s {self.device_id} shell dumpsys connectivity"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            metrics["network_info"] = result.stdout

            return metrics

        except Exception as e:
            logger.error(f"获取系统指标失败: {e}")
            return {}

    def _wait_for_response(self, timeout: int = 30) -> Dict[str, Any]:
        """等待机器人响应"""
        # 这里需要根据实际机器人的响应机制来实现
        # 可以通过监听日志、检查界面变化、或调用API来判断
        time.sleep(2)  # 模拟等待
        return {
            "success": True,
            "text": "模拟回复",
            "accuracy": 0.95
        }

    def _monitor_action_execution(self, action: str, timeout: int = 15) -> Dict[str, Any]:
        """监控动作执行"""
        # 监控机器人动作执行状态
        time.sleep(3)  # 模拟动作执行时间
        return {
            "completed": True,
            "status": "executed"
        }

    def _check_screen_response(self, timeout: int = 5) -> Dict[str, Any]:
        """检查屏幕响应"""
        # 检查UI变化
        time.sleep(1)  # 模拟检查时间
        return {
            "responded": True,
            "ui_changed": True
        }


class NetworkSimulator:
    """网络环境模拟器"""

    def __init__(self, interface: str = "wlan0"):
        self.interface = interface
        self.current_condition = None
        self.original_settings = None

    def apply_condition(self, condition: NetworkCondition):
        """应用网络条件"""
        logger.info(f"应用网络条件: {condition.name}")

        try:
            # 保存原始设置
            if self.original_settings is None:
                self.original_settings = self._get_current_settings()

            # 应用网络限制
            self._apply_bandwidth_limit(condition.download_bandwidth, condition.upload_bandwidth)
            self._apply_latency(condition.latency)
            self._apply_packet_loss(condition.packet_loss)

            self.current_condition = condition
            logger.info(f"网络条件已应用: {condition.description}")

        except Exception as e:
            logger.error(f"应用网络条件失败: {e}")
            raise

    def clear_conditions(self):
        """清除网络限制"""
        if self.current_condition is None:
            return

        logger.info("清除网络限制")
        try:
            # 恢复原始设置
            cmd = f"sudo tc qdisc del dev {self.interface} root"
            subprocess.run(cmd, shell=True, check=False)

            self.current_condition = None
            logger.info("网络限制已清除")

        except Exception as e:
            logger.error(f"清除网络限制失败: {e}")

    def _apply_bandwidth_limit(self, download: str, upload: str):
        """应用带宽限制"""
        if download:
            cmd = f"sudo tc qdisc add dev {self.interface} root tbf rate {download} burst 32kbit latency 400ms"
            subprocess.run(cmd, shell=True, check=True)

    def _apply_latency(self, latency_ms: int):
        """应用延迟"""
        if latency_ms > 0:
            cmd = f"sudo tc qdisc add dev {self.interface} root netem delay {latency_ms}ms"
            subprocess.run(cmd, shell=True, check=True)

    def _apply_packet_loss(self, loss_percent: float):
        """应用丢包"""
        if loss_percent > 0:
            cmd = f"sudo tc qdisc add dev {self.interface} root netem loss {loss_percent}%"
            subprocess.run(cmd, shell=True, check=True)

    def _get_current_settings(self) -> Dict[str, Any]:
        """获取当前网络设置"""
        return {"interface": self.interface}


class RobotNetworkTester:
    """机器人网络测试器"""

    def __init__(self, robot_ip: str, network_interface: str = "wlan0"):
        self.robot = RobotController(robot_ip)
        self.network_sim = NetworkSimulator(network_interface)
        self.test_results: List[TestResult] = []

        # 预定义的网络条件
        self.network_conditions = [
            NetworkCondition("normal", "50Mbps", "20Mbps", 50, 0.1, 10, "正常网络环境"),
            NetworkCondition("weak_wifi", "2Mbps", "1Mbps", 300, 3.0, 50, "弱WiFi信号"),
            NetworkCondition("poor_4g", "1Mbps", "512Kbps", 500, 5.0, 100, "差4G网络"),
            NetworkCondition("unstable", "变化", "变化", 200, 8.0, 150, "不稳定网络"),
            NetworkCondition("very_slow", "512Kbps", "256Kbps", 1000, 10.0, 200, "极慢网络"),
        ]

        # 测试用例
        self.test_cases = {
            "voice_simple": {
                "name": "简单语音交互",
                "commands": ["你好", "现在几点了", "谢谢"],
                "expected_response_time": 3000  # 毫秒
            },
            "voice_complex": {
                "name": "复杂语音交互",
                "commands": ["请帮我查询明天的天气情况", "介绍一下这个展厅的主要展品"],
                "expected_response_time": 5000
            },
            "action_basic": {
                "name": "基础动作测试",
                "actions": ["wave", "nod", "point"],
                "expected_execution_time": 2000
            },
            "action_complex": {
                "name": "复杂动作测试",
                "actions": ["guide_to_meeting_room", "demonstration"],
                "expected_execution_time": 8000
            },
            "screen_interaction": {
                "name": "屏幕交互测试",
                "interactions": [(400, 300), (600, 500), (200, 400)],
                "expected_response_time": 1000
            }
        }

    def run_voice_test(self, test_case: str, network_condition: NetworkCondition) -> TestResult:
        """运行语音测试"""
        logger.info(f"执行语音测试: {test_case} - {network_condition.name}")

        case_config = self.test_cases[test_case]
        start_time = datetime.now()

        try:
            # 应用网络条件
            self.network_sim.apply_condition(network_condition)

            results = []
            for command in case_config["commands"]:
                result = self.robot.send_voice_command(command)
                results.append(result)
                time.sleep(1)  # 间隔1秒

            # 计算平均性能
            avg_response_time = sum(r["response_time"] for r in results) / len(results)
            success_rate = sum(1 for r in results if r["success"]) / len(results)

            success = (avg_response_time <= case_config["expected_response_time"] and
                       success_rate >= 0.8)

            return TestResult(
                test_case=case_config["name"],
                start_time=start_time,
                end_time=datetime.now(),
                network_condition=network_condition.name,
                success=success,
                response_time_ms=avg_response_time,
                metrics={
                    "success_rate": success_rate,
                    "avg_response_time": avg_response_time,
                    "individual_results": results
                }
            )

        except Exception as e:
            return TestResult(
                test_case=case_config["name"],
                start_time=start_time,
                end_time=datetime.now(),
                network_condition=network_condition.name,
                success=False,
                response_time_ms=0,
                error_message=str(e)
            )

    def run_action_test(self, test_case: str, network_condition: NetworkCondition) -> TestResult:
        """运行动作测试"""
        logger.info(f"执行动作测试: {test_case} - {network_condition.name}")

        case_config = self.test_cases[test_case]
        start_time = datetime.now()

        try:
            self.network_sim.apply_condition(network_condition)

            results = []
            for action in case_config["actions"]:
                result = self.robot.trigger_action(action)
                results.append(result)
                time.sleep(2)  # 动作间隔

            avg_execution_time = sum(r["execution_time"] for r in results) / len(results)
            success_rate = sum(1 for r in results if r["success"]) / len(results)

            success = (avg_execution_time <= case_config["expected_execution_time"] and
                       success_rate >= 0.9)

            return TestResult(
                test_case=case_config["name"],
                start_time=start_time,
                end_time=datetime.now(),
                network_condition=network_condition.name,
                success=success,
                response_time_ms=avg_execution_time,
                metrics={
                    "success_rate": success_rate,
                    "avg_execution_time": avg_execution_time,
                    "individual_results": results
                }
            )

        except Exception as e:
            return TestResult(
                test_case=case_config["name"],
                start_time=start_time,
                end_time=datetime.now(),
                network_condition=network_condition.name,
                success=False,
                response_time_ms=0,
                error_message=str(e)
            )

    def run_screen_test(self, test_case: str, network_condition: NetworkCondition) -> TestResult:
        """运行屏幕交互测试"""
        logger.info(f"执行屏幕测试: {test_case} - {network_condition.name}")

        case_config = self.test_cases[test_case]
        start_time = datetime.now()

        try:
            self.network_sim.apply_condition(network_condition)

            results = []
            for coords in case_config["interactions"]:
                result = self.robot.test_screen_interaction(coords)
                results.append(result)
                time.sleep(0.5)

            avg_response_time = sum(r["response_time"] for r in results) / len(results)
            success_rate = sum(1 for r in results if r["success"]) / len(results)

            success = (avg_response_time <= case_config["expected_response_time"] and
                       success_rate >= 0.95)

            return TestResult(
                test_case=case_config["name"],
                start_time=start_time,
                end_time=datetime.now(),
                network_condition=network_condition.name,
                success=success,
                response_time_ms=avg_response_time,
                metrics={
                    "success_rate": success_rate,
                    "avg_response_time": avg_response_time,
                    "individual_results": results
                }
            )

        except Exception as e:
            return TestResult(
                test_case=case_config["name"],
                start_time=start_time,
                end_time=datetime.now(),
                network_condition=network_condition.name,
                success=False,
                response_time_ms=0,
                error_message=str(e)
            )

    def run_comprehensive_test(self) -> List[TestResult]:
        """运行综合测试"""
        logger.info("开始机器人弱网环境综合测试")

        all_results = []

        for condition in self.network_conditions:
            logger.info(f"测试网络条件: {condition.name} - {condition.description}")

            # 语音测试
            for voice_test in ["voice_simple", "voice_complex"]:
                result = self.run_voice_test(voice_test, condition)
                all_results.append(result)
                self.test_results.append(result)

            # 动作测试
            for action_test in ["action_basic", "action_complex"]:
                result = self.run_action_test(action_test, condition)
                all_results.append(result)
                self.test_results.append(result)

            # 屏幕交互测试
            result = self.run_screen_test("screen_interaction", condition)
            all_results.append(result)
            self.test_results.append(result)

            # 获取系统指标 (用于调试和监控)
            self.robot.get_system_metrics()
            logger.info(f"网络条件 {condition.name} 测试完成")

            time.sleep(5)  # 测试间隔

        # 清除网络限制
        self.network_sim.clear_conditions()

        return all_results

    def run_stability_test(self, duration_hours: int = 2):
        """运行稳定性测试"""
        logger.info(f"开始 {duration_hours} 小时稳定性测试")

        end_time = time.time() + (duration_hours * 3600)
        test_count = 0

        while time.time() < end_time:
            # 随机选择网络条件和测试用例
            import random
            condition = random.choice(self.network_conditions[1:])  # 跳过正常网络
            test_type = random.choice(["voice_simple", "action_basic", "screen_interaction"])

            if test_type.startswith("voice"):
                result = self.run_voice_test(test_type, condition)
            elif test_type.startswith("action"):
                result = self.run_action_test(test_type, condition)
            else:
                result = self.run_screen_test(test_type, condition)

            self.test_results.append(result)
            test_count += 1

            if test_count % 10 == 0:
                success_rate = sum(1 for r in self.test_results[-10:] if r.success) / 10
                logger.info(f"稳定性测试进度: {test_count} 次测试, 最近10次成功率: {success_rate:.2%}")

            time.sleep(30)  # 30秒间隔

        logger.info(f"稳定性测试完成: 总共 {test_count} 次测试")

    def generate_report(self) -> str:
        """生成测试报告"""
        if not self.test_results:
            return "没有测试结果"

        report = []
        report.append("# 机器人弱网环境测试报告\n")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        report.append("=" * 60 + "\n")

        # 按网络条件分组统计
        condition_stats = {}
        for result in self.test_results:
            condition = result.network_condition
            if condition not in condition_stats:
                condition_stats[condition] = {
                    "total": 0,
                    "success": 0,
                    "avg_response_time": [],
                    "test_cases": {}
                }

            stats = condition_stats[condition]
            stats["total"] += 1
            if result.success:
                stats["success"] += 1
            stats["avg_response_time"].append(result.response_time_ms)

            # 按测试用例统计
            test_case = result.test_case
            if test_case not in stats["test_cases"]:
                stats["test_cases"][test_case] = {"total": 0, "success": 0}
            stats["test_cases"][test_case]["total"] += 1
            if result.success:
                stats["test_cases"][test_case]["success"] += 1

        # 生成报告内容
        for condition, stats in condition_stats.items():
            success_rate = stats["success"] / stats["total"] if stats["total"] > 0 else 0
            avg_time = sum(stats["avg_response_time"]) / len(stats["avg_response_time"]) if stats["avg_response_time"] else 0

            report.append(f"\n## 网络条件: {condition}\n")
            report.append(f"- 总测试数: {stats['total']}\n")
            report.append(f"- 成功率: {success_rate:.2%}\n")
            report.append(f"- 平均响应时间: {avg_time:.2f}ms\n")

            report.append("### 各测试用例结果:\n")
            for test_case, case_stats in stats["test_cases"].items():
                case_success_rate = case_stats["success"] / case_stats["total"] if case_stats["total"] > 0 else 0
                report.append(f"- {test_case}: {case_success_rate:.2%} ({case_stats['success']}/{case_stats['total']})\n")

        # 总体统计
        total_tests = len(self.test_results)
        total_success = sum(1 for r in self.test_results if r.success)
        overall_success_rate = total_success / total_tests if total_tests > 0 else 0

        report.append("\n## 总体统计\n")
        report.append(f"- 总测试数: {total_tests}\n")
        report.append(f"- 总成功率: {overall_success_rate:.2%}\n")

        return "".join(report)

    def save_results(self, filename: str = None):
        """保存测试结果"""
        if filename is None:
            filename = f"robot_test_results_{int(time.time())}"

        # 保存JSON格式数据
        json_data = {
            "test_info": {
                "total_tests": len(self.test_results),
                "success_count": sum(1 for r in self.test_results if r.success),
                "test_time": datetime.now().isoformat()
            },
            "results": [asdict(result) for result in self.test_results]
        }

        with open(f"{filename}.json", 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2, default=str)

        # 保存报告
        report = self.generate_report()
        with open(f"{filename}_report.md", 'w', encoding='utf-8') as f:
            f.write(report)

        logger.info(f"测试结果已保存: {filename}.json, {filename}_report.md")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='机器人弱网环境测试工具')
    parser.add_argument('--robot-ip', required=True, help='机器人设备IP地址')
    parser.add_argument('--interface', default='wlan0', help='网络接口名称')
    parser.add_argument('--test-type', choices=['comprehensive', 'stability', 'quick'],
                        default='comprehensive', help='测试类型')
    parser.add_argument('--duration', type=int, default=2, help='稳定性测试持续时间(小时)')
    parser.add_argument('--output', help='输出文件名前缀')

    args = parser.parse_args()

    try:
        # 创建测试器
        tester = RobotNetworkTester(args.robot_ip, args.interface)

        if args.test_type == 'comprehensive':
            logger.info("开始综合测试...")
            tester.run_comprehensive_test()
        elif args.test_type == 'stability':
            logger.info(f"开始稳定性测试 ({args.duration}小时)...")
            tester.run_stability_test(args.duration)
        elif args.test_type == 'quick':
            logger.info("开始快速测试...")
            # 只测试正常和一种弱网条件
            normal_condition = tester.network_conditions[0]
            weak_condition = tester.network_conditions[1]

            for condition in [normal_condition, weak_condition]:
                result = tester.run_voice_test("voice_simple", condition)
                tester.test_results.append(result)

        # 保存结果
        tester.save_results(args.output)

        logger.info("测试完成!")

    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试执行异常: {e}")
    finally:
        # 确保清理网络设置
        try:
            if 'tester' in locals():
                tester.network_sim.clear_conditions()
        except Exception:
            pass


if __name__ == "__main__":
    main()
