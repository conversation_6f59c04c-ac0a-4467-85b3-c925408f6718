<!DOCTYPE html>
<html>
<head>
    <title>Model Performance</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f2f2f2;
        }

        h1 {
            margin-bottom: 20px;
            color: #333;
        }

        form {
            margin-bottom: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        th {
            background-color: #f5f5f5;
            color: #333;
        }

        tbody tr:hover {
            background-color: #f9f9f9;
        }

        .pagination {
            margin-top: 20px;
            text-align: center;
        }

        .pagination a {
            display: inline-block;
            padding: 6px 12px;
            margin-right: 5px;
            background-color: #337ab7;
            color: #fff;
            border: 1px solid #337ab7;
            text-decoration: none;
            transition: background-color 0.3s;
        }

        .pagination a:hover {
            background-color: #23527c;
        }

        .pagination .active {
            background-color: #23527c;
            border: 1px solid #23527c;
        }

        .pagination .disabled {
            pointer-events: none;
            opacity: 0.6;
        }

        h1 {
            text-align: center;
        }
    </style>
</head>
<body>
<h1>
    Model Performance Summary</h1>

<form action="/api/performance" method="GET">
    <label for="start_task_id">Start Task ID:</label>
    <input type="text" id="start_task_id" name="start_task_id" value="{{ request.args.get('start_task_id', '') }}">
    <label for="end_task_id">End Task ID:</label>
    <input type="text" id="end_task_id" name="end_task_id" value="{{ request.args.get('end_task_id', '') }}">
    <input type="submit" value="Search">
</form>

<table>
    <thead>
    <tr>
        <th>Scene Description</th>
        <th>Model Name</th>
        <th>Token Speed(t/s)</th>
        <th>Speed(字/s)</th>
        <th>First Package Cost Time(ms)</th>
        <th>Total Cost Time(s)</th>
        <th>Task Thread</th>
        <th>env</th>
        <th>GPU</th>
        <th>Task Name</th>
        <th>Task ID</th>
        <th>Input Token Length</th>
        <th>Output Token Length</th>

    </tr>
    </thead>
    <tbody>
    {% for item in data %}
    <tr>
        <td>{{item.scene_description}}</td>
        <td>{{item.model_name}}</td>
        <td>{{ item.token_speed }}</td>
        <td>{{ item.speed }}</td>
        <td>{{ item.first_package_cost_time }}</td>
        <td>{{ item.total_cost_time }}</td>
        <td>{{ item.task_thread }}</td>
        <td>{{item.env}}</td>
        <td>{{item.GPU}}</td>
        <td>{{ item.task_name }}</td>
        <td>{{ item.task_id }}</td>
        <td>{{ item.input_token_len }}</td>
        <td>{{ item.output_token_len }}</td>
    </tr>
    {% endfor %}
    </tbody>
</table>

</body>
</html>
