<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>翻译助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        .corpus-list {
            max-height: 70vh;
            overflow-y: auto;
        }
        .translation-result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        textarea {
            resize: vertical;
            min-height: 60px;
        }
        .gap-2 > * + * { margin-left: 0.5rem !important; }
        .mic-btn {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            border: none;
            background: #f1f3f4;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            transition: background 0.2s, box-shadow 0.2s;
            position: relative;
            cursor: pointer;
            outline: none;
        }
        .mic-btn:hover {
            background: #e3e7fa;
            box-shadow: 0 4px 16px rgba(0,0,0,0.12);
        }
        .mic-btn.recording {
            background: #ff4d4f;
            animation: mic-breath 1.2s infinite alternate;
        }
        @keyframes mic-breath {
            0% { box-shadow: 0 0 0 0 rgba(255,77,79,0.4); }
            100% { box-shadow: 0 0 0 12px rgba(255,77,79,0.1); }
        }
        .mic-btn i {
            font-size: 1.5rem;
            color: #333;
            transition: color 0.2s;
        }
        .mic-btn.recording i {
            color: #fff;
        }
        .mic-tooltip {
            display: none;
            position: absolute;
            left: 60px;
            top: 8px;
            background: #222;
            color: #fff;
            padding: 4px 10px;
            border-radius: 4px;
            font-size: 0.95em;
            white-space: nowrap;
            z-index: 10;
        }
        .mic-btn:hover + .mic-tooltip {
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>翻译助手</h2>
        
        <!-- 添加新语料 -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="add-corpus">
                    <textarea id="new-corpus" placeholder="输入中文语料" style="width: 60%; min-width: 300px; resize: vertical;" rows="2" onkeydown="if(event.key==='Enter'&&!event.shiftKey){event.preventDefault();addCorpus();}"></textarea>
                    <button class="mic-btn" onclick="startSpeechInput()" id="speechBtn" title="语音输入">
                        <i class="bi bi-mic" id="micIcon"></i>
                    </button>
                    <span class="mic-tooltip" id="micTooltip">语音输入</span>
                    <span style="margin-left:10px;">
                        <label><input type="radio" name="tts-type" value="gtts" checked> gTTS</label>
                        <label style="margin-left:10px;"><input type="radio" name="tts-type" value="edge"> Edge TTS</label>
                    </span>
                    <button class="btn btn-primary" style="margin-left:10px;" onclick="addCorpus()">添加</button>
                </div>
            </div>
        </div>

        <!-- 语料列表 -->
        <div class="corpus-list">
            {% for corpus in corpus_list %}
            <div class="card mb-3" id="corpus-{{ corpus.id }}">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="mb-2" style="font-size: 1.1em; font-weight: bold;">{{ corpus.text }}</div>
                            <div class="translation-result" id="result-{{ corpus.id }}">
                                <div class="en-text"><b>英文：</b>{{ corpus.translations.en if corpus.translations and 'en' in corpus.translations else '' }}</div>
                                <div class="ja-text"><b>日文：</b>{{ corpus.translations.ja if corpus.translations and 'ja' in corpus.translations else '' }}</div>
                            </div>
                        </div>
                        <div class="ms-3 d-flex flex-row align-items-center gap-2">
                            <button class="btn btn-outline-primary" onclick="translateAndPlay('{{ corpus.text }}', 'en', {{ corpus.id }})">
                                播放英语
                            </button>
                            <button class="btn btn-outline-success" onclick="translateAndPlay('{{ corpus.text }}', 'ja', {{ corpus.id }})">
                                播放日语
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteCorpus({{ corpus.id }})">删除</button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
          </div>
          <div class="modal-body">
            确定要删除这条语料吗？
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确定</button>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    let recognition = null;
    let isRecording = false;

    function initSpeechRecognition() {
        if (!('webkitSpeechRecognition' in window)) {
            alert('您的浏览器不支持语音识别功能，请使用Chrome浏览器。');
            return;
        }

        recognition = new webkitSpeechRecognition();
        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.lang = 'zh-CN';

        recognition.onstart = function() {
            isRecording = true;
            document.getElementById('speechBtn').classList.add('recording');
            document.getElementById('micIcon').classList.remove('bi-mic');
            document.getElementById('micIcon').classList.add('bi-mic-fill');
            document.getElementById('micTooltip').innerText = '正在录音...';
        };

        recognition.onend = function() {
            isRecording = false;
            document.getElementById('speechBtn').classList.remove('recording');
            document.getElementById('micIcon').classList.remove('bi-mic-fill');
            document.getElementById('micIcon').classList.add('bi-mic');
            document.getElementById('micTooltip').innerText = '语音输入';
        };

        recognition.onresult = function(event) {
            const text = event.results[0][0].transcript;
            document.getElementById('new-corpus').value = text;
        };

        recognition.onerror = function(event) {
            console.error('语音识别错误:', event.error);
            isRecording = false;
            document.getElementById('speechBtn').classList.remove('recording');
            document.getElementById('micIcon').classList.remove('bi-mic-fill');
            document.getElementById('micIcon').classList.add('bi-mic');
            document.getElementById('micTooltip').innerText = '语音输入';
        };
    }

    function startSpeechInput() {
        if (!recognition) {
            initSpeechRecognition();
        }
        
        if (isRecording) {
            recognition.stop();
        } else {
            recognition.start();
        }
    }

    // 页面加载完成后初始化语音识别
    window.onload = function() {
        initSpeechRecognition();
    };

    function getSelectedTtsType() {
        const radios = document.getElementsByName('tts-type');
        for (let i = 0; i < radios.length; i++) {
            if (radios[i].checked) return radios[i].value;
        }
        return 'gtts';
    }

    function addCorpus() {
        const input = document.getElementById('new-corpus');
        const text = input.value.trim();
        const ttsType = getSelectedTtsType();
        if (!text) return;
        fetch('/api/corpus/add/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ text: text, tts_type: ttsType })
        })
        .then(response => response.json())
        .then(data => {
            if (data.id !== undefined) {
                location.reload();
            }
        });
        input.value = '';
        input.focus();
    }

    function updateCorpus(corpusId, text) {
        fetch(`/api/corpus/${corpusId}/update/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ text: text })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新成功
            }
        });
    }

    function translateAndPlay(text, lang, corpusId) {
        const ttsType = getSelectedTtsType();
        fetch('/api/translate/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ text: text, lang: lang, id: corpusId, tts_type: ttsType })
        })
        .then(response => response.json())
        .then(data => {
            // 只更新对应语言的文案，不覆盖另一种语言
            const resultDiv = document.getElementById(`result-${corpusId}`);
            if (lang === 'en') {
                const enSpan = resultDiv.querySelector('.en-text');
                if (enSpan) enSpan.innerHTML = `<b>英文：</b>${data.translation}`;
            } else if (lang === 'ja') {
                const jaSpan = resultDiv.querySelector('.ja-text');
                if (jaSpan) jaSpan.innerHTML = `<b>日文：</b>${data.translation}`;
            }
            // 播放语音（始终用后端返回的audio_url）
            const audio = new Audio(data.audio_url);
            audio.play();
        });
    }

    let pendingDeleteId = null;
    function deleteCorpus(corpusId) {
        pendingDeleteId = corpusId;
        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }
    document.getElementById('confirmDeleteBtn').onclick = function() {
        if (pendingDeleteId !== null) {
            fetch(`/api/corpus/${pendingDeleteId}/delete/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById(`corpus-${pendingDeleteId}`).remove();
                }
                pendingDeleteId = null;
                bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
            });
        }
    };
    </script>
</body>
</html> 