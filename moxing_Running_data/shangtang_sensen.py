# 跑商汤sensen模型批量获取结果
# kzy
# _*_ coding:utf-8 _*_

import requests
import json
import os
from openpyxl import Workbook
import time

file_path = r"/Users/<USER>/Downloads/pyshuju/budai.json"
output_folder = r"/Users/<USER>/Downloads/pyshuju/pyexcel"
output_filename = "shangtang.xlsx"


def main():
    url = 'https://api.sensenova.cn/v1/llm/chat-completions'

    workbook = Workbook()
    worksheet = workbook.active
    worksheet.append(["qa_prompt", "id", "message", "prompt_tokens", "completion_tokens", "total_tokens"])
    with open(file_path, "r", encoding="utf-8") as file:
        for line in file:
            response_data = json.loads(line)
            qa_prompt = response_data.get("qa_prompt", "")

            payload = {
                "max_new_tokens": 1024,
                "messages": [
                    {
                        "content": qa_prompt,
                        "role": "user"
                    }
                ],
                "model": "nova-ptc-xs-v1",
                "repetition_penalty": 1.0,
                "stream": False,
                "temperature": 0.95,
                "top_p": 0.8
            }

            headers = {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiIyVjZXRVNWcXhWbDJvdnA2elpIUlp4aW5vR3EiLCJleHAiOjE2OTQzMzcxMjAsIm5iZiI6MTY5NDE1NzExNX0.tA7DOATL4H-_gyPgkczy7vkN_X-6zD2zhvy8X-8onS4'
            }

            response = requests.post(url, headers=headers, data=json.dumps(payload))

            if response.status_code == 200:
                response_data = response.json()
                print("响应数据：", response_data)  # 打印响应数据

                data = response_data.get('data', {})  # 获取data字段
                if data:
                    id_val = data.get('id', '')
                    choices = data.get('choices', [])

                    if choices:
                        choice = choices[0]  # 假设只有一个choice
                        message = choice.get('message', '')
                        usage = data.get('usage', {})
                        prompt_tokens_val = usage.get('prompt_tokens', '')
                        completion_tokens_val = usage.get('completion_tokens', '')
                        total_tokens_val = usage.get('total_tokens', '')

                        worksheet.append([qa_prompt, id_val, message, prompt_tokens_val, completion_tokens_val, total_tokens_val])
                    else:
                        print("未找到choices字段")
                else:
                    print("未找到data字段")
            else:
                print(f"API 请求失败，状态码为 {response.status_code}")
                print(response.text)  # 打印响应文本以进行调试
            time.sleep(1)

    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    output_path = os.path.join(output_folder, output_filename)
    workbook.save(output_path)
    print(f"Excel 文件已保存到：{output_path}")


if __name__ == "__main__":
    main()
