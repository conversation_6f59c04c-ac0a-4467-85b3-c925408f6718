#跑百川模型数据脚本
#kzy
# _*_ coding:utf-8 _*_
import os

import requests
import json
import time
import hashlib
import uuid
import openpyxl
from tool.util import get_path,get_time
from test_conifg.path_config import MODEL_OUTPUT_PATH,PROMPT_JSON
out_path=get_path(prefix=MODEL_OUTPUT_PATH,dirs="baichuan_api")
out_file="{T}_baichuan_api.xlsx".format(T=get_time())
file_path = "{prifix}{sep}baidu.json".format(prifix=PROMPT_JSON,sep=os.sep)
def calculate_md5(input_string):
    md5 = hashlib.md5()
    md5.update(input_string.encode('utf-8'))
    encrypted = md5.hexdigest()
    return encrypted

def extract_and_write_to_excel(data):
    # 创建一个新的Excel工作簿
    workbook = openpyxl.Workbook()
    sheet = workbook.active

    # 写入表头
    sheet['A1'] = "qa_prompt"
    sheet['B1'] = "content"
    sheet['C1'] = "prompt_tokens"
    sheet['D1'] = "answer_tokens"
    sheet['E1'] = "total_tokens"
    sheet['F1'] = "X-BC-Request-Id"
    sheet['G1'] = "code"

    # 逐行写入数据
    for index, item in enumerate(data):
        row_number = index + 2  # 从第二行开始写入数据
        sheet.cell(row=row_number, column=1, value=item.get("qa_prompt", "null"))
        sheet.cell(row=row_number, column=2, value=item.get("content", "null"))
        sheet.cell(row=row_number, column=3, value=item.get("prompt_tokens", "null"))
        sheet.cell(row=row_number, column=4, value=item.get("answer_tokens", "null"))
        sheet.cell(row=row_number, column=5, value=item.get("total_tokens", "null"))
        sheet.cell(row=row_number, column=6, value=item.get("x_bc_request_id", "null"))
        sheet.cell(row=row_number, column=7, value=item.get("code", "null"))

    # 保存Excel文件
    workbook.save("{out_path}{sep}{out_file}".format(out_path=out_path,sep=os.sep,out_file=out_file))

def do_request():
    url = "https://api.baichuan-ai.com/v1/chat"
    api_key = "c1b24121c3ecaf7b5af23cf149c7d94c"
    secret_key = "XRY000t7UjqSX5liEwjyYAtwlRM="
    result_data = []

    with open(file_path, "r", encoding="utf-8") as file:
        index = 0
        for line in file:
            if index>0:break
            index+=1
            response_data = json.loads(line)
            qa_prompt = response_data.get("qa_prompt", "")
            prefix_text = " 如果答案引用了参考资料中的内容，请在答案的结尾处添加引用的id，引用的Source_id的值来自于参考资料中，并使用两个方括号包含。示例：[[d97b811489b73f46c8d2cb1bc888dbbe]]、[[b6be48868de736b90363d001c092c019]]"

            data = {
                "model": "Baichuan2-53B",
                "messages": [
                    {
                        "role": "user",
                        "content": prefix_text+qa_prompt
                    }
                ],
                "parameters": {
                    "temperature": 0.95,
                    "top_p": 0.8
                }
            }

            json_data = json.dumps(data)
            time_stamp = int(time.time())
            request_id = str(uuid.uuid4())
            signature = calculate_md5(secret_key + json_data + str(time_stamp))

            headers = {
                "Content-Type": "application/json",
                "Authorization": "Bearer " + api_key,
                "X-BC-Request-Id": request_id,
                "X-BC-Timestamp": str(time_stamp),
                "X-BC-Signature": signature,
                "X-BC-Sign-Algo": "MD5",
            }

            response = requests.post(url, data=json_data, headers=headers)

            if response.status_code == 200:
                response_json = response.json()
                x_bc_request_id = response.headers.get("X-BC-Request-Id")
                code = response_json.get("code", "")

                data_messages = response_json.get("data", {}).get("messages", [])
                content = data_messages[0].get("content", "null") if data_messages else "null"

                usage = response_json.get("usage")
                prompt_tokens = usage.get("prompt_tokens", "null") if usage else "null"
                answer_tokens = usage.get("answer_tokens", "null") if usage else "null"
                total_tokens = usage.get("total_tokens", "null") if usage else "null"

                result_data.append({
                    "qa_prompt": prefix_text+qa_prompt,
                    "content": content,
                    "prompt_tokens": prompt_tokens,
                    "answer_tokens": answer_tokens,
                    "total_tokens": total_tokens,
                    "x_bc_request_id": x_bc_request_id,
                    "code": code
                })

                if code == 0:
                    print("请求成功!")
                    print("响应header:", response.headers)
                    print("响应body:", response.text)
                else:
                    print("请求失败，code码:", code)
                    print("响应header:", response.headers)
                    print("响应body:", response.text)
            else:
                result_data.append({"qa_prompt": qa_prompt, "x_bc_request_id": "", "error": "请求失败，状态码: " + str(response.status_code), "code": "null"})
                print("请求失败，状态码:", response.status_code)

            # 等待5秒
            time.sleep(5)

    # 提取的数据写入Excel文件
    extract_and_write_to_excel(result_data)

if __name__ == "__main__":
    do_request()
