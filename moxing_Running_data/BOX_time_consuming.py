# 盒子非流式计算返回的字数/耗时
#kzy
# _*_ coding:utf-8 _*_
import requests
import time
import json
import openpyxl

# 打开文件读取信息
file_path = r"/Users/<USER>/Downloads/moxingshuju.json"  # 设置JSON文件路径（读取json，本地文件路径）

# 创建Excel工作簿和工作表
wb = openpyxl.Workbook()
sheet = wb.active

# 在要写入的Excel工作表中添加标题行
sheet.append(["choices.content", "耗时 (秒)", "字数", "结果"])

with open(file_path, "r", encoding="utf-8") as file:  # 打开JSON文件，并以UTF-8编码方式读取
    for line in file:  # 逐行读取JSON文件中的内容
        json_obj = {}  # 初始化一个空的JSON对象
        try:
            json_obj = json.loads(line)  # 尝试将当前行内容解析为JSON对象
        except json.JSONDecodeError:  # JSON解析错误时忽略当前行，因为存在无法读取的数据
            pass

        qa_prompt = json_obj.get("qa_prompt", "")  # 获取JSON对象中的qa_prompt字段的值

        # 定义API请求的URL和payload
        url = "http://10.60.13.172:8007/v1/chat/completions"
        headers = {
            "Content-Type": "application/json"
        }
        payload = {
            "model": "orionstar",
            "stream": False,  # 请求参数中指定stream为False，即非流式请求，流式为true
            "messages": [{"role": "user", "content": qa_prompt}]
        }

        # 发送请求并计时
        start_time = time.time()  # 记录请求开始时间
        response = requests.post(url, json=payload, headers=headers)  # 使用requests库发送POST请求，并传入请求参数
        end_time = time.time()  # 记录请求结束时间

        # 处理返回结果
        data = response.json()

        # 尝试获取choices.content
        choices_content = ""
        if "choices" in data and len(data["choices"]) > 0 and "message" in data["choices"][0] and "content" in data["choices"][0]["message"]:
            choices_content = data["choices"][0]["message"]["content"]

        # 统计字符数（包括汉字、单词、标点符号等）
        character_count = len(choices_content)

        # 计算耗时和结果，避免除以0
        processing_time = end_time - start_time
        result = character_count / processing_time if processing_time > 0 else 0

        # 打印结果
        print("choices.content:", choices_content)
        print("耗时:", processing_time, "秒")
        print("字数:", character_count)
        print("结果:", result)

        # 将结果写入Excel
        sheet.append([choices_content, processing_time, character_count, result])

# 保存Excel文件，设置输出的Excel文件路径
output_excel_path = r"/Users/<USER>/Downloads/results.xlsx"
wb.save(output_excel_path)
