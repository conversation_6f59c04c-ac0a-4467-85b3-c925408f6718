# 盒子—打印耗时+性能指标
# kzy
# _*_ coding:utf-8 _*_

import os
import sys

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print(BASE_DIR)
sys.path.append(BASE_DIR)
from test_conifg.config import ip, port
from tool.logger import log
import requests
import json
import time
import concurrent.futures
from datetime import datetime
import pandas as pd
from test_conifg.data.prompt import prompt
from test_conifg.path_config import PROMPT_JSON, MODEL_OUTPUT_PATH, SQL_PATH
from tool.util import get_time, get_tqdm, task_time, get_path, send_card_message
from tool.common import get_sql, load_yml, query_condition_joint
from tool.DB import DB
from tool.custom_tokenizer import get_num_tokens

sql_datas = load_yml(SQL_PATH)
batch_insert = get_sql(sql_datas, "batch_insert_model_performance")

#
# thread = 8  # 并发数
# loop = 1  # 循环次数
# task_list = [{"thread": 1, "loop": 5}, {"thread": 5, "loop": 2}, {"thread": 8, "loop": 2}]
# task_list = [{"thread": 1, "loop": 1}]
task_list = [{"thread": 3, "loop": 1}, {"thread": 4, "loop": 1}, {"thread": 5, "loop": 1}]

Conversion_ratio = 1.54  # 1个token=1.54汉字长度
env = "盒子"
GPU = "A6000*4"
scene_description = "3090测试最佳数据"
model_name = "OrionStar14Q"
# model_url = "http://*************:8007/v1/chat/completions"
#   盒子地址
# model_url = "http://*************:8007/v1/chat/completions"

#   3090
model_url = "http://*************:8007/v1/chat/completions"

headers = {
    "Content-Type": "application/json",
}

file_path = "{}{}baidu.json".format(PROMPT_JSON, os.sep)  # 设置JSON文件路径（读取json，本地文件路径）

prompt_pre = ""


def send_request(qa_prompt):
    data = {
        "model": "OrionStar14",
        "stream": True,
        "messages": [{"role": "user", "content": prompt_pre + qa_prompt}],
        "temperature": 0
    }

    # 记录请求发送的时间（精确到毫秒）
    request_send_time = time.time()
    response = requests.post(model_url, headers=headers, json=data, stream=True)

    first_packet_time = None
    total_character_count = 0
    answer = ""
    # 处理响应数据
    error = 0
    error_answer = ""
    for line in response.iter_lines():
        response_line = line.decode()
        # print("返回数据包:", response_line)
        # 只处理第二行，因为第一行是空行
        if not first_packet_time:
            # 记录第一个数据包返回的时间（精确到毫秒）
            first_packet_time = time.time()

        # 处理响应数据
        if not line.startswith(b"data: "):
            error_answer = response_line
        else:
            # if line.startswith(b"data: "):
            json_data = line[6:]
            try:
                data_dict = json.loads(json_data)
            except json.JSONDecodeError:
                data = line.decode("utf-8")
                if data.__contains__("DONE"):
                    continue
                else:
                    log.error("json loads error:->{}".format(line))
                    continue
            if 'choices' in data_dict:
                choices = data_dict['choices']
                for choice in choices:
                    if choice["finish_reason"] == "stop":
                        break
                    if 'delta' in choice and 'content' in choice['delta']:
                        content = choice['delta']['content']
                        content_length = len(content)
                        answer += content
                        total_character_count += content_length
                    else:
                        error += 1
                        if error > 1:
                            log.error("prompt : {}".format(qa_prompt))
                            log.error("choices not delta or delta not content:->{}".format(line))
            else:
                log.error("response not choices:->{}".format(line))

    # 等待整个请求完全返回
    response.close()
    # 记录请求完成的时间（精确到毫秒）
    request_complete_time = time.time()

    # 转换为易读的时分秒格式（精确到毫秒）并保留毫秒的前3位
    request_send_time_str = datetime.fromtimestamp(request_send_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    first_packet_time_str = datetime.fromtimestamp(first_packet_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    request_complete_time_str = datetime.fromtimestamp(request_complete_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]

    # 计算并打印首包耗时和总耗时
    first_packet_duration = (first_packet_time - request_send_time) * 1000
    total_duration = request_complete_time - request_send_time
    tokens_time = (request_complete_time - first_packet_time)
    # 计算性能指标（字/s)
    performance_metric = round(total_character_count / tokens_time, 2),  # 先除以1.54换算成token
    #   计算输出、输入token数

    output_token = get_num_tokens(provider="orionstar", tokenizer_name="yi", text=answer)
    input_token = get_num_tokens(provider="orionstar", tokenizer_name="yi", text=prompt_pre + qa_prompt)
    performance_token = round(output_token / tokens_time, 2),

    result = {
        "请求发送的时间": request_send_time_str,
        "第一个数据包返回的时间": first_packet_time_str,
        "请求完成的时间": request_complete_time_str,
        "首包耗时(ms)": first_packet_duration,
        "总耗时(s)": total_duration,
        "输入字数": len(prompt_pre + qa_prompt),
        "输出字数": total_character_count,
        "输入token": input_token,
        "输出token": output_token,
        "性能指标(字/s)": performance_metric,
        "推理速度(tokens/s)": performance_token,
        "prompt": qa_prompt,
        "error_answer": error_answer
    }
    return result


def save_print_to_excel(thread, loop, qa_prompt, p_len, task_id, task_name):
    results = []
    db_result = []
    output_path = get_path(prefix=MODEL_OUTPUT_PATH, dirs="box_orion")
    output_file = "Tread_{thread}_loop_{loop}_{T}_prompt_len_{p_len}_orion.xlsx".format(T=get_time(), thread=thread,
                                                                                        loop=loop, p_len=p_len)
    excel_path = "{output_path}{sep}{output_file}".format(output_path=output_path, sep=os.sep, output_file=output_file)
    with concurrent.futures.ThreadPoolExecutor(max_workers=thread) as executor:
        futures = []
        sample_count = 0
        for i in range(loop):
            with open(file_path, "r", encoding="utf-8") as file:
                lines = file.readlines()
                index = 0
                for line in lines:
                    # if index > 10: break
                    index += 1
                    json_obj = json.loads(line.strip())
                    # qa_prompt = json_obj.get("qa_prompt", "")
                    # print(qa_prompt)
                    futures.append(executor.submit(send_request, qa_prompt))
                    sample_count += 1
        tqdm_task = get_tqdm(iter_len=len(futures))
        for future in concurrent.futures.as_completed(futures):
            tqdm_task.update(1)
            try:
                result = future.result()
                res = (task_id, task_name + str(sample_count),
                       result.get("请求发送的时间"), result.get("第一个数据包返回的时间"),
                       result.get("请求完成的时间"), result.get("首包耗时(ms)"),
                       result.get("总耗时(s)"), result.get("输入字数"),
                       result.get("输出字数"), result.get("输入token"),
                       result.get("输出token"), result.get("推理速度(tokens/s)"),
                       result.get("性能指标(字/s)"), thread, env, scene_description,
                       GPU, model_name, result.get("error_answer")
                       )
                db_result.append(res)
                #   修改为处理一条直接写库
                DB.batch_insert(batch_insert, db_result)
                db_result = []
                results.append(result)
            except Exception as exc:
                print(f"Error: {exc}")
        tqdm_task.close()
    #   批量入库
    # DB.batch_insert(batch_insert, db_result)
    df = pd.DataFrame(results)
    df.to_excel(excel_path, index=False)
    return results


def insert_performance_res(max_id):
    """
    获取最近的性能测试结果,将结果整理写入到性能汇总表
    :param max_id:
    :return:
    """
    insert_summary = get_sql(sql_datas, "batch_insert_performance_summary")
    query_performance_res = get_sql(sql_datas, "query_batch_performance", key_id=max_id)
    #   查询计算出汇总结果
    db_res = DB.query_all(query_performance_res)
    summary_data = [tuple(x.values()) for x in db_res]
    #   写入汇总表
    DB.batch_insert(insert_summary, summary_data)
    return json.dumps(db_res, ensure_ascii=False)


def query_performance(start_task_id: str = "", end_task_id: str = ""):
    """

    :param start_task_id:
    :param end_task_id:
    :return:
    """
    query = get_sql(sql_datas, "query_performance_summary")
    if start_task_id and end_task_id:
        query = query_condition_joint(query, {"task_id": "between {} and {}".format(start_task_id, end_task_id)})
    elif start_task_id:
        query = query_condition_joint(query, {"task_id": ">= {}".format(start_task_id)})
    elif end_task_id:
        query = query_condition_joint(query, {"task_id": "<= {}".format(end_task_id)})

    res = DB.query_all(query)
    return res


def query_error_list(max_id):
    """
    查询没有返回answer的请求信息
    :param max_id:
    :return:
    """
    query = get_sql(sql_datas, "query_error", max_id=max_id)
    db_res = DB.query_all(query)
    return db_res


def get_max_id():
    max_id_sql = get_sql(sql_datas, "query_max_id")
    db_max_id = DB.query_one(max_id_sql)
    max_id = db_max_id.get("id")
    return max_id


if __name__ == "__main__":
    res = []
    task_time(d=10, h=0)
    #   作为临时查询条件
    start_task_id = get_time()
    for i in range(1):
        max_id = get_max_id()
        #   作为临时查询条件
        query_condition = get_time()
        log.info("{}->run {} loop".format(get_time(), i))
        for task in task_list:
            log.info("{}->run for task {}".format(get_time(), task.get("thread")))
            thread = task.get("thread")
            loop = task.get("loop")
            ids = 0
            for p in prompt:
                # if ids>=0:break
                ids += 1
                log.info("{}->run for prompt {}".format(get_time(), p.get("name")))
                task_id = get_time()
                task_name = "{name}_model_thread_{thread}_samples_".format(name=p.get("name"), thread=thread)
                prompt_text = p.get("text")
                prompt_len = p.get("p_len")
                # log.info("run prompt :->{}".format(prompt_text))
                save_print_to_excel(thread, loop, prompt_text, prompt_len, task_id, task_name)
                time.sleep(20)
        insert_performance_res(max_id)
        end_task_id = get_time()
        db_res = query_error_list(max_id)
        if db_res:
            res = "loop-{}性能测试脚本执行完成.answer为空{}条".format(i, len(db_res))
        else:
            res = "loop-{}性能测试脚本执行完成全部成功!".format(i)
        task_url = 'https://open.feishu.cn/open-apis/bot/v2/hook/bf9c14c9-5d6c-4094-9547-a957dabbcefb'
        path = "/api/performance?start_task_id={start_task_id}&end_task_id={end_task_id}".format(
            start_task_id=start_task_id, end_task_id=end_task_id)
        url = "http://{ip}:{port}{path}".format(ip=ip, port=port, path=path)
        send_card_message(title=res, down_url=url, tag="点击查看性能详情")
