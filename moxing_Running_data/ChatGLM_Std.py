#跑ChatGLM_Std模型数据综合脚本
#kzy
# _*_ coding:utf-8 _*_
import zhipuai
import json
import openpyxl

file_path = r"/Users/<USER>/Downloads/zhenge310.json"
excel_file_path = r"output_chatglm_std.xlsx"  # 指定输出的Excel文件路径

# 初始化Excel工作簿和表格
wb = openpyxl.Workbook()
ws = wb.active
ws["A1"] = "content"
ws["B1"] = "choices.content"
ws["C1"] = "code"

# 设置智拍AI的API密钥
zhipuai.api_key = "6d726d08b61c810476c8829dec0000a0.iP3p59JdxyeSbqLR"

# 创建一个空列表来存储数据
data_list = []

with open(file_path, "r", encoding="utf-8") as file:
    for line in file:
        response_data = json.loads(line)
        qa_prompt = response_data.get("qa_prompt", "")

        # 请求模型
        response = zhipuai.model_api.invoke(
            model="chatglm_std",
            prompt=[
                {"role": "user", "content": qa_prompt}
            ]
        )

        # 提取返回的数据
        choices_data = response.get('data', {}).get('choices', [])
        content = choices_data[0].get("content", "") if choices_data else ""
        code = response.get('code', "")

        # 将数据添加到列表中
        data_list.append([qa_prompt, content, code])
        print(response)
# 将数据写入Excel文件
for row_idx, data in enumerate(data_list, start=2):  # 从第二行开始写入数据
    ws.cell(row=row_idx, column=1, value=data[0])  # 写入content
    ws.cell(row=row_idx, column=2, value=data[1])  # 写入choices.content
    ws.cell(row=row_idx, column=3, value=data[2])  # 写入code

# 保存Excel文件
wb.save(excel_file_path)

print("Data written to Excel successfully!")
