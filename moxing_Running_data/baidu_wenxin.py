# 跑百度文心一言模型数据综合脚本
# kzy
# _*_ coding:utf-8 _*_
import time

import requests
import json
import os
from openpyxl import Workbook
from tool.util import get_time
from test_conifg.config import origin_data
import score

#
# API_KEY = "0aaubwpGy2LCh3yuia782ypm"
# SECRET_KEY = "kqVBQXMnNsjSbWRihHO1c8Yhl0xAHfs3"


API_KEY = "qtqgEu7MmeuflaACRqM4Gof3"
SECRET_KEY = "85C7Emkac5nS6KGp0IdXT4oH9XuR9EBg"

file_path = r"..{sep}document_file{sep}input{sep}baidu.json".format(sep=os.sep)
no_answer = "..{sep}document_file{sep}input{sep}no_answer_baidu.json".format(sep=os.sep)
output_folder = r"..{sep}document_file{sep}output{sep}baidu_wenxinyiyan{sep}".format(sep=os.sep)
v = "4.0"


def main(flag=1):
    if flag == 1:
        output_filename = "Quote_{t}_{version}.xlsx".format(t=get_time(), version=v)
    elif flag == 2:
        output_filename = "NoQuote_{t}_{version}.xlsx".format(t=get_time(), version=v)
    elif flag == 3:
        file_path = no_answer
        output_filename = "safeguard_{t}_{version}.xlsx".format(t=get_time(), version=v)
    else:
        return "未知模式,不支持"
    # output_filename = "Quote_{t}_{version}.xlsx".format(t=get_time(),version=v) if is_quote else "NoQuote_{t}_{version}.xlsx".format(t=get_time(),version=v)
    url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro?access_token=" + get_access_token()

    workbook = Workbook()
    worksheet = workbook.active
    worksheet.append(
        ["qa_prompt", "id", "object", "prompt_tokens", "completion_tokens", "total_tokens", "is_truncated", "question",
         "standard_answer", "result", "Keywords"])
    prefix_text = " 如果答案引用了参考资料中的内容，请在答案的结尾处添加引用的id，引用的Source_id的值来自于参考资料中，并使用两个方括号包含。示例：[[d97b811489b73f46c8d2cb1bc888dbbe]]、[[b6be48868de736b90363d001c092c019]]"
    safeguard = "4. 如果参考资料中的信息不足以回答问题，那么直接回答以下内容\"抱歉，没有学习到相关知识，请上传更多的文档让我学习需要的知识\""
    i = 0  # 暂时用来记录执行到第几条数据
    key_words = origin_data.get("key_word")
    question = origin_data.get("question")
    standard_answer = origin_data.get("standard_answer")
    with open(file_path, "r", encoding="utf-8") as file:
        for line in file:
            i += 1
            response_data = json.loads(line)
            qa_prompt = response_data.get("qa_prompt", "")
            if flag == 1:
                content = prefix_text + str(qa_prompt).replace("{safeguard}", safeguard)
            elif flag == 2:
                content = str(qa_prompt).replace("{safeguard}", safeguard)
            elif flag == 3:
                content = qa_prompt
            payload = json.dumps({
                "messages": [
                    {
                        "role": "user",
                        "content": content
                    }
                ]
            })
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.post(url, headers=headers, data=payload)
            response_data = response.json()
            if response_data.get("error_code"):
                print("三方接口返回报错:index:{index} {response_data}".format(index=i, response_data=response_data))
            # print("执行到第{}行数据".format(i))
            # print("Response:", response_data)  # 打印返回的结果
            id_val = response_data.get("id", "")
            object_val = response_data.get("object", "")
            #   TODO #开启引用时没有获取usage信息，后续可以和result拼接在一起
            result_val = response_data.get("result", "")
            prompt_tokens_val = response_data.get("usage", {}).get("prompt_tokens", "")
            completion_tokens_val = response_data.get("usage", {}).get("completion_tokens", "")
            total_tokens_val = response_data.get("usage", {}).get("total_tokens", "")
            is_truncated = response_data.get("is_truncated", "")
            worksheet.append(
                [content, id_val, object_val, prompt_tokens_val, completion_tokens_val, total_tokens_val, is_truncated,
                 question[i - 1], standard_answer[i - 1], result_val, key_words[i - 1]])
            time.sleep(10)

    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    output_path = os.path.join(output_folder, output_filename)
    workbook.save(output_path)
    print(f"Excel file saved at: {output_path}")
    time.sleep(10)
    #   调用模型生成结果文件后，调用score计算得分
    score.calc_score(output_filename)


def get_access_token():
    url = "https://aip.baidubce.com/oauth/2.0/token"
    params = {"grant_type": "client_credentials", "client_id": API_KEY, "client_secret": SECRET_KEY}
    return str(requests.post(url, params=params).json().get("access_token"))
