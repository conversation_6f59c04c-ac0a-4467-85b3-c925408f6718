# 获取商汤_sensen的key
# kzy
# _*_ coding:utf-8 _*_

import time
import jwt

ak = "2V6WESVqxVl2ovp6zZHRZxinoGq"      # 填写您的ak
sk = "vPJ6YoFFYrSLIpyc0h6TBynWsHA5gSAN"     # 填写您的sk

def encode_jwt_token(ak, sk):
    headers = {
        "alg": "HS256",
        "typ": "JWT"
    }
    payload = {
        "iss": ak,
        "exp": int(time.time()) + 180000,       # 填写您期望的有效时间，此处示例代表当前时间+30分钟
        "nbf": int(time.time()) - 5           # 填写您期望的生效时间，此处示例代表当前时间-5秒
    }
    token = jwt.encode(payload, sk, headers=headers)
    return token

authorization = encode_jwt_token(ak, sk)
print(authorization) # 打印生成的API_TOKEN