#跑minimax_5.5模型数据综合脚本
#kzy
# _*_ coding:utf-8 _*_
import requests
import json
import openpyxl
import time
file_path = r"/Users/<USER>/Downloads/zhenge312.json"
excel_file_path = r"output_minimax.xlsx"  # 指定输出的Excel文件路径

wb = openpyxl.Workbook()
ws = wb.active
ws["A1"] = "Text"
ws["B1"] = "Reply"
ws["C1"] = "Status Code"

with open(file_path, "r", encoding="utf-8") as file:
    for line in file:
        json_obj = {}
        try:
            json_obj = json.loads(line)
        except json.JSONDecodeError:
            pass

        qa_prompt = json_obj.get("qa_prompt", "")

        group_id = "1689660683037296"
        api_key = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

        url = f"https://api.minimax.chat/v1/text/chatcompletion?GroupId={group_id}"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": "abab5.5-chat",
            "tokens_to_generate": 8000,
            "prompt": "Answer ONLY with the facts listed in the list of CONTEXT and CHAT HISTORY below. If there isn't enough information below, just say you don't know. Don't try to make up an answer.If asking a clarifying question to the user would help, ask the question.Please do not indicate in your answer that you have referred to the CONTEXT and CHAT HISTORY.",
            "role_meta": {
                "user_name": "我",
                "bot_name": "专家"
            },
            "messages": [
                {
                    "sender_type": "USER",
                    "text": qa_prompt
                }
            ]
        }

        response = requests.post(url, headers=headers, json=payload)
        response_data = {}
        try:
            response_data = response.json()
        except json.JSONDecodeError:
            pass

        reply = response_data.get("reply", "")
        status_code = response_data.get("base_resp", {}).get("status_code", "")

        # 处理每个响应
        # print(f"Text: {qa_prompt}")
        print(f"Reply: {reply}")
        # print(f"Status Code: {status_code}")
        # print()

        # 写入Excel
        row = [qa_prompt, reply, status_code]
        ws.append(row)
        time.sleep(20)


wb.save(excel_file_path)
