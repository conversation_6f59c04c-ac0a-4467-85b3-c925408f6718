# 腾讯混元大模型批量获取结果_融合了实时获取key+获取结果的功能
# kzy
# _*_ coding:utf-8 _*_


import time
import urllib
import uuid
import json
import requests
import hmac
import hashlib
import base64
import pandas as pd

_SIGN_HOST = "hunyuan.cloud.tencent.com"
_SIGN_PATH = "hyllm/v1/chat/completions"
_URL = "https://hunyuan.cloud.tencent.com/hyllm/v1/chat/completions"


class TencentHyChat:
    def __init__(self, appid, secretid, secretkey):
        self.appid = appid
        self.secretid = secretid
        self.secretkey = secretkey
        self.messages_content = ""
        self.choices_content = ""
        self.prompt_tokens = 0
        self.completion_tokens = 0
        self.total_tokens = 0
        self.error_message = ""
        self.error_code = ""

    def run(self, content):
        request = self.__get_parm(content)
        signature = self.__gen_signature(self.__gen_sign_params(request))
        headers = {
            "Content-Type": "application/json",
            "Authorization": str(signature)
        }

        URL = _URL
        resp = requests.post(URL, headers=headers, json=request)
        response_data = resp.json()

        self.messages_content = request['messages'][0]['content']
        if response_data.get('choices') is not None:
            self.choices_content = response_data['choices'][0]['messages']['content']
            self.prompt_tokens = response_data['usage']['prompt_tokens']
            self.completion_tokens = response_data['usage']['completion_tokens']
            self.total_tokens = response_data['usage']['total_tokens']
            self.error_message = ""
            self.error_code = ""
        else:
            self.choices_content = ""
            self.prompt_tokens = 0
            self.completion_tokens = 0
            self.total_tokens = 0
            self.error_message = response_data['error']['message']
            self.error_code = response_data['error']['code']


        print(f'Choices Content: {self.choices_content}')
        print(f'Error Message: {self.error_message}')
        print(f'Error Code: {self.error_code}')
        # print(response_data)
    def __get_parm(self, content):
        timestamp = int(time.time()) + 10000
        json_data = {
            "app_id": self.appid,
            "secret_id": self.secretid,
            "query_id": "test_query_id_" + str(uuid.uuid4()),
            "messages": [
                {"role": "user", "content": content}
            ],
            "temperature": 0.95,
            "top_p": 0.8,
            "stream": 0,
            "timestamp": timestamp,
            "expired": timestamp + 24 * 60 * 60

        }
        return json_data

    def __gen_signature(self, param):
        sort_dict = sorted(param.keys())
        sign_str = _SIGN_HOST + "/" + _SIGN_PATH + "?"
        for key in sort_dict:
            sign_str = sign_str + key + "=" + str(param[key]) + '&'
        sign_str = sign_str[:-1]
        hmacstr = hmac.new(self.secretkey.encode('utf-8'),
                           sign_str.encode('utf-8'), hashlib.sha1).digest()
        s = base64.b64encode(hmacstr)
        s = s.decode('utf-8')
        return s

    def __gen_sign_params(self, data):
        params = dict()
        params['app_id'] = data["app_id"]
        params['secret_id'] = data['secret_id']
        params['query_id'] = data['query_id']
        params['temperature'] = '%g' % data['temperature']
        params['top_p'] = '%g' % data['top_p']
        params['stream'] = data["stream"]
        messagestr = ""
        for message in data["messages"]:
            content = message["content"]
            messagestr += '{"role":"' + message["role"] + '","content":"' + content + '"},'
        messagestr = messagestr.strip(",")
        params['messages'] = r"[{}]".format(messagestr)
        params['timestamp'] = str(data["timestamp"])
        params['expired'] = str(data["expired"])
        return params


if __name__ == "__main__":
    # 将AppId、SecretId、SecretKey替换为自己的即可
    AppId = 1256573505
    SecretId = "AKIDfKqAGB3iBWjkZDbrwsa9WnslV4wd8RZX"
    SecretKey = "Vr10mMl1ILAm9VhPuBE3f4HNGNMAlAQV"

    # Create empty lists to store data
    messages_content_list = []
    choices_content_list = []
    prompt_tokens_list = []
    completion_tokens_list = []
    total_tokens_list = []
    error_message_list = []
    error_code_list = []

    file_path = r"/Users/<USER>/Downloads/pyshuju/cuowu.json"
    # prompt = " 如果答案引用了参考资料中的内容，请在答案的结尾处添加引用的id，引用的Source_id的值来自于参考资料中，并使用两个方括号包含。示例：[[d97b811489b73f46c8d2cb1bc888dbbe]]、[[b6be48868de736b90363d001c092c019]]"
    with open(file_path, "r", encoding="utf-8") as file:
        for line in file:
            response_data = json.loads(line)
            qa_prompt = response_data.get("qa_prompt", "")

            qa_prompt = f'"""{qa_prompt}\n"""'

            tencent_chat = TencentHyChat(AppId, SecretId, SecretKey)
            tencent_chat.run(qa_prompt)
            time.sleep(10)

            messages_content = tencent_chat.messages_content
            choices_content = tencent_chat.choices_content
            prompt_tokens = tencent_chat.prompt_tokens
            completion_tokens = tencent_chat.completion_tokens
            total_tokens = tencent_chat.total_tokens
            error_message = tencent_chat.error_message
            error_code = tencent_chat.error_code

            messages_content_list.append(messages_content)
            choices_content_list.append(choices_content)
            prompt_tokens_list.append(prompt_tokens)
            completion_tokens_list.append(completion_tokens)
            total_tokens_list.append(total_tokens)
            error_message_list.append(error_message)
            error_code_list.append(error_code)

    data = {
        'Messages Content': messages_content_list,
        'Choices Content': choices_content_list,
        'Prompt Tokens': prompt_tokens_list,
        'Completion Tokens': completion_tokens_list,
        'Total Tokens': total_tokens_list,
        'Error Message': error_message_list,
        'Error Code': error_code_list
    }
    df = pd.DataFrame(data)

    output_path = '/Users/<USER>/Downloads/doudi_tengxun.xlsx'

    df.to_excel(output_path, index=False)

