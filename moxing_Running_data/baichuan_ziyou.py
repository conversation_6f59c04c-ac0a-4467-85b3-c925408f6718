#自有模型获取结果
#kzy
# _*_ coding:utf-8 _*_




import requests
import json
from openpyxl import Workbook

url = "http://*************:8007/v1/chat/completions"
file_path = r"/Users/<USER>/Downloads/pyshuju/budai111.json"

wb = Workbook()
ws = wb.active
ws.append(["User Content", "Assistant Content"])

with open(file_path, "r", encoding="utf-8") as file:
    for line in file:
        input_content = line.strip()

        payload = {
            "model": "OrionStarSaviorBaichuan2",
            "stream": False,
            "messages": [
                {"role": "user", "content": input_content}
            ]
        }

        headers = {
            "Content-Type": "application/json"
        }

        response = requests.post(url, json=payload, headers=headers)

        response_data = response.json()
        content = response_data["choices"][0]["message"]["content"]
        ws.append([input_content, content])

        print(response.text)

output_excel_path = "/Users/<USER>/Downloads/answer_shuruzhengchang.xlsx"
wb.save(output_excel_path)
print(f"写入成功 {output_excel_path}")
