# 统计从各种模型中跑出的结果与关键词匹配算出的分数
# kzy
# _*_ coding:utf-8 _*_
import os

import pandas as pd
import re

# Excel文件路径
file_path = '..{sep}document_file{sep}output{sep}baidu_we<PERSON><PERSON><PERSON>yan{sep}{result_file}'.format(sep=os.sep)
output_file = '..{sep}document_file{sep}output{sep}baidu_wenxinyiyan{sep}score_{result_file}'.format(sep=os.sep)

"""
1.跑分之前，先对结果文件添加key字段数据，建议生成结果文件将对应的key添加进去
2.跑分结束后，给徐鸿声结果文件，前3列数据放最后
"""


def calculate_score(content, keywords_str):
    # 拆分关键词并转换为小写
    keywords = [word.strip().lower() for word in re.split(r'[｜|]', keywords_str)]
    # 转换内容为小写，并确保content是字符串类型
    content = str(content).lower()

    # 统计匹配词数
    matched = sum(1 for keyword in keywords if keyword in content)

    # 计算得分
    total = len(keywords)
    score = matched / total if total > 0 else 0.0
    score = round(score, 2)  # 小数点后两位
    return score


def calc_score(result_file):
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path.format(result_file=result_file))

        # 计算得分并存储在'Score'列中  #寻找第一列中列名为"关键词"与"内容"进行匹配，命中多次只算一次
        df['Score'] = df.apply(lambda row: calculate_score(row['result'], row['Keywords']), axis=1)

        # 保存结果到新Excel文件
        df.to_excel(output_file.format(result_file=result_file), index=False)

        print('计算完成.xlsx')

    except FileNotFoundError:
        print(f'文件不存在: {file_path}')

    except Exception as e:
        print(f'ERROR: {e}')
