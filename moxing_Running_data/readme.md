#  BOX_time_consuming.py
    #盒子非流式计算返回的字数/耗时

#  BOX_time_liushi_consuming.py
    #计算盒子流式返回的首包时间、请求完成时间、输入+输出的总字数、性能指标

# Box_Request.py
    #盒子并发请求，调整并发数可以实现是否打满GPU

# ChatGLM-Lite.py
    #跑ChatGLM-Lite模型数据综合脚本

# ChatGLM_Std.py
    #跑ChatGLM_Std模型数据综合脚本

# Model_Merge.py
    #已知模型的整合脚本，从execute_code_1开始跑直到结束，后面有新增模型继续往下增加即可

# baichuan_53b.py
    #百川53B在没有api的情况下，抓取前端页面，实现结果获取的功能脚本

# baichuan_53b_api.py
    #跑百川模型数据脚本

# baichuan_ziyou.py
    #自有百川模型，批量获取问答结果

# baidu_wenxin.py
    #跑百度文心一言模型数据综合脚本

# box_3090_orion.py
    #盒子—打印耗时+性能指标

#  minimax_5.5.py
    #跑minimax模型数据综合脚本

# mobvoi3.py
    #序列猴子相关接口-对外版V0.2

# qianwen14b_ziyou.py
    #千问自有模型批量获取结果

# shangtang_sensen.py
    #跑商汤sensen模型批量获取结果

# shangtang_sensen_key.py
    #获取商汤_sensen的key

# tengxun_hunyun.py
    #腾讯混元大模型批量获取结果_融合了实时获取key+获取结果的功能

# score.py
    #统计从各种模型中跑出的结果与关键词匹配算出的分数

