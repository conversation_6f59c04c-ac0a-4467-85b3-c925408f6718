# 计算盒子流式返回的首包时间、请求完成时间、输入+输出的总字数、性能指标
# kzy
# _*_ coding:utf-8 _*_
import os

import requests
import time
from datetime import datetime
import json
from test_conifg.path_config import PROMPT_JSON

url = "http://10.60.13.172:8007/v1/chat/completions"
headers = {
    "Content-Type": "application/json",
}


def send_request(messages, qa_prompt_length):
    total_character_count = 0
    first_packet_time = None

    data = {
        "model": "orionstar",
        "stream": True,
        "messages": messages
    }

    # 记录请求发送的时间（精确到毫秒）
    request_send_time = time.time()

    # 发送请求
    response = requests.post(url, headers=headers, json=data, stream=True)

    # 遍历响应的每一行
    for line in response.iter_lines():
        response_line = line.decode()

        # 只处理第二行，因为第一行是空行
        if not first_packet_time:
            # 记录第一个数据包返回的时间（精确到毫秒）
            first_packet_time = time.time()

        # 处理响应数据
        if line.startswith(b"data: "):
            # 从line中取出从第6个字符开始到末尾的子串，并将其赋值给变量json_data
            json_data = line[6:]

            try:
                data_dict = json.loads(json_data)
            except json.JSONDecodeError:
                continue

            if 'choices' in data_dict:
                choices = data_dict['choices']
                for choice in choices:
                    if 'delta' in choice and 'content' in choice['delta']:
                        content = choice['delta']['content']
                        content_length = len(content)

                        total_character_count += content_length

    # 等待整个请求完全返回
    response.close()

    # 记录请求完成的时间（精确到毫秒）
    request_complete_time = time.time()

    # 转换为易读的时分秒格式（精确到毫秒）
    request_send_time_str = datetime.fromtimestamp(request_send_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    first_packet_time_str = datetime.fromtimestamp(first_packet_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    request_complete_time_str = datetime.fromtimestamp(request_complete_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]

    # 打印请求发送的时间，第一个数据包返回的时间，和请求完成的时间
    print("请求发送的时间:", request_send_time_str)
    print("第一个数据包返回的时间:", first_packet_time_str)
    print("请求完成的时间:", request_complete_time_str)

    # 计算并打印首包耗时和总耗时
    first_packet_duration = first_packet_time - request_send_time
    total_duration = request_complete_time - request_send_time

    print("首包耗时: {:.3f} 秒".format(first_packet_duration))
    print("总耗时: {:.3f} 秒".format(total_duration))
    # 打印字的总个数
    print(f"总字数（输出+输入）: {total_character_count}+{qa_prompt_length}")
    # 计算性能指标
    performance_metric = (total_character_count + qa_prompt_length) / total_duration
    print("性能指标: {:.3f} 字/秒".format(performance_metric))
    print("*" * 50)


def process_file(file_path):
    # 生成器，用于逐行读取数据
    def read_lines(file_path):
        with open(file_path, "r", encoding="utf-8") as file:
            for line in file:
                yield line.strip()  # 使用yield返回每一行数据，strip()去除行尾的换行符

    # 打开文件读取信息
    lines_generator = read_lines(file_path)

    # 遍历每一行数据
    index = 0
    for line in lines_generator:
        if index >= 1: break
        index += 1
        if not line:
            continue

        json_obj = {}  # 初始化一个空的JSON对象
        try:
            json_obj = json.loads(line)  # 尝试将当前行内容解析为JSON对象
        except json.JSONDecodeError:  # JSON解析错误时忽略当前行，因为存在无法读取的数据
            continue

        qa_prompt = json_obj.get("qa_prompt", "")  # 获取JSON对象中的qa_prompt字段的值
        messages = [{"role": "user", "content": qa_prompt}]
        qa_prompt_length = len(qa_prompt)
        print(qa_prompt)

        # 发送请求
        send_request(messages, qa_prompt_length)


if __name__ == "__main__":
    file_path = "{}{}baidu.json".format(PROMPT_JSON, os.sep)  # 设置JSON文件路径（读取json，本地文件路径）
    process_file(file_path)
