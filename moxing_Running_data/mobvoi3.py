#序列猴子相关接口-对外版V0.2
#kzy
# _*_ coding:utf-8 _*_
import hashlib
import json
import requests
import time

# 设置输入文件和输出文件的路径
# 设置输入文件和输出文件的路径
ips_file = "/Users/<USER>/Downloads/pyshuju/tongyiqianwen.json"
opt_file = "/Users/<USER>/Downloads/pyshuju/tongyiqianwen111.json"



# 生成签名
def generate_signature(appkey, secret, timestamp):
    raw_str = appkey + "+" + secret + "+" + timestamp
    # raw_str = appkey + secret + timestamp
    md5 = hashlib.md5()
    md5.update(raw_str.encode('utf-8'))
    return md5.hexdigest()


# 调用聊天 API
def call_chat_api(appkey, secret, model, messages, temperature=0.93, top_p=0.98, top_k=200,
                  repetition_penalty=1.02, max_tokens=1024):
    url = 'https://open.mobvoi.com/api/chat/v1/chat'
    timestamp = str(int(time.time()))
    signature = generate_signature(appkey, secret, timestamp)
    headers = {'Content-Type': 'application/json'}
    payload = {
        'appkey': appkey,
        'signature': signature,
        'timestamp': timestamp,
        'model': model,
        'messages': messages,
        'temperature': temperature,
        'top_p': top_p,
        'top_k': top_k,
        'repetition_penalty': repetition_penalty,
        'max_tokens': max_tokens
    }
    response = requests.post(url, headers=headers, json=payload)

    # 处理不同的响应状态码
    if response.status_code == 400:
        return ""
    if response.status_code == 504:
        return "服务器错误"
    if response.status_code == 500:
        return "服务器错误"

    # 解析响应数据
    response_data = response.json()

    if "choices" in response_data:
        choices = response_data["choices"]
        for choice in choices:
            finish_reason = choice["finish_reason"]
            index = choice["index"]
            message = choice["message"]
            content = message["content"]
            print(f"Finish Reason: {finish_reason}")
            print(f"Index: {index}")
            print(f"Content: {content}")
            return content
    return ""


# 设置 API 相关参数
appkey = '9508C3E51C0A037931FD935431413956'
secret = 'D66B5C6172D33C62B0FC5E3C3690E617'
model = 'uclai-large'

ans = []  # 用于存储答案的列表

# 读取输入文件并逐行处理
with open(ips_file) as f:
    for line in f:
        line = line.strip()  # 去除不可见字符
        if not line:
            continue  # 忽略空行
        keyword = line  # 使用整行文本作为关键字
        messages = [
            {"role": "user", "content": keyword}
        ]
        res = call_chat_api(appkey, secret, model, messages)
        print("q ", keyword, " ans ", res)
        ans.append({'question': keyword, 'answer': res})




# 将结果写入输出文件
with open(opt_file, "w", encoding='utf-8') as f:
    json.dump(ans, f, ensure_ascii=False)
