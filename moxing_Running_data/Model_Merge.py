#已知模型的整合脚本，从execute_code_1开始跑直到结束，后面有新增模型继续往下增加即可
#kzy
# _*_ coding:utf-8 _*_
import requests
import json
import openpyxl
import time
import zhipuai

#minimax5.5
def execute_code_1():
    file_path = r"/Users/<USER>/Downloads/zhenge312.json"
    excel_file_path = r"output_minimax5.5_daiyinyong.xlsx"  # 指定输出的Excel文件路径

    wb = openpyxl.Workbook()
    ws = wb.active
    ws["A1"] = "Text"
    ws["B1"] = "Reply"
    ws["C1"] = "Status Code"

    with open(file_path, "r", encoding="utf-8") as file:
        for line in file:
            json_obj = {}             # 初始化一个空字典，用于存储JSON数据
            try:
                json_obj = json.loads(line)                  # 尝试将行解析为JSON，并将其存储在json_obj中
            except json.JSONDecodeError:
                pass



            # 从JSON数据中获取与"qa_prompt"键相关联的值
            qa_prompt = json_obj.get("qa_prompt", "")

            group_id = "1689660683037296"
            api_key = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

            url = f"https://api.minimax.chat/v1/text/chatcompletion?GroupId={group_id}"
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            payload = {
                "model": "abab5.5-chat",
                "tokens_to_generate": 8000,
                "prompt": "Answer ONLY with the facts listed in the list of CONTEXT and CHAT HISTORY below. If there isn't enough information below, just say you don't know. Don't try to make up an answer.If asking a clarifying question to the user would help, ask the question.Please do not indicate in your answer that you have referred to the CONTEXT and CHAT HISTORY.",
                "role_meta": {
                    "user_name": "我",
                    "bot_name": "专家"
                },
                "messages": [
                    {
                        "sender_type": "USER",
                        "text": qa_prompt
                    }
                ]
            }

            response = requests.post(url, headers=headers, json=payload)
            response_data = {}
            try:
                response_data = response.json()
            except json.JSONDecodeError:
                pass

            # 从API响应中获取"reply"和"status_code"的值。
            reply = response_data.get("reply", "")
            status_code = response_data.get("base_resp", {}).get("status_code", "")

            # 处理每个响应
            # print(f"Text: {qa_prompt}")
            print(f"Reply: {reply}")
            # print(f"Status Code: {status_code}")
            # print()

            # 写入Excel
            row = [qa_prompt, reply, status_code]
            ws.append(row)
            time.sleep(20)

    wb.save(excel_file_path)
    print("Data written to output_minimax5.5 Excel successfully!")

#minimax5
def execute_code_2():
    file_path = r"/Users/<USER>/Downloads/zhenge312.json"
    excel_file_path = r"output_minimax5_daiyinyong.xlsx"  # 指定输出的Excel文件路径

    wb = openpyxl.Workbook()
    ws = wb.active
    ws["A1"] = "Text"
    ws["B1"] = "Reply"
    ws["C1"] = "Status Code"

    with open(file_path, "r", encoding="utf-8") as file:
        for line in file:
            json_obj = {}
            try:
                json_obj = json.loads(line)
            except json.JSONDecodeError:
                pass

            qa_prompt = json_obj.get("qa_prompt", "")

            group_id = "1689660683037296"
            api_key = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

            url = f"https://api.minimax.chat/v1/text/chatcompletion?GroupId={group_id}"
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            payload = {
                "model": "abab5-chat",
                "tokens_to_generate": 1800,
                "prompt": "Answer ONLY with the facts listed in the list of CONTEXT and CHAT HISTORY below. If there isn't enough information below, just say you don't know. Don't try to make up an answer.If asking a clarifying question to the user would help, ask the question.Please do not indicate in your answer that you have referred to the CONTEXT and CHAT HISTORY.",
                "role_meta": {
                    "user_name": "我",
                    "bot_name": "专家"
                },
                "messages": [
                    {
                        "sender_type": "USER",
                        "text": qa_prompt
                    }
                ]
            }

            response = requests.post(url, headers=headers, json=payload)
            response_data = {}
            try:
                response_data = response.json()
            except json.JSONDecodeError:
                pass

            reply = response_data.get("reply", "")
            status_code = response_data.get("base_resp", {}).get("status_code", "")

            # 处理每个响应
            # print(f"Text: {qa_prompt}")
            print(f"Reply: {reply}")
            # print(f"Status Code: {status_code}")
            # print()

            # 写入Excel
            row = [qa_prompt, reply, status_code]
            ws.append(row)
            time.sleep(20)

    wb.save(excel_file_path)
    print("Data written to output_minimax5 Excel successfully!")

#minimax5.5_不带引用
def execute_code_3():
    file_path = r"/Users/<USER>/Downloads/budaiyinyong.json"
    excel_file_path = r"output_minimax5.5_budaiyinyong.xlsx"  # 指定输出的Excel文件路径

    wb = openpyxl.Workbook()
    ws = wb.active
    ws["A1"] = "Text"
    ws["B1"] = "Reply"
    ws["C1"] = "Status Code"

    with open(file_path, "r", encoding="utf-8") as file:
        for line in file:
            json_obj = {}
            try:
                json_obj = json.loads(line)
            except json.JSONDecodeError:
                pass

            qa_prompt = json_obj.get("qa_prompt", "")

            group_id = "1689660683037296"
            api_key = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

            url = f"https://api.minimax.chat/v1/text/chatcompletion?GroupId={group_id}"
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            payload = {
                "model": "abab5.5-chat",
                "tokens_to_generate": 8000,
                "prompt": "Answer ONLY with the facts listed in the list of CONTEXT and CHAT HISTORY below. If there isn't enough information below, just say you don't know. Don't try to make up an answer.If asking a clarifying question to the user would help, ask the question.Please do not indicate in your answer that you have referred to the CONTEXT and CHAT HISTORY.",
                "role_meta": {
                    "user_name": "我",
                    "bot_name": "专家"
                },
                "messages": [
                    {
                        "sender_type": "USER",
                        "text": qa_prompt
                    }
                ]
            }

            response = requests.post(url, headers=headers, json=payload)
            response_data = {}
            try:
                response_data = response.json()
            except json.JSONDecodeError:
                pass

            reply = response_data.get("reply", "")
            status_code = response_data.get("base_resp", {}).get("status_code", "")

            # 处理每个响应
            # print(f"Text: {qa_prompt}")
            print(f"Reply: {reply}")
            # print(f"Status Code: {status_code}")
            # print()

            # 写入Excel
            row = [qa_prompt, reply, status_code]
            ws.append(row)
            time.sleep(20)

    wb.save(excel_file_path)
    print("Data written to output_minimax5.5_budaiyinyong Excel successfully!")

#minimax5_不带引用
def execute_code_4():
    file_path = r"/Users/<USER>/Downloads/budaiyinyong.json"
    excel_file_path = r"output_minimax5_budaiyinyong.xlsx"  # 指定输出的Excel文件路径

    wb = openpyxl.Workbook()
    ws = wb.active
    ws["A1"] = "Text"
    ws["B1"] = "Reply"
    ws["C1"] = "Status Code"

    with open(file_path, "r", encoding="utf-8") as file:
        for line in file:
            json_obj = {}
            try:
                json_obj = json.loads(line)
            except json.JSONDecodeError:
                pass

            qa_prompt = json_obj.get("qa_prompt", "")

            group_id = "1689660683037296"
            api_key = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

            url = f"https://api.minimax.chat/v1/text/chatcompletion?GroupId={group_id}"
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            payload = {
                "model": "abab5-chat",
                "tokens_to_generate": 1800,
                "prompt": "Answer ONLY with the facts listed in the list of CONTEXT and CHAT HISTORY below. If there isn't enough information below, just say you don't know. Don't try to make up an answer.If asking a clarifying question to the user would help, ask the question.Please do not indicate in your answer that you have referred to the CONTEXT and CHAT HISTORY.",
                "role_meta": {
                    "user_name": "我",
                    "bot_name": "专家"
                },
                "messages": [
                    {
                        "sender_type": "USER",
                        "text": qa_prompt
                    }
                ]
            }

            response = requests.post(url, headers=headers, json=payload)
            response_data = {}
            try:
                response_data = response.json()
            except json.JSONDecodeError:
                pass

            reply = response_data.get("reply", "")
            status_code = response_data.get("base_resp", {}).get("status_code", "")

            # 处理每个响应
            # print(f"Text: {qa_prompt}")
            print(f"Reply: {reply}")
            # print(f"Status Code: {status_code}")
            # print()

            # 写入Excel
            row = [qa_prompt, reply, status_code]
            ws.append(row)
            time.sleep(20)

    wb.save(excel_file_path)
    print("Data written to output_minimax5_budaiyinyong Excel successfully!")

#chatglm_std
def execute_code_5():
    file_path = r"/Users/<USER>/Downloads/zhenge312.json"
    excel_file_path = r"output_chatglm_std_daiyinyong.xlsx"  # 指定输出的Excel文件路径

    # 初始化Excel工作簿和表格
    wb = openpyxl.Workbook()
    ws = wb.active
    ws["A1"] = "content"
    ws["B1"] = "choices.content"
    ws["C1"] = "code"

    # 设置智拍AI的API密钥
    zhipuai.api_key = "6d726d08b61c810476c8829dec0000a0.iP3p59JdxyeSbqLR"

    # 创建一个空列表来存储数据
    data_list = []

    with open(file_path, "r", encoding="utf-8") as file:
        for line in file:
            response_data = json.loads(line)
            qa_prompt = response_data.get("qa_prompt", "")

            # 请求模型
            response = zhipuai.model_api.invoke(
                model="chatglm_std",
                prompt=[
                    {"role": "user", "content": qa_prompt}
                ]
            )

            # 提取返回的数据
            choices_data = response.get('data', {}).get('choices', [])
            content = choices_data[0].get("content", "") if choices_data else ""
            code = response.get('code', "")

            # 将数据添加到列表中
            data_list.append([qa_prompt, content, code])
            print(response)
    # 将数据写入Excel文件
    for row_idx, data in enumerate(data_list, start=2):  # 从第二行开始写入数据
        ws.cell(row=row_idx, column=1, value=data[0])  # 写入content
        ws.cell(row=row_idx, column=2, value=data[1])  # 写入choices.content
        ws.cell(row=row_idx, column=3, value=data[2])  # 写入code

    # 保存Excel文件
    wb.save(excel_file_path)
    print("Data written to output_chatglm_std Excel successfully!")


#chatglm_lite
def execute_code_6():
    file_path = r"/Users/<USER>/Downloads/zhenge.json"
    excel_file_path = r"output_chatglm_lite_dai.xlsx"  # 指定输出的Excel文件路径

    # 初始化Excel工作簿和表格
    wb = openpyxl.Workbook()
    ws = wb.active
    ws["A1"] = "content"
    ws["B1"] = "choices.content"
    ws["C1"] = "code"

    # 设置智拍AI的API密钥
    zhipuai.api_key = "6d726d08b61c810476c8829dec0000a0.iP3p59JdxyeSbqLR"

    # 创建一个空列表来存储数据
    data_list = []

    with open(file_path, "r", encoding="utf-8") as file:
        for line in file:
            response_data = json.loads(line)
            qa_prompt = response_data.get("qa_prompt", "")

            # 请求模型
            response = zhipuai.model_api.invoke(
                model="chatglm_lite",
                prompt=[
                    {"role": "user", "content": qa_prompt}
                ]
            )

            # 提取返回的数据
            choices_data = response.get('data', {}).get('choices', [])
            content = choices_data[0].get("content", "") if choices_data else ""
            code = response.get('code', "")

            # 将数据添加到列表中
            data_list.append([qa_prompt, content, code])
            print(response)
    # 将数据写入Excel文件
    for row_idx, data in enumerate(data_list, start=2):  # 从第二行开始写入数据
        ws.cell(row=row_idx, column=1, value=data[0])  # 写入content
        ws.cell(row=row_idx, column=2, value=data[1])  # 写入choices.content
        ws.cell(row=row_idx, column=3, value=data[2])  # 写入code

    # 保存Excel文件
    wb.save(excel_file_path)
    print("Data written to output_chatglm_lite Excel successfully!")

if __name__ == "__main__":
    execute_code_1()
    execute_code_2()
    execute_code_3()
    execute_code_4()
    execute_code_5()
    execute_code_6()