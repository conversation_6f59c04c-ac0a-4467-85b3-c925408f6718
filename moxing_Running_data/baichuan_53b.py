#百川53B在没有api的情况下，抓取前端页面，实现结果获取的功能脚本
#kzy
# _*_ coding:utf-8 _*_
import os

import requests
import json
import openpyxl
from tool.util import get_time,get_path
from test_conifg.path_config import DOCUMENT_PATH,MODEL_OUTPUT_PATH,PROMPT_JSON
thread=1
loop=1
output_path = get_path(prefix=MODEL_OUTPUT_PATH,dirs="baichaun")
output_file = "Tread_{thread}_loop_{loop}_{T}_baichuan_53b.xlsx".format(T=get_time(),thread=thread,loop=loop)
input_file = '{prefix}{sep}baidu.json'.format(prefix=DOCUMENT_PATH,sep=os.sep)
url = 'https://www.baichuan-ai.com/api/chat/v1/chat'
headers = {
    'Accept': '*/*',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Connection': 'keep-alive',
    'Content-Type': 'application/json',
    'Cookie': '_bl_uid=5FlbwlanzwCzgpypRqU53sL6eqv2; aliyungf_tc=fbc0c805513e8fd12516682b95839343c22925ba2c3347a330ac1878e6da29f7; next-auth.csrf-token=56a7e45b6edf00b20b79d33b87e9c55ab188ae94030ba2baeba06c16893dbf2f%7C30013af3f81a8e5c3e6e76422653a79425a9d228bfe26823a7bd40f192d0f525; acw_tc=63b9532716958199674351008cced8e115b2f0826f86ee5fd7cce3634f7065; next-auth.callback-url=https%3A%2F%2Fchat.baichuan-ai.com; __bc_uid=ec729e5f2e595ef426a08ad8205fb99b49a35a4e747ad53dcc0966b5a49d005b; next-auth.session-token=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIn0..WkbYyMnFUQqrz3HJ._LNxOSRuSCEGRSmzIa5Ij4QNti3ybm_lIpLJK5co7amQLlhPW4MeGdusz1ge-b9EuY1u7J7LbvMwbSA9RKf0U3eA-BGGayrkhD9CukruidGR7CN6eP4WQ7QTBmO22jQq_-ATjS8fCkaRRkfEwd7nc6jDxwkDVwwsr8yhvpp6-JgQGXbdP_a0eQ.HJwcRbIa5KLp_vFpXZ4E2w',
    'EagleEye-SessionID': '9zldsn0L1ahrepfetc1m6z5e1b7L',
    'EagleEye-TraceID': '63b9532716958199806901017cced8',
    'EagleEye-pAppName': 'bc7akk1cwt@b6e253f842cced8',
    'Origin': 'https://www.baichuan-ai.com',
    'Referer': 'https://www.baichuan-ai.com/chat?from=%2Fhome',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'sec-ch-ua': '"Google Chrome";v="117", "Not;A=Brand";v="8", "Chromium";v="117"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': 'macOS',
    'x-requested-with': 'XMLHttpRequest',
}

# 打开 JSON 文件并逐行读取和处理
with open(input_file, 'r') as json_file:
    # 创建一个新的Excel工作簿
    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    index = 0
    for line in json_file:
        if index>0:break
        index+=1
        try:
            data = json.loads(line)

            if 'qa_prompt' in data:
                request_data = {
                    'request_id': '00151e6f-0c63-461d-95bb-061297018c21',
                    'stream': True,
                    'prompt': {
                        'id': 'U42ef009QDphpRo',
                        'data': data['qa_prompt'],
                        'from': 0,
                        'parent_id': 'M5983009Q5pYpsK',
                        'created_at': 1695707503922
                    },
                    'app_info': {
                        'id': 10001,
                        'name': 'baichuan_web'
                    },
                    'user_info': {
                        'id': 1871,
                        'status': 1
                    },
                    'session_info': {
                        'id': 'pe653009Qly',
                        'name': '你好',
                        'created_at': 1695707478056
                    },
                    'assistant_info': {},
                    'parameters': {
                        'repetition_penalty': -1,
                        'temperature': -1,
                        'top_k': -1,
                        'top_p': -1,
                        'max_new_tokens': -1,
                        'do_sample': -1,
                        'regenerate': 0
                    },
                    'history': [
                    ]
                }

                response = requests.post(url, headers=headers, json=request_data, stream=True)

                # 初始化用于拼接 answer.data 的变量
                concatenated_response = ""

                for chunk in response.iter_lines(chunk_size=1024):
                    if chunk:
                        # 尝试解析 JSON 数据
                        try:
                            json_data = json.loads(chunk.decode('ISO-8859-1'))

                            json_data = json.loads(chunk.decode('utf-8'))
                            answer_data = json_data.get("answer", {}).get("data", "")
                            if answer_data:
                                concatenated_response += answer_data
                        except json.JSONDecodeError:
                            pass

                worksheet.append([data['qa_prompt'], concatenated_response])

                # print("qa_prompt:", data['qa_prompt'])
                print("concatenated_response:", concatenated_response)

        except json.JSONDecodeError:
            print("Error parsing JSON:", line)

    workbook.save("{output_path}{sep}{output_file}".format(output_path=output_path,sep=os.sep,output_file=output_file))
