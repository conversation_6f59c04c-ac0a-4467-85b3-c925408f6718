# 盒子并发请求，调整并发数可以实现是否打满GPU
#kzy
# _*_ coding:utf-8 _*_
import requests
import json
import concurrent.futures
import time

# 设置请求参数
url = "http://10.60.13.172:8007/v1/chat/completions"
headers = {
    "Content-Type": "application/json"
}
payload = {
    "model": "orionstar",
    "stream": True,
    "messages": [{"role": "user", "content": "世界第一台摩托车在哪一年哪个国家问世"}]
}

# 发送请求的函数，注意要使用纯文本读取不然会报错，因为我们不是标准的json格式
def send_request():
    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        response.raise_for_status()
        result = response.text
        print("请求成功:", result)
        return result
    except requests.exceptions.RequestException as e:  # 处理请求异常
        print("请求失败:", str(e))
        return {"error": str(e)}

# 主函数
def main():
    run_hours = 10  # 设置运行时长为10小时
    end_time = time.time() + run_hours * 3600  # 计算结束时间，当前时间加上运行时长的秒数

    num_concurrent_requests = 10  # 设置并发数为10

    with concurrent.futures.ThreadPoolExecutor(max_workers=num_concurrent_requests) as executor:  # 创建线程池执行器，最大线程数为并发数
        futures = {executor.submit(send_request) for _ in range(num_concurrent_requests)}  # 提交并发请求任务给线程池

        concurrent.futures.wait(futures)  # 等待并发请求完成

        # 持续运行直到指定时长
        while time.time() < end_time:
            new_futures = {executor.submit(send_request) for _ in range(num_concurrent_requests)}
            concurrent.futures.wait(new_futures)

if __name__ == "__main__":
    main()
