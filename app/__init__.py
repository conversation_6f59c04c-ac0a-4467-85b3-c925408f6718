# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : __init__.py.py
<AUTHOR> <EMAIL>
@Time   : 2023-10-30 10:21:14
@Version: v1.0
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_apscheduler import APScheduler
# from flask_compress import Compress
# from config.job.conf import TaskConfig, MYSQL_URL
# from settings.config import POOL
import os
# from utils.models import db
# compress = Compress()
root_path = os.path.abspath(os.path.dirname(__file__))
root = os.path.split(root_path)[0]
templates_dir = os.path.join(root, 'templates')
print("root_path:{}".format(root))


def create_app(config_filename):
    # 获取项目根目录
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    app = Flask(
        __name__,
        template_folder=templates_dir,
        static_folder=os.path.join(project_root, 'static')
    )
    app.template_folder = templates_dir
    # compress.init_app(app)
    app.config.from_pyfile(config_filename)
    print(f"app.config: {app.config}")
    print(f"app.static_folder: {app.static_folder}")
    # app.config['SQLALCHEMY_DATABASE_URI'] = MYSQL_URL
    # app.config['SQLALCHEMY_COMMIT_ON_TEARDOWN'] = True
    # app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    # app.config.from_object(TaskConfig())
    # app.config.from_object(POOL)
    # db_model(app)
    # with app.app_context():
    #     #     db.create_all()

    from app.main import main as main
    from app.main import root as root
    from app.main import translation
    app.register_blueprint(main, url_prefix="/api")
    app.register_blueprint(translation)

    return app


def create_scheduler(app):
    # 初始化scheduler
    scheduler = APScheduler()
    scheduler.init_app(app)
    return scheduler


def create_db_model(app):
    # 初始化db_model
    db = SQLAlchemy(app)
    with app.app_context():
        db.create_all()
    return db
