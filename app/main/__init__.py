# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : __init__.py.py
<AUTHOR> <EMAIL>
@Time   : 2023-10-30 10:25:21
@Version: v1.0
"""


from flask import Blueprint, render_template
from .translation_views import translation_page, translate_text, add_corpus, update_corpus, delete_corpus

main = Blueprint('main', __name__)
root = Blueprint('root', __name__)
translation = Blueprint('translation', __name__)

translation.route('/translation/')(translation_page)
translation.route('/api/translate/', methods=['POST'])(translate_text)
translation.route('/api/corpus/add/', methods=['POST'])(add_corpus)
translation.route('/api/corpus/<int:corpus_id>/update/', methods=['POST'])(update_corpus)
translation.route('/api/corpus/<int:corpus_id>/delete/', methods=['POST'])(delete_corpus)
from . import views, template_view