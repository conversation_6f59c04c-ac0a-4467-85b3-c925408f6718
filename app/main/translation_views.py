from flask import render_template, request, jsonify
from deep_translator import GoogleTranslator
import os
import json
from datetime import datetime
import asyncio
import edge_tts
from gtts import gTTS
from filelock import FileLock

# 确保音频文件存储目录存在
AUDIO_DIR = os.path.join('static', 'audio')
os.makedirs(AUDIO_DIR, exist_ok=True)

# 语料数据文件存储在static目录下
DATA_FILE = os.path.join('static', 'corpus_data.json')
LOCK_FILE = DATA_FILE + '.lock'


def load_corpus():
    with FileLock(LOCK_FILE):
        if os.path.exists(DATA_FILE):
            with open(DATA_FILE, 'r', encoding='utf-8') as f:
                try:
                    return json.load(f)
                except Exception:
                    return []
        return []


def save_corpus(corpus_list):
    with FileLock(LOCK_FILE):
        with open(DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(corpus_list, f, ensure_ascii=False, indent=2)


# 启动时加载本地语料
corpus_list = load_corpus()


def get_corpus_by_id(cid):
    corpus_list = load_corpus()
    return next((c for c in corpus_list if c['id'] == cid), None)


def generate_tts(text, lang, audio_path, tts_type='gtts'):
    if tts_type == 'edge':
        lang_voice = {
            'zh': 'zh-CN-XiaoxiaoNeural',
            'en': 'en-US-AriaNeural',
            'ja': 'ja-JP-NanamiNeural'
        }
        voice = lang_voice.get(lang[:2], 'en-US-AriaNeural')

        async def edge_speak():
            communicate = edge_tts.Communicate(text, voice)
            await communicate.save(audio_path)
        asyncio.run(edge_speak())
    else:
        tts = gTTS(text=text, lang=lang)
        tts.save(audio_path)


def auto_translate_and_audio(corpus, langs=['en', 'ja'], tts_type='gtts'):
    for lang in langs:
        translation = GoogleTranslator(source='auto', target=lang).translate(corpus['text'])
        corpus.setdefault('translations', {})[lang] = translation
        audio_filename = f"corpus_{corpus['id']}_{lang}_{tts_type}.mp3"
        audio_rel_path = f"audio/{audio_filename}"
        audio_path = os.path.join('static', 'audio', audio_filename)
        audio_key = f"{lang}_{tts_type}"
        if not os.path.exists(audio_path):
            generate_tts(translation, lang, audio_path, tts_type)
        corpus.setdefault('audio', {})[audio_key] = audio_rel_path


def translation_page():
    """翻译页面"""
    corpus_list = load_corpus()
    return render_template('translation.html', corpus_list=corpus_list)


def translate_text():
    """翻译文本并生成语音"""
    if request.method == 'POST':
        data = request.get_json()
        text = data.get('text')
        target_lang = data.get('lang')
        cid = data.get('id')
        tts_type = data.get('tts_type', 'gtts')
        corpus_list = load_corpus()
        corpus = None
        if cid is not None:
            corpus = next((c for c in corpus_list if c['id'] == cid), None)
        if corpus is None:
            corpus = {'id': None, 'text': text, 'audio': {}, 'translations': {}}
        translation = None
        audio_key = f"{target_lang}_{tts_type}"
        if audio_key in corpus.get('audio', {}) and os.path.exists(os.path.join('static', corpus['audio'][audio_key])):
            translation = corpus.get('translations', {}).get(target_lang)
            if not translation:
                translation = GoogleTranslator(source='auto', target=target_lang).translate(text)
                corpus.setdefault('translations', {})[target_lang] = translation
                if corpus.get('id') is not None:
                    save_corpus(corpus_list)
            audio_rel_path = corpus['audio'][audio_key]
            audio_path = os.path.join('static', audio_rel_path)
            if not os.path.exists(audio_path):
                generate_tts(translation, target_lang, audio_path, tts_type)
        else:
            translation = GoogleTranslator(source='auto', target=target_lang).translate(text)
            audio_filename = f"corpus_{corpus.get('id', 'tmp')}_{target_lang}_{tts_type}.mp3"
            audio_rel_path = f"audio/{audio_filename}"
            audio_path = os.path.join('static', 'audio', audio_filename)
            generate_tts(translation, target_lang, audio_path, tts_type)
            corpus.setdefault('audio', {})[audio_key] = audio_rel_path
            corpus.setdefault('translations', {})[target_lang] = translation
            if corpus.get('id') is not None:
                save_corpus(corpus_list)
        return jsonify({
            'translation': translation,
            'audio_url': f'/static/{audio_rel_path}'
        })
    return jsonify({'error': 'Invalid request method'}), 400


def add_corpus():
    """添加新的语料并自动翻译和生成音频"""
    if request.method == 'POST':
        data = request.get_json()
        text = data.get('text')
        tts_type = data.get('tts_type', 'gtts')
        if text:
            corpus_list = load_corpus()
            corpus = {
                'id': len(corpus_list),
                'text': text,
                'created_at': datetime.now().isoformat(),
                'audio': {},
                'translations': {}
            }
            corpus_list.append(corpus)
            auto_translate_and_audio(corpus, langs=['en', 'ja'], tts_type=tts_type)
            save_corpus(corpus_list)
            return jsonify(corpus)
    return jsonify({'error': 'Invalid request method'}), 400


def update_corpus(corpus_id):
    """更新语料"""
    if request.method == 'POST':
        data = request.get_json()
        text = data.get('text')
        tts_type = data.get('tts_type', 'gtts')
        try:
            corpus_list = load_corpus()
            corpus = next((c for c in corpus_list if c['id'] == corpus_id), None)
            if corpus:
                corpus['text'] = text
                corpus['audio'] = {}
                corpus['translations'] = {}
                auto_translate_and_audio(corpus, langs=['en', 'ja'], tts_type=tts_type)
                save_corpus(corpus_list)
                return jsonify({'success': True})
            return jsonify({'error': 'Corpus not found'}), 404
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    return jsonify({'error': 'Invalid request method'}), 400


def delete_corpus(corpus_id):
    """删除指定id的语料，并删除相关音频文件"""
    if request.method == 'POST':
        corpus_list = load_corpus()
        new_list = [c for c in corpus_list if c['id'] != corpus_id]
        deleted = [c for c in corpus_list if c['id'] == corpus_id]
        if deleted:
            # 删除所有关联的mp3文件
            audio_dict = deleted[0].get('audio', {})
            for rel_path in audio_dict.values():
                abs_path = os.path.join('static', rel_path)
                if os.path.exists(abs_path):
                    try:
                        os.remove(abs_path)
                    except Exception:
                        pass
        if len(new_list) != len(corpus_list):
            # 重新分配id，保证id连续
            for idx, c in enumerate(new_list):
                c['id'] = idx
            save_corpus(new_list)
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'Not found'})
    return jsonify({'success': False, 'error': 'Invalid request method'}), 400
