# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : template_view.py
<AUTHOR> <EMAIL>
@Time   : 2023-12-23 16:52:01
@Version: v1.0
"""
from . import main, render_template
from moxing_Running_data.box_3090_orion import query_performance
from tool.common import api_response, get_request_params, get_upload_param


@main.route('performance', methods=['GET'])
def get_performance_summary():
    arguments = get_request_params()
    start_task_id = arguments.get('start_task_id', "")
    end_task_id = arguments.get('end_task_id', "")
    performance_res = query_performance(start_task_id=start_task_id, end_task_id=end_task_id)

    return render_template("performance.html", data=performance_res)
