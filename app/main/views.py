# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : views.py
<AUTHOR> <EMAIL>
@Time   : 2023-10-30 10:25:58
@Version: v1.0
"""
from . import main
from tool.common import api_response, get_request_params, get_upload_param
from tool.decorators import *
from services.sensitive.check_sensitive_for_gen import sensitive_check_task
from services.up_down_load.file_load import upload_file, download_file, upload_shumei
from services.generation_answer.generation_answer import request
from services.jenkins_task import jenkins_task
from tool.feishu import add_feishu_record, add_feishu_record_by_table


@main.route('/sensitive/check_gen', methods=['POST'])
@response_format
def check_sensitive_for_gen():
    arguments = get_request_params()
    sensitive_check_task()
    return api_response()


@main.route("/upload_check/<task_name>", methods=["POST"])
@response_format
def upload(task_name):
    if task_name not in {"gen", "qa", "convert_json"}:
        return api_response(msg="指定校验任务为枚举 gen/qa/convert_json")
    file = get_upload_param()
    res = upload_file(file, task_name)
    code = 0 if res[0] else -1
    msg = res[1]
    data = res[2]
    return api_response(code=code, msg=msg, data=data)


@main.route("/download/<filename>", methods=["GET"])
# @response_format
def download(filename):
    if not {filename}:
        return api_response(msg="filename不能为空")
    res = download_file(filename)
    if res[0]:
        return res[1]
    else:
        msg = res[1]
        return api_response(code=-1, msg=msg)


@main.route("/shumei", methods=["POST"])
@response_format
def check_shumei():
    file = get_upload_param()
    res = upload_shumei(file)
    code = 0 if res[0] else -1
    msg = res[1]
    data = res[2]
    return api_response(code=code, msg=msg, data=data)


@main.route("/juyan/auto_return", methods=["POST"])
@response_format
def auto_return():
    #   测试key失效自动获取
    res = request("你好")
    return api_response(data=res)


@main.route("/juyan/plugin/auto_return", methods=["POST"])
@response_format
def plugin_return():
    arguments = get_request_params()
    name = arguments.get("name", "没有传名字")
    return api_response(msg=name)

#
# @main.route("/jenkins/buildParam/<version>", methods=["GET"])
# @response_format
# def build_jenkins_param(version):
#     status, msg = jenkins_task.build_by_gpt(version)
#     return api_response(msg=msg)
#


@main.route("/jenkins/build", methods=["GET"])
@response_format
def build_jenkins():
    status, msg = jenkins_task.build_by_gpt()
    return api_response(msg=msg)


@main.route("/jenkins/buildParam/<version>/<job_name>", methods=["GET"])
@response_format
def build_job(version, job_name):
    #   目前该方法只调用gpt,后续可以更改协议支持传不同job_mame
    payload = {"version": version}
    if job_name == "by_gpt":
        status, msg = jenkins_task.build_job("by_gpt", param=payload)
    elif job_name == "agentos":
        status, msg = jenkins_task.build_job("AgentOS", param=payload)
    return api_response(msg=msg)


@main.route("/feishu/add_record", methods=["POST"])
@response_format
def add_record():
    arguments = get_request_params()
    file_name = arguments.get("file_name", "")
    table_name = arguments.get("table_name", "")
    records_list = arguments.get("records_list", [])
    folder_token = arguments.get("folder_token", "")
    res = add_feishu_record(file_name, table_name, records_list, folder_token)
    if res["status"]:
        return api_response(code=0, data=res, msg="执行成功!")
    else:
        return api_response(code=-1, msg="执行失败")


@main.route("/feishu/add_record_by_table", methods=["POST"])
@response_format
def add_record_by_table():
    arguments = get_request_params()
    table_name = arguments.get("table_name", "")
    records_list = arguments.get("records_list", [])
    folder_token = arguments.get("folder_token", "")
    res = add_feishu_record_by_table(table_name, records_list, folder_token)
    if res[0]:
        return api_response(msg=res[1])
    else:
        return api_response(code=-1, msg=res[1])
