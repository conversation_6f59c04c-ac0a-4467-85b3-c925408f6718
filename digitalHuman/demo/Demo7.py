from moviepy.editor import VideoFileClip
import moviepy.editor as mp

# 视频时长裁剪
def trim_video(input_path, output_path, start_time, end_time):
    # 读取视频
    video = VideoFileClip(input_path)

    # 裁剪视频
    trimmed_video = video.subclip(start_time, end_time)

    # 保存裁剪后的视频
    trimmed_video.write_videofile(output_path, codec='libx264', audio_codec='aac')


# 视频物理空间大小裁剪
import os
import subprocess
from moviepy.editor import VideoFileClip


def get_video_size(path):
    return os.path.getsize(path)


def trim_video_by_size(input_path):
    # 读取视频
    # Load your video file
    video = mp.VideoFileClip(input_path)

    # Define the target file size (in bytes)
    target_size = 524288000  # 1 GB

    # Calculate the target bitrate (in bits per second)
    duration = video.duration  # Duration of the video in seconds
    target_bitrate = (target_size * 8) / duration  # Convert bytes to bits

    # Ensure the bitrate is an integer
    target_bitrate = int(target_bitrate)

    # Path to the output video
    output_file = "/Users/<USER>/Desktop/500MB.mp4"

    # Use FFmpeg to re-encode the video with the target bitrate
    cmd = [
        "ffmpeg",
        "-i", "/Users/<USER>/Desktop/test.mp4",
        "-b:v", str(target_bitrate),
        "-bufsize", str(target_bitrate),
        "-maxrate", str(target_bitrate),
        "-c:v", "libx264",
        "-b:a", "128k",  # Set a reasonable audio bitrate
        output_file
    ]

    # Run the FFmpeg command
    subprocess.run(cmd)


if __name__ == '__main__':
    # 视频时长裁剪
    input_video_path = "/Users/<USER>/Desktop/61分钟.avi"
    output_video_path = "/Users/<USER>/Desktop/30分钟.avi"
    start_time = 0  # 开始时间（秒）
    end_time = 1800  # 结束时间（秒）

    trim_video(input_video_path, output_video_path, start_time, end_time)

    # 视频大小裁剪
    # 示例使用
    # 修改视频物流空间大小
    # input_video_path = "/Users/<USER>/Desktop/test.mp4"
    #
    # trim_video_by_size(input_video_path)