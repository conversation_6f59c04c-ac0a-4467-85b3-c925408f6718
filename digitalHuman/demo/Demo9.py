import ffmpeg


# 视频转音频
def extract_audio(video_path, audio_path):
    # 使用 ffmpeg 提取音频并保存为 MP3 文件
    ffmpeg.input(video_path).output(audio_path).run()


from pydub import AudioSegment


def crop_audio(input_path, output_path, start_time_ms, end_time_ms):
    # 加载音频文件
    audio = AudioSegment.from_file(input_path)

    # 裁剪音频片段
    cropped_audio = audio[start_time_ms:end_time_ms]

    # 保存裁剪后的音频文件
    cropped_audio.export(output_path, format="mp3")


if __name__ == '__main__':
    # 输入视频文件路径
    video_path = "/Users/<USER>/Desktop/SP/汽车高压电气系统之三电系统的材料应用与探索.mp4"
    # 输出音频文件路径
    audio_path = "/Users/<USER>/Desktop/YP/汽车高压电气系统之三电系统的材料应用与探索.wav"
    # 视频转音频
    extract_audio(video_path, audio_path)

    # # 输出裁剪后的音频文件路径
    # output_path = "/Users/<USER>/Desktop/YP/古窑0.5.wav"
    #
    # # 裁剪开始时间（以毫秒为单位）
    # start_time = 1 * 1000  # 0秒
    # # 裁剪结束时间（以毫秒为单位）
    # end_time = 120 * 1000  # 60秒
    # # 音频时长裁剪
    # crop_audio(audio_path, output_path, start_time, end_time)
