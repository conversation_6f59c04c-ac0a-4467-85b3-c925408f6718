import json
from random import randint

import requests
from requests_toolbelt import MultipartEncoder

"""
This script is used to test the chatbot API of ChatMax.
It will send a request to the server and receive a response.
The response will be saved in a file name
"""
def post_process():
    url = "https://test-chatmax.orionstar.com/api/any-command/leave_msg.submit"
    p1value = generate_random_number()
    headers = {"Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryBRUBRQ7744t97nvK",
               "Cookie": "supabase-auth-token=[%22eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJhdXRoZW50aWNhdGVkIiwiZXhwIjoxNzIxNjQxMDc0LCJzdWIiOiJmMGEyMTIzYi00MjRjLTQ0ZmQtYjE5ZC1iYzUwODE4ZTY3MDAiLCJlbWFpbCI6IjE1ODEwMjY2NzE2QGp1eWFuLmNvbSIsInBob25lIjoiMTU4MTAyNjY3MTYiLCJhcHBfbWV0YWRhdGEiOnsicHJvdmlkZXIiOiJlbWFpbCIsInByb3ZpZGVycyI6WyJlbWFpbCIsInBob25lIl19LCJ1c2VyX21ldGFkYXRhIjp7ImFwcF9kZW1vX2d1aWRlIjoxLCJjb21wYW55Ijoi54yO5oi35rWL6K-VIiwidXNlcl9uYW1lIjoi5byg546J5LyfIiwidXNlcl9wcm9maWxlIjoidjIuMSIsInVzZXJfc3luY2VkIjp0cnVlLCJ3eF9pZCI6Im9rWFZsNnJrdXFzWTdvV3cxV3JTVlNTVFZjbGsifSwicm9sZSI6ImF1dGhlbnRpY2F0ZWQiLCJhYWwiOiJhYWwxIiwiYW1yIjpbeyJtZXRob2QiOiJvdHAiLCJ0aW1lc3RhbXAiOjE3MjExMjI2NzR9XSwic2Vzc2lvbl9pZCI6IjVjYjAwMGQxLWM0MGUtNDExNC04MjE3LTcwMTQ4ODljMmI0NiJ9.9-rPbykl7_EFTiJC7HibFUIvOYdGY8ILTcXY-Oynno0%22%2C%22v7Hr_rSvseXrRJrbbf4Fww%22%2Cnull%2Cnull]"}
    form_data = MultipartEncoder(
        fields={
            'p1': p1value,
            'p2': 'f0a2123b-424c-44fd-b19d-bc50818e6700'
        },
        boundary='----WebKitFormBoundaryBRUBRQ7744t97nvK'
    )
    headers.update({'Content-Type': form_data.content_type})

    response = requests.post(url, data=form_data, headers=headers)
    print(response.text)


def generate_random_number():
    random_digits = ''.join([str(randint(0, 9)) for _ in range(10)])
    return '1' + random_digits


if __name__ == "__main__":
    for i in range(1):
        post_process()
