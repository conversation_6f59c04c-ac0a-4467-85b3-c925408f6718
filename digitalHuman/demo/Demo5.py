import json

import requests

headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'orionstar-api-key': 'bz0e0f6a9abfe75a900c983762a1ba6cadzb094304191ff6d95395c26116304bbe3',
}
# 线上地址
base_url = "https://test-openapi-chatmax.orionstar.com"
# 测试地址
#base_url = "https://openapi.chatmax.net"

path = "/v1/ctai/ctai_query_doc_section_list_v2"

# 一个pdf文档场景
data = {"character_id": "0609c328db024a67712aa29acdd769ee", "ctai_doc_list": [
    {"ctdoc_id": "057e8c78cdd6787eab480f6bc679ce78",
     "ctai_doc": {"ctdoc_id": "057e8c78cdd6787eab480f6bc679ce78", "ctorg_id": "746c6a2e63ce380ca52c05fbbc811e97",
                  "character_id": "0609c328db024a67712aa29acdd769ee",
                  "ctdataset_id": "a3078cb2aacaccff475b3d90d8bd9f8b", "scope_type": "character",
                  "scope_id": "0609c328db024a67712aa29acdd769ee", "doc_type": "doc", "usage_type": "normal",
                  "doc_genre": "normal", "doc_mime": "docx", "doc_name": "豹小秘1.1产品参数表.docx", "doc_version": "0",
                  "pub_status": "1", "doc_url": "",
                  "doc_file_id": "ct003_20240801_a576679ce5e12e8136a07437f0d5c018.docx", "doc_file_size": "199094",
                  "doc_preview_file_id": "", "create_time": "1722481972", "update_time": "1722482048",
                  "doc_file_url": "https://test-api-chatmax.orionstar.com/orics/down/ct003_20240801_a576679ce5e12e8136a07437f0d5c018.docx",
                  "doc_preview_file_url": ""}, "ctai_doc_section_list": [
        {"ctdoc_id": "057e8c78cdd6787eab480f6bc679ce78",
         "section_id": "a3078cb2aacaccff475b3d90d8bd9f8b.057e8c78cdd6787eab480f6bc679ce78.section_query_by_es.0",
         "score": "0.8273461456070995"}]}]}
# 两个pdf文档场景
data1 = {"character_id": "0609c328db024a67712aa29acdd769ee", "ctai_doc_list": [
    {"ctdoc_id": "7edf9ccfedbfe576005215a951c2fe27",
     "ctai_doc": {"ctdoc_id": "7edf9ccfedbfe576005215a951c2fe27", "ctorg_id": "746c6a2e63ce380ca52c05fbbc811e97",
                  "character_id": "0609c328db024a67712aa29acdd769ee",
                  "ctdataset_id": "a3078cb2aacaccff475b3d90d8bd9f8b", "scope_type": "character",
                  "scope_id": "0609c328db024a67712aa29acdd769ee", "doc_type": "doc", "usage_type": "normal",
                  "doc_genre": "normal", "doc_mime": "pdf", "doc_name": "top-tech-trends-ebook-2021.pdf",
                  "doc_version": "0", "pub_status": "1", "doc_url": "",
                  "doc_file_id": "ct003_20240801_5b1210f7eb1ec1aeafe7899470b3deec.pdf", "doc_file_size": "298243",
                  "doc_preview_file_id": "", "create_time": "1722482094", "update_time": "1722482095",
                  "doc_file_url": "https://test-api-chatmax.orionstar.com/orics/down/ct003_20240801_5b1210f7eb1ec1aeafe7899470b3deec.pdf",
                  "doc_preview_file_url": "https://test-api-chatmax.orionstar.com/orics/down/ct003_20240801_5b1210f7eb1ec1aeafe7899470b3deec.pdf"},
     "ctai_doc_section_list": [{"ctdoc_id": "7edf9ccfedbfe576005215a951c2fe27",
                                "section_id": "a3078cb2aacaccff475b3d90d8bd9f8b.7edf9ccfedbfe576005215a951c2fe27.section.18",
                                "score": "1.0"}, {"ctdoc_id": "7edf9ccfedbfe576005215a951c2fe27",
                                                  "section_id": "a3078cb2aacaccff475b3d90d8bd9f8b.7edf9ccfedbfe576005215a951c2fe27.section_query_by_es.0",
                                                  "score": "0.8335175193811383"}]},
    {"ctdoc_id": "9c9ff4cf59865e40355d4f5ab26c80ca",
     "ctai_doc": {"ctdoc_id": "9c9ff4cf59865e40355d4f5ab26c80ca", "ctorg_id": "746c6a2e63ce380ca52c05fbbc811e97",
                  "character_id": "0609c328db024a67712aa29acdd769ee",
                  "ctdataset_id": "a3078cb2aacaccff475b3d90d8bd9f8b", "scope_type": "character",
                  "scope_id": "0609c328db024a67712aa29acdd769ee", "doc_type": "doc", "usage_type": "normal",
                  "doc_genre": "normal", "doc_mime": "pdf",
                  "doc_name": "top-priorities-for-tech-service-providers-leadership-vision-for-2021-ebook.pdf",
                  "doc_version": "0", "pub_status": "1", "doc_url": "",
                  "doc_file_id": "ct003_20240801_a407d5e1109bc4b3e23614a7c2fa0d29.pdf", "doc_file_size": "1317119",
                  "doc_preview_file_id": "", "create_time": "**********", "update_time": "**********",
                  "doc_file_url": "https://test-api-chatmax.orionstar.com/orics/down/ct003_20240801_a407d5e1109bc4b3e23614a7c2fa0d29.pdf",
                  "doc_preview_file_url": "https://test-api-chatmax.orionstar.com/orics/down/ct003_20240801_a407d5e1109bc4b3e23614a7c2fa0d29.pdf"},
     "ctai_doc_section_list": [{"ctdoc_id": "9c9ff4cf59865e40355d4f5ab26c80ca",
                                "section_id": "a3078cb2aacaccff475b3d90d8bd9f8b.9c9ff4cf59865e40355d4f5ab26c80ca.section.0",
                                "score": "0.8306247175613595"}, {"ctdoc_id": "9c9ff4cf59865e40355d4f5ab26c80ca",
                                                                 "section_id": "a3078cb2aacaccff475b3d90d8bd9f8b.9c9ff4cf59865e40355d4f5ab26c80ca.section.36",
                                                                 "score": "0.8078353344909663"},
                               {"ctdoc_id": "9c9ff4cf59865e40355d4f5ab26c80ca",
                                "section_id": "a3078cb2aacaccff475b3d90d8bd9f8b.9c9ff4cf59865e40355d4f5ab26c80ca.section.26",
                                "score": "0.8038740932567081"}]}]}
json_data = json.dumps(data)

"""
- OpenAPI接口测试
- /v1/ctai/ctai_query_doc_section_list_v2
"""


def post_request():
    try:
        # 发送 POST 请求
        print(base_url + path)
        response = requests.post(url=base_url + path, headers=headers, data=json_data)

        # 检查响应状态码
        if response.status_code == 200:
            # 解析 JSON 响应
            result = response.json()
            print('Response:', json.dumps(result, indent=4, ensure_ascii=False))
        else:
            print(f'Error: HTTP {response.status_code}')
    except Exception as e:
        print(f'Error: {e}')


if __name__ == "__main__":
    post_request()
