import csv
import difflib
import os.path

import pandas as pd

"""
def handle_conditions1():
def handle_conditions2():
def handle_conditions3():
conditions = {
    "condition1": handle_conditions1,
    "condition2": handle_conditions2,
    "condition3": handle_conditions3,
}
"""
def test(path):
    with open(path, 'r') as file:
        cp = 0
        qt = 0
        kfws = 0
        ggss = 0
        zbhj = 0
        qtsb = 0
        zsfg = 0
        for line in file:
            if line.split(',')[2] == "菜品" and line.split(',')[1] == "正":
                cp += int(line.split(',')[3])
            elif line.split(',')[2] == "其他服务" and line.split(',')[1] == "正":
                qt += int(line.split(',')[3])
            elif line.split(',')[2] == "客房卫生" and line.split(',')[1] == "正":
                kfws += int(line.split(',')[3])
            elif line.split(',')[2] == "公共设施" and line.split(',')[1] == "正":
                ggss += int(line.split(',')[3])
            elif line.split(',')[2] == "周边环境" and line.split(',')[1] == "正":
                zbhj += int(line.split(',')[3])
            elif line.split(',')[2] == "其他设施" and line.split(',')[1] == "正":
                qtsb += int(line.split(',')[3])
            elif line.split(',')[2] == "装饰及风格" and line.split(',')[1] == "正":
                zsfg += int(line.split(',')[3])
        print(f"菜品:{cp}\n其他服务:{qt}\n客房卫生:{kfws}\n公共设施:{ggss}\n周边环境:{zbhj}\n其他设施:{qtsb}\n装饰及风格:{zsfg}")

def testDiff():
    text1 = "尊敬的加盟商，您好!判断是否需要做权利义务转让协议，首先看公司营业执照是否改变，如果只是公司更名，统一社会信用代码不变的，那是不需要做权利义务转让协议;如果公司营业执照发生变更，统一社会信用代码同时变化，那就需要做权利义务转让协议协议，具体流程:加盟商书面申请·省区领导、加盟部领导审批同意，提交全套证照(与新公司名称一致）、租赁合同等材料给到加盟客服走线上合同流程。谢谢"
    text2 = "尊敬的加盟商，您好！，首先看公司营业执照是否改变，如果只是公司更名，统一社会信用代码不变的，那是不需要做权利义务转让协议；如果公司营业执照发生变更，统一社会信用代码同时变化，那就需要做权利义务转让协议协议。具体流程：加盟商书面申请-省区领导、加盟部领导审批同意，提交全套证照（与新公司名称一致）、租赁合同等材料给到加盟客服走线上合同流程。谢谢！"
    text1_lines = text1.splitlines()
    text2_lines = text2.splitlines()
    d = difflib.HtmlDiff()
    diff = d.make_file(text1_lines, text2_lines)
    with open("diff.html", "w") as f:
        f.write(diff)
    print(diff)

def compare_texts(text1, text2):
    differ = difflib.Differ()
    diff = list(differ.compare(text1.splitlines(), text2.splitlines()))

    return '\n'.join(diff)

#实现两段文案的模糊匹配，并且一致率大于90%认为通过
def fuzzy_match(text1, text2, threshold):
    matcher = difflib.SequenceMatcher(None, text1, text2)
    similarity = matcher.ratio()

    if similarity >= threshold:
        return True
    else:
        return False
#读取jmeter的jtl文件数据
def test1():
    first_line = True
    jtl_file = '/Users/<USER>/Desktop/6.27华住压测数据/jmeter/06_27/teach_chat_evaluate/10/10.jtl'
    with open(jtl_file, 'r') as file:
        for line in file:
            if first_line:
                first_line = False
                continue
            print(line.split(',')[1])
if __name__ == "__main__":
    #test("/Users/<USER>/Desktop/test.log")
    #testDiff()
    """
    text1 = "hello word"
    text2 = "hello word90"
    print(compare_texts(text1, text2))
    print(fuzzy_match(text1, text2, 0.9))
    """
    test1()
