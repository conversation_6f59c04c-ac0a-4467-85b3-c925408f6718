from pydub import AudioSegment
import os


def get_file_size(file_path):
    """获取文件大小，单位为字节"""
    return os.path.getsize(file_path)


def reduce_audio_size_to_target(input_file, output_file, target_size, initial_bitrate="128k"):
    # 加载音频文件
    audio = AudioSegment.from_file(input_file)

    # 初始比特率设定
    bitrate = int(initial_bitrate.replace("k", ""))

    # 临时文件路径
    temp_output_file = "/Users/<USER>/Desktop/YP/1GB.mp3"

    while True:
        # 导出音频文件并设置比特率
        audio.export(temp_output_file, format="mp3", bitrate=f"{bitrate}k")

        # 获取导出文件的大小
        current_size = get_file_size(temp_output_file)
        print(current_size)
        # 检查是否达到目标大小
        if current_size <= target_size:
            break

        # 如果文件大小大于目标大小，减小比特率
        bitrate -= 8  # 每次减小8kbps

        # 预防比特率过小
        if bitrate <= 8:
            print("无法进一步减小文件大小。")
            break

    # 将临时文件重命名为输出文件
    os.rename(temp_output_file, output_file)


if __name__ == '__main__':
    # 输入文件路径
    input_file = "/Users/<USER>/Desktop/YP/30分钟.mp3"

    # 输出文件路径
    output_file = "/Users/<USER>/Desktop/YP/1GB.mp3"

    # 目标文件大小（单位为字节，例如2MB = 2 * 1024 * 1024字节）
    target_size = 1024 * 1024 * 1024  # 2MB

    # 调用函数，将音频文件大小调整到接近目标大小
    reduce_audio_size_to_target(input_file, output_file, target_size, initial_bitrate="128k")
