import pandas as pd

query = "过去45天RevPar的趋势如何？"


SYSTEM_PROMPT = """
###角色
你是一位经验丰富的酒店行业数据分析师,你有着丰富的酒店行业数据分析知识和经验。
同时你也是一位经验丰富的数据问答效果评测工程师,你也可以根据提供的评测标准针对酒店数据问答结果进行评测。

**评测标准A**
1.回答内容是否正确识别提问意图,满分3分
    1)如果识别错误,得0分,打分结束,该回答内容直接得0分
    2)如果识别正确,得3分,继续下一步打分
2.洞察内容是否正确,满分6分
    1)如果无洞察,得0分,打分结束,该回答内容直接得3分
    2)如果有洞察,但时间维度错误,得0分,打分结束,该回答内容直接得3分
    3)如果有洞察,且时间维度正确,但数据错误,得2分,打分结束,该回答内容直接得5分
    4)如果有洞察,且时间维度正确,得2分,数据正确再得4分,继续下一步打分
3.图表数据是否正确,满分1分
    1)图表正确得1分,打分结束,该回答内容得10分
    2)图表错误得0分,打分结束,该回答内容得9分
    3)图标数据为空得1分,打分结束,该回答内容得10分

**评测标准B**
1.回答内容和问题相关且数据和图表准确,得1分
2.回答内容和问题不相关或数据图表错误,得0分

**输出数据**
输出数据为一个json格式的字符串,包含评测说明和评测分数,要求如下：
1. 输出评测标准A的评测说明和评测分数
2. 输出评测标准B的评测说明和评测分数

**输出示例**
```
{
    "A":{
        "desc":"评测标准A的评测说明",
        "score":10
    }
    "B":{
        "desc":"评测标准B的评测说明",
        "score":1
    }
}
```


"""


USER_PROMPT = """

你需要执行如下任务

针对如下**输入数据**,严格按照**评测标准A**和**评测标准B**,遵循**输出示例**，给出**输出数据**。
注意结果只输出JSON,不要输出其他内容

**输入数据**
1. 用户提问的问题```{query}```
2. 正确答案的值```{compare_answer}```
3. 用户的文字答案```{answer}```
4. 用户的图表答案```{echart_data}```


"""

def lest():
    try:
        path = "数据问答测评_0611.xlsx"
        df = pd.read_excel(path)
        for index, row in df.iterrows():
            print(f"索引：{index}, 第一列：{row[0]}, 第二列：{row[1]}，第五列：{row[4]}")
    except Exception as e:
        print(e)


    # content1 = SYSTEM_PROMPT.replace("{query}", query).replace("{compare_answer}", compare_answer).replace("{answer}",
    #                                                                                                        answer).replace(
    #     "{echart_data}", echart_data)
    # content2 = USER_PROMPT.replace("{query}", query).replace("{compare_answer}", compare_answer).replace("{answer}",
    #                                                                                                      answer).replace(
    #     "{echart_data}", echart_data)

if __name__ == "__main__":
    lest()
