from moviepy.editor import VideoFileClip


def convert_to_avi(input_file, output_file):
    # 加载MP4视频文件
    video_clip = VideoFileClip(input_file)

    # 将视频文件写入AVI格式
    video_clip.write_videofile(output_file, codec='png')

    print("视频转换完成！")


if __name__ == '__main__':
    input_file = "/Users/<USER>/Desktop/SP/test.mp4"
    output_file = "/Users/<USER>/Desktop/SP/test.avi"
    convert_to_avi(input_file, output_file)
