import json
import time
import requests

"""
pytest的前置钩子函数
"""


def pytest_sessionstart(session):
    session.start_time = time.time()


"""
pytest的后置钩子函数
"""


def pytest_sessionfinish(session, exitstatus):
    # 计算测试用例执行时间
    duration = time.time() - session.start_time
    # Webhook URL
    webhook_url = 'https://open.feishu.cn/open-apis/bot/v2/hook/66588c17-2958-432e-981b-be223005e3b5'
    # 获取测试结果passeds:失败数量，failures：错误数量，skipped：跳过数量
    passed = session.testscollected - session.testsfailed
    failed = session.testsfailed
    skipped = session.testscollected - (passed + failed)
    # 报告title背景颜色颜色
    Background = "green"
    if failed > 0:
        Background = "red"
    # 消息体
    message = {
        "msg_type": "interactive",
        "card": {
            "config": {
                "wide_screen_mode": True,
                "enable_forward": False
            },
            "header": {
                "template": "blue",
                "title": {
                    "tag": "plain_text",
                    "content": "测试报告"
                },
                "template": Background
            },
            "elements": [
                {
                    "tag": "markdown",
                    "content": f"**测试结果：**\n{'✅ ' if passed else '❌ '}通过，{passed}个测试用例\n{'❌ ' if failed else '✅ '}失败，{failed}个测试用例\n{'⏭️ ' if skipped else '✅ '}跳过，{skipped}个测试用例\n\n**测试时长：**\n{duration:.2f}秒"
                }
            ]
        }
    }

    # 发送消息到飞书群
    send_message_to_feishu(webhook_url, message)


"""
给飞书发送消息
"""


def send_message_to_feishu(webhook_url, message):
    response = requests.post(webhook_url, json=message)
    if response.status_code != 200:
        raise Exception(f"Failed to send message to Feishu: {response.text}")
    print(response.text)
    print(f"发送消息成功: {json.dumps(message)}")


if __name__ == '__main__':
    pytest_sessionstart(None)
