import difflib
import json
import openpyxl
import requests

"""
config文件内容,统一抽取到头部
"""
# url
url = 'http://10.100.10.57:92/v1/ctai/ai_chat'
# headers
headers = {
    'Content-Type': 'application/json',
    'orionstar-api-Key': 'bz2250427101e386aa21ceca096e3daf4dz21cb995a083be97e4bfaae6761467175'
}
# 入参
data = {
    "session_id": "220eb220-131a-4a5d-b107-456552d036b2",
    "recommend": 0,
    "query": "开店四个月，如何开始进行酒店账单的核对工作？",
    "sence": "superboss_qa",
    "request_id": "75d22d11-29da-41a4-bfa1-be838e9a1928",
    "store_id": "9007534",
    "dataaccess_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoic3lzdGVtIiwiaG90ZWxfaWQiOiI5MDA3NTM0IiwiY3JlYXRlX3RpbWUiOiIyMDI0LTA1LTEzIiwiZXhwIjoxNzUxNjE5NTE1fQ.OfBbSuOBO1wRkkoOQiAA3njX49egLJVz8-DfYinxPfI"
}
# fuzzy_match()的阀值
threshold = 0.9
"""
def diff(path):

#调用api
def invokeApi():
"""

"""
function: 实现读取excel文件内容并与api返回结果进行对比
"""


# 内容按照阀值实现模糊匹配
def fuzzy_match(text1, text2):
    matcher = difflib.SequenceMatcher(None, text1, text2)
    similarity = matcher.ratio()
    if similarity >= threshold:
        return True
    else:
        return False


"""
function: 从excel中读取数据，并将数据存储到字典中
""
"""


def read_from_excel():
    workbook = openpyxl.load_workbook("/Users/<USER>/Desktop/Test.xlsx")
    # 选择第一个工作表
    sheet = workbook.active
    # 定义起始行数
    start_row = 2
    # 循环读取数据
    while True:
        # 读取第二列数据
        second_column_data = sheet.cell(row=start_row, column=2).value
        if second_column_data is None:
            break
        data["query"] = second_column_data
        answer_text = http_post(data)
        print(answer_text)
        sheet.cell(row=start_row, column=4, value=answer_text)
        if not answer_text:
            break
        print(f"answer_text: {answer_text}")
        # 读取第三列数据
        third_column_data = sheet.cell(row=start_row, column=3).value
        if fuzzy_match(answer_text, third_column_data):
            sheet.cell(row=start_row, column=5, value=1)
        else:
            sheet.cell(row=start_row, column=5, value=0)
        print(fuzzy_match(answer_text, third_column_data))
        print(f"第二列第{start_row}行数据:", second_column_data)
        print(f"第三列第{start_row}行数据:", third_column_data)
        # 增加行数继续读取下一行数据
        start_row += 1
    # 保存修改后的 Excel 文件
    workbook.save("/Users/<USER>/Desktop/Test.xlsx")
    # 关闭 Excel 文件
    workbook.close()


"""
http post请求
""
"""


def http_post(data):
    # print(data)
    answer_text = ""
    response = requests.post(url, headers=headers, data=json.dumps(data))
    if response.status_code == 200:
        for line in response.iter_lines():
            if line:
                line = line[6:]
                try:
                    data_dict = json.loads(line)
                except BaseException as e:
                    print(e)
                return_type = data_dict.get("data", {}).get("return_type", "")
                if return_type == "rag":
                    text = data_dict.get("data", {}).get("return_data", {}).get("rag", []).get("answer_text", "")
                    answer_text = answer_text + "" + text
    else:
        print('Failed to fetch data:', response.status_code, response.text)
    return answer_text


if __name__ == "__main__":
    read_from_excel()
