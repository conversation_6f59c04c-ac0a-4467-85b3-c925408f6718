import openpyxl
import requests

user_input = ""
url = 'http://43.139.254.241:8007/v1/chat/completions'
headers = {'Content-Type': 'application/json'}
data = {
    "model": "OrionStarMessiah2",
    "stream": False,
    "messages": [
        {
            "role": "user",
            "content": f"你是一个角色识别助手，需要基于用户输入，识别用户角色，生成一个角色的定义。请考虑以下几个方面：1. 角色的专长领域2. 角色的主要目标3. 角色的个性特征4. 角色的擅长技能5. 角色与用户交互时的语气和行为准则  最终生成的提示应该描述这个角色的名称、个性特征和在与用户互动时的表现方式。### 用户输入 ###{user_input}### 回答要求 ###1.回答不应包含任何有害、不道德、种族主义、性别歧视、有毒、危险或非法的内容。2.一定要用中文回答问题。3.请你以第二人称'你',并且用严谨风格来回答问题 4.参考例子生成角色### 例子 ### 你是一位经验丰富的老师，专注于知识与教育领域。你的目标是帮助用户解答复杂的问题并提升他们的知识水平。你的个性是智慧、理性且充满耐心，并且你擅长分析复杂问题、提供详细解释和进行教育与培训。在与用户交互时，请确保你的语气温和且具启发性，始终保持耐心并引导用户深入思考。你是一位卓越的医生，专精于医疗与健康领域。你的主要目标是解答用户的健康疑虑，提供医疗指导，帮助他们做出最佳的健康决策。你的个性刻画为理性、亲切和关怀，并且你擅长识别病症、给出专业建议和处理危急状况。在与用户交互时，你需要保持专业且友善的语气，耐心听取用户的诉说，为他们提供详尽准确的医学信息，鼓励他们采取正确的健康行动。"
        }
    ],
    "temperature": 0.1
}

content = """System: 你是一位卓越的医生，擅长于医疗与健康领域。你的主要目标是对用户提出的健康疑问进行解答，提供医疗建议，以帮助他们做出最有利于健康的决定。你以专业、关怀和亲切的个性闻名，并且你擅长识别疾病症状、提供专业医疗建议以及处理突发的紧急情况。你的语气应以专业、友善为主，时刻保持耐心，对病患的问题或疑虑给予详细而准确的答复，鼓励他们采取正确的健康行动。严格按照下面的“回答要求”，并根据“聊天历史”和“参考资料”中提供的信息，尽你所能回答“用户问题”。

### 回答要求 ###
1. 你只能根据下面“参考资料”中给出的事实信息来回答“用户问题”，必须有理有据，先说明引用的政策原文，再回答用户问题，严禁胡编乱造。
2. 如果“参考资料”中的信息不足以回答“用户问题”，请直接回答下面三个双引号中的内容："抱歉，我还没有学习到关于这个问题的知识。您可以尝试问些其他问题，或者联系我们的专业团队以获取支持。"。
3. 请你以第一人称并且用严谨的风格来回答“用户问题”，一定要用中文来回答。
Human: ### 参考资料 ###
["Source_id": e87dd58e1cfb5a1fefbb5988a7f2dfc5,"Content": " 广东省社保保障卡业务经办规程 （一）持卡人办理社保卡临时挂失或正式挂失后，未办理补卡前，又找回本人社保卡的，可办理解除挂失，恢复该卡的正常使用。 （二）服务银行卡经办网点可办理解挂业务。 （三）服务银行单方受理解挂后，同步向省社保卡系统发送解挂请求，同步实现社保功能、银行账户的联动解挂。 （四）已办理正式挂失且已申请补换卡业务的，不允许办理解挂业务。 解除挂失的操作流程： 在服务银行卡经办网点办理解除挂失的，按服务银行相关解除挂失业务规定办理。服务银行同步向省社保卡系统发送解除挂失请求，省社保卡系统实时解除社保卡的挂失，并向服务银行反馈挂失结果。 "]

["Source_id": e87dd58e1cfb5a1fefbb5988a7f2dfc5,"Content": " 广东省社保保障卡业务经办规程 第二十四条 临时挂失的基本规定： （一）临时挂失可通过人社部门或服务银行卡经办网点、电话银行、网上银行渠道办理。 （二）临时挂失后即时生效，原实体社保卡停止使用，原电子社保卡经持卡人授权后身份凭证功能可以继续使用。 （三）在服务银行各服务渠道办理临时挂失业务的，同步向省社保卡系统发送挂失请求，同步实现社保功能、银行账户的联动挂失。 （四）社保卡支持异地临时挂失。异地临时挂失可通过网上服务渠道和服务银行客服渠道办理。 临时挂失的操作流程包括： （一）通过线上服务渠道办理临时挂失的，持卡人须提供其社会保障号码、姓名和服务银行等信息用于系统内部的身份鉴别。 （二）通过服务银行卡经办网点办理临时挂失业务的，网点经办人员须核实持卡人身份信息后，在省社保卡系统“社保卡维护-挂失解挂注销-临时挂失”栏目中，进行挂失业务操作。办理成功后，打印业务办理回执单交还持卡人。 （三）临时挂失的网办操作流程参照线下网点业务操作流程执行。 "]

["Source_id": e87dd58e1cfb5a1fefbb5988a7f2dfc5,"Content": " 广东省社保保障卡业务经办规程 第二十五条 正式挂失的基本规定： （一）正式挂失通过服务银行卡经办网点办理。 （二）正式挂失即时生效，原实体社保卡停止使用，原电子社保卡经持卡人授权后身份凭证功能可以继续使用。 （三）在服务银行卡经办网点办理正式挂失的，服务银行同步向省社保卡系统发送挂失请求，同步实现社保功能、银行账户的联动挂失。 （四）社保卡支持异地正式挂失。异地正式挂失通过持卡人当前所在地区服务银行卡经办网点办理。 正式挂失的操作流程： 在服务银行卡经办网点办理正式挂失的，按服务银行相关正式挂失业务规定办理。 "]

["Source_id": e87dd58e1cfb5a1fefbb5988a7f2dfc5,"Content": " 广东省社保保障卡业务经办规程 第二十三条 社保卡挂失分临时挂失和正式挂失。临时挂失是指通过非书面形式确认的挂失，正式挂失是指通过书面形式确认的挂失。 社保卡丢失的，应当及时告知持卡人办理挂失手续，避免造成个人权益和账户资金的损失。可先办理临时挂失，再办理正式挂失，也可直接办理正式挂失。挂失应同步实现社保功能、银行账户的联动挂失。 "]

["Source_id": e87dd58e1cfb5a1fefbb5988a7f2dfc5,"Content": " 广东省社保保障卡业务经办规程 第四条 本规程适用于广东省行政区域内社保卡的本地业务及省内通办业务，包括社保卡的申请、制卡和分发、领取、社保功能启用和银行账户激活、密码、收取工本费、挂失和解挂、补卡和换卡、回收、注销、即时制卡等业务。各地市需统一使用广东省社会保障卡管理信息系统，按照本经办规程开展业务工作。 "]

### 用户问题 ###
社保卡的解挂流程是怎样的？"""


# data1 = {
#     "model": "OrionStarMessiah2",
#     "stream": False,
#     "messages": [
#         {
#             "role": "system",
#             "content": f"{content}"
#         }
#     ],
#     "temperature": 0
# }
def read_from_excel():
    global user_input
    workbook = openpyxl.load_workbook("/Users/<USER>/Desktop/out_7.xlsx")
    # 选择第一个工作表
    sheet = workbook.active
    # 定义起始行数
    start_row = 2
    # 循环读取数据
    while True:
        # 读取第二列数据
        second_column_data = sheet.cell(row=start_row, column=1).value
        if second_column_data is None:
            break
        user_input = second_column_data
        print(user_input)
        result = request_api()
        print(f"{result}")
        if not result:
            break
        sheet.cell(row=start_row, column=2, value=result)
        # 增加行数继续读取下一行数据
        start_row += 1
    # 保存修改后的 Excel 文件
    workbook.save("/Users/<USER>/Desktop/out_7.xlsx")
    # 关闭 Excel 文件
    workbook.close()


def request_api():
    global data
    data = {
        "model": "OrionStarMessiah2",
        "stream": False,
        "messages": [
            {
                "role": "system",
                "content": f"你是一个角色识别助手，需要基于用户输入，识别用户角色，生成一个角色的定义。请考虑以下几个方面：1. 角色的专长领域2. 角色的主要目标3. 角色的个性特征4. 角色的擅长技能5. 角色与用户交互时的语气和行为准则  最终生成的提示应该描述这个角色的名称、个性特征和在与用户互动时的表现方式。### 用户输入 ###{user_input}### 回答要求 ###1.回答不应包含任何有害、不道德、种族主义、性别歧视、有毒、危险或非法的内容。2.一定要用中文回答问题。3.请你以第二人称'你',并且用严谨风格来回答问题 4.参考例子生成角色### 例子 ### 你是一位经验丰富的老师，专注于知识与教育领域。你的目标是帮助用户解答复杂的问题并提升他们的知识水平。你的个性是智慧、理性且充满耐心，并且你擅长分析复杂问题、提供详细解释和进行教育与培训。在与用户交互时，请确保你的语气温和且具启发性，始终保持耐心并引导用户深入思考。你是一位卓越的医生，专精于医疗与健康领域。你的主要目标是解答用户的健康疑虑，提供医疗指导，帮助他们做出最佳的健康决策。你的个性刻画为理性、亲切和关怀，并且你擅长识别病症、给出专业建议和处理危急状况。在与用户交互时，你需要保持专业且友善的语气，耐心听取用户的诉说，为他们提供详尽准确的医学信息，鼓励他们采取正确的健康行动。"
            }
        ],
        "temperature": 0.3
    }
    response = requests.post(url, headers=headers, json=data)
    return response.json().get("choices")[0].get("message").get("content")


roleChatUrl = 'https://test-api-chatmax.orionstar.com/capi/v1/chatmax/ctai_generate_role_chat'
roleChatHeaders = {
    'Content-Type': 'application/json',
    'Cookie': '_gcl_au=1.1.2119496464.1721913694; _ga=GA1.1.638504264.1721913694; _ga_KPVNPZ0CT8=GS1.1.1721913693.1.1.1721913732.0.0.0; _ga_423T6BCTX3=GS1.1.1721913693.1.1.1721913732.0.0.0; _ga_FT8PSNVF2S=GS1.1.1721913693.1.1.1721913732.0.0.0'
}
param = ''
roleChatData = {"query": "轮船", "temperature": 4}


def ctai_generate_role_chat():
    print(roleChatUrl)
    print(roleChatHeaders)
    print(roleChatData)
    response = requests.post(roleChatUrl, headers=roleChatHeaders, json=roleChatData)
    return response.text


if __name__ == '__main__':
    # print(request_api())
    # for i in range(10):
    #     read_from_excel()
    read_from_excel()
    # print(ctai_generate_role_chat())
