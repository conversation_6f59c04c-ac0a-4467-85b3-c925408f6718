import requests
import pytest

""""
配置文件
"""
# uri
uri = "http://10.100.10.57:100"
# path
PATH = {
    "check-digital-human": "/dh/check-digital-human",
    "start-digital-human": "/dh/start-digital-human",
    "stop-digital-human": "/dh/stop-digital-human",
    "send-text": "/dh/send-text",
    "send-command": "/dh/send-command",
    "heart-beat": "/dh/heart-beat"
}

# 全局变量
number = 0


# 生成1-10的自然数
def get_numbers():
    global number
    number += 1
    return number


# param
PARAMS_DICT = {
    "start-digital-human": [
        {"user_id": "test1", "digital_human_id": "woman-2"},
        {"user_id": "test2", "digital_human_id": "man-3"},
        {"user_id": "test3", "digital_human_id": "woman-4"}
    ],
    "stop-digital-human": [
        {"session_id": "test1"},
        {"session_id": "test2"},
        {"session_id": "test3"}
    ],
    "send-command": [
        {"session_id": "test1", "command": "INTERRUPT"},
        {"session_id": "test2", "command": "INTERRUPT"},
        {"session_id": "test3", "command": "INTERRUPT"}
    ],
    "heart-beat": [
        {"session_id": "test1"},
        {"session_id": "test2"},
        {"session_id": "test3"}
    ],
    "send-text": [
        {"session_id": "test1",
         "text": "生命是一树花开，花开是缘，花落是缘，缘来缘去，走在喜怒哀乐、悲欢离合的交替中。我们都是行者，做着一场无预知的修行，或深或浅，或浓或淡，展开着不一样的羽翼，泼墨着不同的风景。若能适时读闲岁月，通透人生，适时放下，自然盛开，抛开拘泥的束缚，轻扣静好，便可一笔清欢，把光阴读闲!忙忙碌碌的脚步，拥挤的人群，纷杂的是是非非;争权夺利涂染了目光，曾经的纯粹，也随之淹没在物欲横流中。什么时候，才可静静地听听音乐，看本书，排序一下心情的故事，寻找一页心灵的家园?不是人生劳累你，而是你劳累了人生，生活的闲庭小径，在一园子的角角落落，需要一双晶莹剔透，会发现的眼睛，读闲岁月。",
         "interrupt": "false"},
        {"session_id": "test2",
         "text": "生命是一树花开，花开是缘，花落是缘，缘来缘去，走在喜怒哀乐、悲欢离合的交替中。我们都是行者，做着一场无预知的修行，或深或浅，或浓或淡，展开着不一样的羽翼，泼墨着不同的风景。若能适时读闲岁月，通透人生，适时放下，自然盛开，抛开拘泥的束缚，轻扣静好，便可一笔清欢，把光阴读闲!忙忙碌碌的脚步，拥挤的人群，纷杂的是是非非;争权夺利涂染了目光，曾经的纯粹，也随之淹没在物欲横流中。什么时候，才可静静地听听音乐，看本书，排序一下心情的故事，寻找一页心灵的家园?不是人生劳累你，而是你劳累了人生，生活的闲庭小径，在一园子的角角落落，需要一双晶莹剔透，会发现的眼睛，读闲岁月。",
         "interrupt": "false"},
        {"session_id": "test3",
         "text": "生命是一树花开，花开是缘，花落是缘，缘来缘去，走在喜怒哀乐、悲欢离合的交替中。我们都是行者，做着一场无预知的修行，或深或浅，或浓或淡，展开着不一样的羽翼，泼墨着不同的风景。若能适时读闲岁月，通透人生，适时放下，自然盛开，抛开拘泥的束缚，轻扣静好，便可一笔清欢，把光阴读闲!忙忙碌碌的脚步，拥挤的人群，纷杂的是是非非;争权夺利涂染了目光，曾经的纯粹，也随之淹没在物欲横流中。什么时候，才可静静地听听音乐，看本书，排序一下心情的故事，寻找一页心灵的家园?不是人生劳累你，而是你劳累了人生，生活的闲庭小径，在一园子的角角落落，需要一双晶莹剔透，会发现的眼睛，读闲岁月。",
         "interrupt": "false"}
    ]
}


# case 1: 正常场景
@pytest.mark.parametrize('params', [""])
def test_check_digital_human(params):
    """
    测试接口：检查数字人是否存在
    :return:
    """
    result = requests.post(url=uri + PATH["check-digital-human"], json=params)
    assert result.status_code == 200
    assert result.json().get("data").get("dh_status") == 1
    print("检查数字人是否存在:", result.json())


@pytest.mark.parametrize('params', PARAMS_DICT["start-digital-human"])
def test_start_digital_human(params):
    """
    测试接口：启动数字人
    :return:
    """
    result = requests.post(url=uri + PATH["start-digital-human"], json=params)
    assert result.status_code == 200
    # assert result.json().get("data").get("dh_status") == 1
    print("启动数字人：", result.json())


@pytest.mark.parametrize('params', PARAMS_DICT["send-text"])
def test_send_text(params):
    """
    测试接口：发送文本
    :return:
    """
    result = requests.post(url=uri + PATH["send-text"], json=params)
    assert result.status_code == 200
    print("发送文本：", result.json())


@pytest.mark.parametrize('params', PARAMS_DICT["send-command"])
def test_send_command(params):
    """
    测试接口：发送命令
    :return:
    """
    result = requests.post(url=uri + PATH["send-command"], json=params)
    assert result.status_code == 200
    print("发送命令:", result.json())


@pytest.mark.parametrize('params', PARAMS_DICT["heart-beat"])
def test_heart_beat(params):
    """
    测试接口：心跳
    :return:
    """
    result = requests.post(url=uri + PATH["heart-beat"], json=params)
    assert result.status_code == 200
    print("检查心跳：", result.json())


@pytest.mark.parametrize('params', PARAMS_DICT["stop-digital-human"])
def test_stop_digital_human(params):
    """
    测试接口：停止数字人
    :return:
    """
    result = requests.post(url=uri + PATH["stop-digital-human"], json=params)
    assert result.json().get("status") == 200
    print("停止数字人：", result.json())


if __name__ == "__main__":
    pass
