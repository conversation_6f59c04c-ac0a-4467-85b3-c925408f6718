import os
import openai

# 设置 API 密钥
# openai.api_key = os.getenv("OPENAI_API_KEY")  # 从环境变量获取，或者直接粘贴你的 API 密钥
openai.api_key = "***************************************************"


def call_chatgpt(prompt, model="gpt-4", max_tokens=1000, temperature=1):
    """
    调用 ChatGPT API 生成文本。

    Args:
        prompt (str): 对 ChatGPT 的提示文本。
        model (str, optional): 使用的 ChatGPT 模型。默认为 "gpt-3.5-turbo"。
        max_tokens (int, optional): 生成文本的最大 tokens 数。默认为 150。
        temperature (float, optional): 控制生成文本的随机性。默认为 0.7。

    Returns:
        str: ChatGPT 生成的文本。
    """
    try:
        response = openai.ChatCompletion.create(
            model=model,
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt},
            ],
            max_tokens=max_tokens,
            temperature=temperature,
        )
        return response.choices[0].message.content.strip()
    except openai.error.OpenAIError as e:
        print(f"OpenAI API 调用出错: {e}")
        return None


if __name__ == "__main__":
    user_prompt = input("请输入你的问题：")
    chatgpt_response = call_chatgpt(user_prompt)
    if chatgpt_response:
        print(f"{chatgpt_response}")
    else:
        print("ChatGPT 响应失败，请稍后重试。")
