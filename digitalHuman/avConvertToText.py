import ffmpeg
import whisper


# 使用 Whisper 将音视频转文案
def extract_audio_from_video(video_path, audio_path):
    (
        ffmpeg
        .input(video_path)
        .output(audio_path, format='wav')
        .run(overwrite_output=True)
    )


def transcribe_audio(audio_path):
    model = whisper.load_model("base")
    result = model.transcribe(audio_path)
    return result['text']


def video_to_transcription(video_path):
    audio_path = 'extracted_audio.wav'
    extract_audio_from_video(video_path, audio_path)
    transcription = transcribe_audio(audio_path)
    return transcription


if __name__ == '__main__':
    video_path = '/Users/<USER>/Desktop/YP/古窑1.wav'
    transcription = video_to_transcription(video_path)
    print(transcription)
