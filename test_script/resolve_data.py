# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : resolve_data.py
<AUTHOR> <EMAIL>
@Time   : 2024-01-03 19:24:32
@Version: v1.0
"""
import json
from tool.util import get_time, read_xlsx_to_dict

import pandas as pd

"""
将网信办测试接口json数据解析成Excel表，进行分析，存储在当前路径下
1.修改最新的json文件
2.重命名生成的文件
3.Bearer umZYy6sWEAeyH89xPY2pQSu1lr8kUg（网信办测试使用的key，打开json文件确认）
"""
def repeat_convert_xlsx():
    """
    网信办接口调用记录删除测试数据
    :return:
    """
    f = open("wangxinban_dialogues_0108_0115.json")
    lines = f.read()
    data = json.loads(lines)
    repeat = []
    count = 0  # 不去重总共多少数据
    no_repeat_data = []
    for line in data.get("Bearer umZYy6sWEAeyH89xPY2pQSu1lr8kUg"):
        count += 1
        query = line.get("dialogue")[0].get("content")
        answer = line.get("answer")
        # content = line.get("dialogue")[0].get("content") + line.get("answer")
        content = query + answer
        if content not in repeat:
            repeat.append(content)
            if content.__contains__("存在违规内容"):
                no_repeat_data.append({"query": query, "answer": answer, "type": "sensitive"})
            else:
                no_repeat_data.append({"query": query, "answer": answer, "type": "standard"})
    no_repeat_data_df = pd.DataFrame(no_repeat_data)
    no_repeat_data_df.to_excel("{}_no_repeat_wangxinban.xlsx".format(get_time(format="%Y-%m-%d")), index=False)
    print(f"总共有{count}数据")
    print(f"去重后有{len(no_repeat_data)}数据")


def data_is_produce():
    """
    网信办数据是否为猎户提供的case
    :return:
    """
    net_data = read_xlsx_to_dict(file_name="2024-01-22_no_repeat_wangxinban.xlsx")
    net_query_list = []
    for data in net_data:
        net_query_list.append(str(data.get("query")).strip())
    produce_data = read_xlsx_to_dict(
        file_name="/Users/<USER>/workspace/liangwei/sensitive/大模型备案相关材料/测试题集_0109.xlsx", sheet="全量测试题集")
    produce_query_list = []
    for data in produce_data:
        produce_query_list.append(str(data.get("输入内容")).strip())
    is_produce = list(set(produce_query_list).intersection(set(net_query_list)))
    print(is_produce)
    print(f"交集数据有{len(is_produce)}条")


if __name__ == '__main__':
    data_is_produce()
    # repeat_convert_xlsx()
