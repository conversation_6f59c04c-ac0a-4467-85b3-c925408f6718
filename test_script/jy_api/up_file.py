# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : up_file.py
<AUTHOR> <EMAIL>
@Time   : 2024-01-31 21:11:09
@Version: v1.0
"""
import traceback
import requests
from tool.util import get_dir_file, flush_key
from test_conifg.path_config import REQUEST_DATA
from tool.common import load_yml

req_data = load_yml(REQUEST_DATA)


def upload_file(env="test"):
    url = req_data["upload_file"][env]["url"]
    # url = "https://test-api-chatmax.orionstar.com/capi/v1/chatmax/upload_ctai_doc"
    upload_file_path = "/Users/<USER>/workspace/liangwei/测试集源文档/线上下载数据"
    character_id = req_data["upload_file"][env]["payload"]["character_id"]

    FILE_TYPR = {
        "docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "txt": "text/plain",
        "pdf": "application/pdf"
    }
    #   获取文件夹下所有要上传的文件
    file_list = get_dir_file(upload_file_path)
    #   控制上传的轮次
    for i in range(1):
        key = flush_key()
        num = 0
        #   遍历上传的所有文件
        for file_name in file_list:
            file_type = file_name[str(file_name).rindex(".") + 1:]
            payload = {"character_id": character_id,
                       "ctai_doc": f'{{"doc_name":"{file_name}","enable_autoqa":"0"}}'
                       }
            files = [
                ('content',
                 (f'{file_name}', open(f'{upload_file_path}/{file_name}', 'rb'), f'{FILE_TYPR[file_type.lower()]}'))
            ]
            headers = {
                'Orionstar-Api-Key': f'{key}'
            }
            print(file_name, FILE_TYPR[file_type.lower()])
            response = requests.request("POST", url, headers=headers, data=payload, files=files)
            try:
                res = response.json()
                if res.get("code") != 0:
                    print(response.text)
                else:
                    num += 1
                    print(f"upload successful {num}")
            except BaseException:
                print(traceback.format_exc())


if __name__ == '__main__':
    upload_file()
