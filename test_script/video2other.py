from moviepy.editor import VideoFileClip
import os

# 输入的 MP4 文件路径
input_file = "/Users/<USER>/workspace/liangwei/测试集源文档/2.13视频文件/音视频对应的文档/非标准普通话-基于复材和全塑的极致轻量化 (1).mp4"
intermediate_file = '/Users/<USER>/workspace/liangwei/测试集源文档/2.13视频文件/音视频对应的文档/intermediate.mp4'
# 输出的 WMV 文件路径
output_file = "/Users/<USER>/workspace/liangwei/测试集源文档/2.13视频文件/音视频对应的文档/非标准普通话-基于复材和全塑的极致轻量化.wmv"

# 加载视频文件
clip = VideoFileClip(input_file)

# 将视频文件写入 WMV 格式
clip.write_videofile(intermediate_file, codec='libx264')
os.system(f'ffmpeg -i {intermediate_file} -c:v wmv2 -b:v 1000k -c:a wmav2 {output_file}')
os.remove(intermediate_file)

print("转换完成！")
