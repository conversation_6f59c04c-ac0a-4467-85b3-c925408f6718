import asyncio
import time
from datetime import datetime
from loguru import logger
import aiohttp
from typing import Optional


class MapPointsMonitor:
    def __init__(self, interval: int = 3600, URL: str = "http://resource-online-test.ainirobot.com/open/v1/resource/dict"):  # 默认每小时执行一次
        self.interval = interval
        self.robot_support_map_points_url = URL  # 替换为实际的API地址.国内版
        # self.robot_support_map_points_url = "http://dev-voice-lite.ainirobot.com/speech-manager/v1/grpc/resource/dict"  # 替换为实际的API地址

    async def get_map_points(self, device_id: str, enterprise_id: str, language: str) -> Optional[list[str]]:
        """获取地图点位信息"""
        start = time.time()
        try:
            async with aiohttp.ClientSession() as session:
                request_body = {
                    "device_id": device_id,
                    "enterprise_id": enterprise_id,
                    "mark": ["SEMANTIC_TAG_DESTINATION"],
                    "limit": 1000,
                    "lang": [language],
                }
                async with session.post(
                    self.robot_support_map_points_url,
                    json=request_body,
                ) as response:
                    if response.status != 200:
                        logger.error(
                            f"获取地图点位API调用异常，地址：{self.robot_support_map_points_url}, "
                            f"status: {response.status}, device_id: {device_id}, "
                            f"enterprise_id: {enterprise_id}"
                        )
                        return None
                    data = await response.json()
                    elapsed = time.time() - start
                    logger.info(
                        f"[MapPointsMonitor] 获取地图点位成功 - "
                        f"device_id: {device_id}, enterprise_id: {enterprise_id},data: {data} "
                        f"耗时: {elapsed:.2f}秒"
                    )
                    try:
                        points: list = (
                            data["level_entries"][0]["entry_map"].get("1", {}).get("word", [])
                        )
                        if "充电桩" in points:
                            points.remove("充电桩")
                        return sorted(points)
                    except Exception as e:
                        logger.error(f"解析地图点位数据失败: {e}")
                        return None
        except Exception as e:
            logger.error(f"获取地图点位异常: {e}")
            return None

    async def monitor_loop(self, device_id: str, enterprise_id: str, language: str = "zh_CN"):
        """监控循环"""
        while True:
            try:
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                logger.info(f"[MapPointsMonitor] 开始获取地图点位 - {current_time}")
                points = await self.get_map_points(device_id, enterprise_id, language)
                if points:
                    logger.info(
                        f"[MapPointsMonitor] 当前地图点位列表 - "
                        f"device_id: {device_id}, enterprise_id: {enterprise_id}\n"
                        f"点位数量: {len(points)}\n"
                        f"点位列表: {points}"
                    )
                else:
                    logger.warning(
                        f"[MapPointsMonitor] 获取地图点位失败 - "
                        f"device_id: {device_id}, enterprise_id: {enterprise_id}"
                    )
            except Exception as e:
                logger.error(f"[MapPointsMonitor] 监控循环异常: {e}")
            # 等待下一次执行
            await asyncio.sleep(self.interval)


# DEVICE_ID = "M03SCN1A14024530EC45"
DEVICE_ID = "M03SCN2B19025008383B"
REGION_TO_ENTERPRISE_ID = {
    "domestic": "orion.ovs.entprise.9945420568",
    "overseas": "orion.ovs.entprise.2465488082",
}
REGION_TO_ROBOT_LANGUAGE_CODE = {
    "domestic": "zh_CN",
    "overseas": "en_US",
}
REGION_TO_URL = {
    "domestic": "http://resource-online-test.ainirobot.com/open/v1/resource/dict",
    "overseas": "http://dev-voice-lite.ainirobot.com/speech-manager/v1/grpc/resource/dict",
}


async def main():
    # 配置日志
    logger.add(
        "logs/map_points_monitor.log",
        rotation="500 MB",
        retention="10 days",
        compression="zip",
        encoding="utf-8",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
    )

    # 创建监控实例
    ENV = "domestic"
    # ENV = "overseas"
    monitor = MapPointsMonitor(interval=5, URL=REGION_TO_URL[ENV])  # 每小时执行一次
    # 启动监控
    await monitor.monitor_loop(
        device_id=DEVICE_ID,
        enterprise_id=REGION_TO_ENTERPRISE_ID[ENV],
        language=REGION_TO_ROBOT_LANGUAGE_CODE[ENV],

    )
if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("监控程序已停止")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
