def detect_adjacent_same_faceid(file_path, window_size_range=(2, 4)):
    """
    检测文件中相邻行的相同faceId

    参数:
    file_path: 文件路径
    window_size_range: 检测窗口大小范围，默认为(2, 4)，即检测相邻的2-4行

    返回:
    异常行列表，每个元素为(行号, faceId, 连续出现次数)
    """
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    # 提取每行的faceId
    face_ids = []
    for i, line in enumerate(lines):
        if 'faceId:' in line:
            # 提取faceId值
            parts = line.split('faceId:')
            if len(parts) > 1:
                face_id = parts[1].split(',')[0].strip() if ',' in parts[1] else parts[1].strip()
                face_ids.append((i + 1, face_id))  # 存储行号和faceId
        else:
            face_ids.append((i + 1, None))  # 没有faceId的行

    # 检测相邻行的相同faceId
    anomalies = []
    min_window, max_window = window_size_range

    for i in range(len(face_ids)):
        if face_ids[i][1] is None:
            continue
        current_id = face_ids[i][1]
        consecutive_count = 1

        # 向后检查相邻行
        for j in range(i + 1, min(i + max_window, len(face_ids))):
            if face_ids[j][1] == current_id:
                consecutive_count += 1
            else:
                break

        # 如果连续出现次数在指定范围内，记录为异常
        if min_window <= consecutive_count <= max_window:
            anomalies.append((face_ids[i][0], current_id, consecutive_count))

    return anomalies


file_path = '/Users/<USER>/workspace/liangwei/iterm/AgentOS速度优化专项/免唤醒测试/2025-03-14/821/logs/ModelA_2.log'  # 替换为您的文件路径
anomalies = detect_adjacent_same_faceid(file_path)

# 打印结果
if anomalies:
    print("检测到以下异常:")
    for line_num, face_id, count in anomalies:
        print(f"行号: {line_num}, faceId: {face_id}, 连续出现: {count}次")
else:
    print("未检测到异常")
print(len(anomalies))
