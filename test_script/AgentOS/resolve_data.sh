#   处理免唤醒效果测试日志数据
#   获取当前日期
today=$(date +"%Y-%m-%d")
echo $today
log_data_path=/Users/<USER>/workspace/liangwei/iterm/AgentOS速度优化专项/免唤醒测试/$today
mkdir $log_data_path
ip=************
#   链接adb拉取日志
adb connect $ip:5555
echo "adb 连接成功"
echo "开始拉取日志"
adb pull /sdcard/logs/offlineLogs/821/ $log_data_path
echo "日志拉取完成"

echo "开始拉取图片"
adb pull /sdcard/VadFrame/ $log_data_path
echo "图片拉取完成"
#   进入日志路劲处理日志
cd $log_data_path/821
# 解压日志文件
mkdir -p logs
mkdir -p tmp_logs
echo "开始解压日志文件"
for file in *.tgz; do
    tar -xzf $file -C tmp_logs
    log_name=${file%.*}.log
    mv tmp_logs/* logs/$log_name
    echo "解压完成 $file"
done
echo "解压完成"
rm -rf tmp_logs
cd logs
echo "开始处理日志文件"
grep 'ModelA' *.log > ModelA.log
echo "ModelA.log 处理完成"

#   处理图片数据
cd $log_data_path/VadFrame
# 对所有符合模式的文件进行重命名
for file in *-????-??-??-??-??-??-???.jpg; do
  [[ -f "$file" ]] || continue  # 如果没有匹配的文件则跳过
  
  prefix=${file%-????-??-??-??-??-??-???.*}
  timestamp=${file#*-}
  timestamp=${timestamp%.*}
  extension=${file##*.}
  
  mv -v "$file" "${timestamp}-${prefix}.${extension}"
done
echo "图片处理完成"
adb disconnect $ip:5555
echo "adb 连接已经断开"
