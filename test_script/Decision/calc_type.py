# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : calc_type.py
<AUTHOR> <EMAIL>
@Time   : 2024-04-03 18:57:43
@Version: v1.0
"""

"""
统计每个类型的数据信息
"""
type_s = """
洞察内容不合适,未绘制图表,sql查询意图错误
未按原始意图洞察,洞察数据错误,洞察内容不合适,图表数据错误
无洞察
图表数据错误
洞察数据错误,洞察结论错误,未绘制图表,sql查询维度不足（列）
图例错误
未按原始意图洞察,洞察缺少关键指标
图表数据错误,图不合适
洞察缺少关键指标
洞察结论错误

洞察内容不合适,图不合适
未按原始意图洞察,图表内容截断
洞察数据错误,图表数据错误
未按原始意图洞察,图例错误
未按原始意图洞察
未按原始意图洞察,图类型错误,sql查询维度不足（列）
未按原始意图洞察,图表内容重叠,sql查询意图错误
未按原始意图洞察,图表内容重叠,sql查询意图错误
未按原始意图洞察,图表数据错误
未按原始意图洞察,图表内容重叠
未按原始意图洞察,图表内容重叠,图表数据错误,sql查询意图错误
未按原始意图洞察,图表数据错误
图表数据错误
洞察数据错误,图表数据错误,少图
"""
import json

data_list = type_s.split("\n")
type_dic = {}
for t in data_list:
    spilt_type = t.split(",")
    for st in spilt_type:
        if st in type_dic:
            type_dic[st] += 1
        elif st != '':
            type_dic[st] = 1

print(json.dumps(type_dic, ensure_ascii=False))
