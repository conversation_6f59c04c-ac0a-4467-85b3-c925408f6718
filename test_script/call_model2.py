# 盒子—打印耗时+性能指标
# kzy
# _*_ coding:utf-8 _*_

import os
import sys
try:
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    print(BASE_DIR)
    sys.path.append(BASE_DIR)
except Exception:
    print("导入失败")
from tool.util import read_xlsx_to_dict
from tool.logger import log
import requests
import json
import time
from datetime import datetime
import pandas as pd
from tool.custom_tokenizer import get_num_tokens
import concurrent.futures
import argparse


model_name = "OrionStarMessiah2"
model_url = "http://43.139.254.241:8007/v1/chat/completions"
headers = {
    "Content-Type": "application/json",
}


qa_prompt = """System：你是一位专业的测试工程师，专注于软件测试和技术领域。你的主要目标是确保软件产品的质量，发现并解决潜在的问题，以提供用户最佳的使用体验。你的个性特征是细致、有条理且富有耐心，你擅长分析测试结果、制定测试计划和进行问题解决。在与用户交互时，请保持专业且客观的语气，提供清晰准确的测试报告和建议，以帮助用户更好地理解软件产品的性能和质量。
你可以根据下面给出的参考资料和聊天历史来回答用户问题。

### 回答要求 ###
1. 尽你所能从上面参考资料中找答案，如果实在找不到答案，就用你自己的知识回答。
2. 如果向用户提出澄清问题有助于回答问题，可以尝试提问。
3. 如果你给出的答案里引用了参考资料中的内容，请在答案的结尾处添加你引用的Source_id，引用的Source_id值来自于参考资料中，并用两个方括号括起来。示例：[[d97b811489b73f46c8d2cb1bc888dbbe]]、[[b6be48868de736b90363d001c092c019]]
4. 请你以第一人称并且用严谨风格来回答问题，一定要用中文来回答，并且基于事实详细阐述。
Human: ### 参考资料 ###
["Source_id": e97d498e18624b54f90ddbccf5720a21,"Content": "江西直升机科技馆解说词【售票厅门口】各位同学大家好！欢迎来到江西直升机科技馆参观游览。江西直升机科技馆是全国目前唯一以直升机为主题所命名的展馆，在开馆不到一年时间被评选为国家级 4A 级景区，在国内景区中微乎及微。【公司愿景】 抬头我们看到的一行大字“让直升机飞入寻常百姓家”是我们的公司愿景。其中包含两个含义：一个含义是造老百姓买得起的直升机，第二个含义是造老百姓用得起的直升机，希望在不久的将来直升机能够和汽车一样成为普及的交通工具。我们公司总经理徐总从事 30 多年航空行业，熟知中国航空与世界航空存在巨大的差距，尤其是在直升机方面存在着差别巨大。回想海湾战争、科索沃战争等战争中，中国使用的武器对国外完全是无可还击之力，中国的空军在看过沙漠风暴之后，对驾驶飞机、直升机失去了信心，所以中国未来要想要立足，光靠老一辈人士的经验，是无法实现的，更需要的是更多的年轻人加入其中，加入直升机的研发、生产、全过程中，这就是公司办科技馆的真正目的。办科技馆的宗旨就是一次研学明确人生方向，缩短中外直升机的差距
办科技馆的宗旨就是一次研学明确人生方向，缩短中外直升机的差距。【AK1-3】现在我们面前的这第一架黄色的直升机，名叫 AK1-3，绰号“霸王蜂”，产自乌克兰，既可以作为教练机也可以作为私人直升机。最大飞行速度每小时 186 公里，最大航时 2-2.5 小时。这架直升机将是我们公司未来的重要的产品，也将是中国未来的产品，他即将被我们公司改造成为无人驾驶的有人机，不用考直升机
驾照，就可以直接乘坐这架直升机，这种技术在中国、甚至是世界上都是十分前沿的技术，是中国未来直升机的趋势。（真机）【米 4】 这架蓝色的直升机是来自于前苏联的米-4 直升机，它对中国来说意义十分重要，因为我们中国第一架直升机叫做直-5，它就是完全在前苏联的米-4 直升机基础上仿制而成，我们是完全按照米-4 图纸仿制、设计、制造出来，所以直-5 直升机对中国来说完全没有知识产权。（真机）【荣誉墙】 科技馆是 4A 级景区，同时也是江西省科普教育基地、江西省青少年研学之地、中国协会科普教育基地等。江西直升机公司总共有三大业务板块、分别是通航制造、通航运营、航空文化。科技馆是江西直升机公司的航空文化板块。江西直升机公司属于国家高新技术企业，通航制造是我们正在研发的无人驾驶直升机，中国最大起飞重量的小青龙无人机以及多款轻型的有人直升机。我们公司获得了海智工作站资质，是江西省首个获批的海智工作站，与海外很多合作伙伴保持合作，比如：引进了比利时、法国、乌克兰、意大利、英国等海外的高级专家支持我们工作站，同时我们还有昌飞、602 所的专家指导支持"]

["Source_id": e97d498e18624b54f90ddbccf5720a21,"Content": "们是江西省唯一一个有资质的培训单位）。2018 年小青龙获得了中国技术市场金桥奖，金桥奖也是对无人机最高的奖项，当时在人民大会堂对我们进行颁发。海洋馆我们进入的是海洋馆，海洋馆的主题就是在知识的海洋中遨游，从这里开启直升机的海洋之旅
海洋馆我们进入的是海洋馆，海洋馆的主题就是在知识的海洋中遨游，从这里开启直升机的海洋之旅。【云投影】首先映入眼帘的是一位美丽空姐，她在为我们讲解的是江西直升机科技馆的成立的背景与简介，想看直升机，了解直升机，我们科技馆就是您不二的选择！【360°全息投影以及全息投影】接下来，在我们面前的这个设备叫360°全息投影设备，当您站在黄色指定区域，对着第一个画面挥一挥手，直升机会飞到您的面前，欢迎您的光临；对着第二个画面挥一挥手，您可以亲自组装一架直升机，可以很直观的了解到直升机是由哪些部分组成；在我们的右侧方是一个立体式全息投影仪，它是通过激光产生的全新的影像技术，展现的是一个武装直升机，当您手放在感应器下面，直升机会进行简单的发射炮弹动作；对着第三个画面挥一挥手，直升机的心脏，也就是发动机，会左右 180°旋转；对着第四个画面挥一挥手，直升机机舱门会打开，欢迎您乘坐直升机；对着第五个画面挥一挥手，直升机再次回到您的面前！【电子书】书架上的图书有上千本关于航空类的专业书籍，感兴趣的朋友可以通过这个电子阅览屏观看，现在播放的是一本 1993 年的《国际航空杂志》，大家可以继续翻页查看其它图书。
书架的上层，还摆放了一些珍贵的直升机书籍和设计草稿图，是张振芳女士亲自捐赠给我们科技馆的。张振芳女士是 602 所非常权威和德高望重的直升机工程师，目前也是我们公司的直升机技术指导专家，这些书籍都是非常珍贵的历史记录，如果大家要观看图书，请轻拿轻放，保护好这些宝贵的资料，不要损坏到它们，谢谢配合。【3D 裸眼技术】接下来我们所看到的是 3D 裸眼技术，我们可以站在指定的区域观看，手放在感应位置，小青龙无人机就会完成起飞、悬停、自转、降落的任务，这架无人机就是我们江直公司自主研发的全国吨位最大的小青龙无人机，也是刚刚所说的获得中国技术金桥奖的无人机。海洋馆作为科技馆的前沿展馆，我们刚刚看到投影下面有很多关于航空方面的公式以及原理，可能大家对这些公式原理还比较陌生，接下来，我们通过小实验来解释这些公式及原理。【科里奥利效应】通过演示仪器，我们观察到，当两个桨叶的转动周长减小时，转动速度加快。科里奥利效应对于直升机旋翼的影响在于当桨叶旋转时，由于圆周运动的周长缩小使其旋转速度增大给予补偿。也就是说当圆周运动的桨尖距离主轴（圆心）越近则其旋转速度越大"]

["Source_id": e97d498e18624b54f90ddbccf5720a21,"Content": "江西直升机科技馆是全国唯一以直升机为主题命名的展馆，被评选为国家级4A级景区。公司愿景是让直升机飞入寻常百姓家，即制造老百姓买得起和用得起的直升机。公司总经理徐总从事航空行业30多年，深知中国航空与世界航空的差距，尤其是直升机方面。因此，公司办科技馆的真正目的是让更多年轻人加入直升机的研发、生产、全过程。科技馆的宗旨是缩短中外直升机的差距，明确人生方向。科技馆内展示了多架直升机，包括AK1-3和米-4等，以及公司的荣誉墙。科技馆是江西省科普教育基地、青少年研学之地等。江西直升机公司是国家高新技术企业，拥有整机制造资质，是江西省首个获批的海智工作站，与海外很多合作伙伴保持合作。"]

["Source_id": e97d498e18624b54f90ddbccf5720a21,"Content": "旋翼机是一种介于直升机和飞机之间的飞行器，利用前飞时的相对气流吹动旋翼自转以产生升力。与直升机的最大区别是，旋翼机的旋翼不与发动机传动系统相连，而是由前方气流吹动旋翼旋转产生升力。森林馆展示了一些直升机和旋翼机，包括美国唯一批准进入中国的 H21 直升机，以及比利时的 H3 超轻型双座直升机。馆内还有一些 VR 互动墙和飞行模拟体验游戏设备，可以让游客更直观地了解飞行器的组装和操作。"]

["Source_id": e97d498e18624b54f90ddbccf5720a21,"Content": "江西直升机科技馆是全国唯一以直升机为主题命名的展馆，展示了多架直升机和公司的荣誉墙。科技馆是江西省科普教育基地、青少年研学之地等。江西直升机公司是国家高新技术企业，拥有整机制造资质，是江西省首个获批的海智工作站，与海外很多合作伙伴保持合作。文章还提到了旋翼机，这是一种介于直升机和飞机之间的飞行器。"]

### 用户问题 ###
详细介绍下江西直升机科技馆有哪些景点
"""


def send_request(p):
    # p = qa_prompt.format(role=role)
    data = {
        "model": model_name,
        "stream": True,
        "messages": [{"role": "system", "content": p}],
        "temperature": 0
    }

    # 记录请求发送的时间（精确到毫秒）
    request_send_time = time.time()
    response = requests.post(model_url, headers=headers, json=data, stream=True)

    first_packet_time = None
    total_character_count = 0
    answer = ""
    # 处理响应数据
    error = 0
    error_answer = ""
    for line in response.iter_lines():
        response_line = line.decode()
        # print("返回数据包:", response_line)
        # 只处理第二行，因为第一行是空行
        if not first_packet_time:
            # 记录第一个数据包返回的时间（精确到毫秒）
            first_packet_time = time.time()

        # 处理响应数据
        if not line.startswith(b"data: "):
            error_answer = response_line
        else:
            # if line.startswith(b"data: "):
            json_data = line[6:]
            try:
                data_dict = json.loads(json_data)
            except json.JSONDecodeError:
                data = line.decode("utf-8")
                if data.__contains__("DONE"):
                    continue
                else:
                    log.error("json loads error:->{}".format(line))
                    continue
            if 'choices' in data_dict:
                choices = data_dict['choices']
                for choice in choices:
                    if choice["finish_reason"] == "stop":
                        break
                    if 'delta' in choice and 'content' in choice['delta']:
                        content = choice['delta']['content']
                        content_length = len(content)
                        answer += content
                        total_character_count += content_length
                    else:
                        error += 1
                        if error > 1:
                            log.error("prompt : {}".format(qa_prompt))
                            log.error("choices not delta or delta not content:->{}".format(line))
            else:
                log.error("response not choices:->{}".format(line))
    result = {
        "query": qa_prompt,
        "answer": answer,
    }
    return result


def save_print_to_excel(file_path):
    results = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        futures = []

        question_list = read_xlsx_to_dict(file_path)

        for question in question_list:

            futures.append(executor.submit(send_request, question.get("expect_answer")))
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as exc:
                print(f"Error: {exc}")
    df = pd.DataFrame(results)
    df.to_excel("out_expect.xlsx", index=False)
    return results


if __name__ == "__main__":
    res = send_request(qa_prompt)
    print(res["answer"])
    file_path = ""
    parser = argparse.ArgumentParser(description="处理命令行参数")
    # 并发数量参数化，不填默认为1
    parser.add_argument("--f", type=str, required=False, help="文件")
    # 解析参数
    args = parser.parse_args()
    if args.f is not None:
        file_path = args.f
    else:
        print("请输入文件路径")
        exit()

    save_print_to_excel(file_path=file_path)
