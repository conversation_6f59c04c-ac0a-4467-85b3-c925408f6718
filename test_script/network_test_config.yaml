# 机器人网络环境测试配置文件

# 基础配置
base_config:
  base_url: "https://api.chatmax.net"
  auth_token: "pw9SwJLvLLeuarN1W3u9X8fu3oZZvG"
  timeout: 30
  max_retries: 3

# 测试用例配置
test_cases:
  - name: "simple_query"
    description: "简单查询测试"
    method: "POST"
    endpoint: "/proxyapi/gateway_algo/autolm/test_query"
    data:
      max_tokens: 512
      stream: false
      dialogue:
        - role: "user"
          content: "你好"
    
  - name: "complex_query"
    description: "复杂查询测试"
    method: "POST"
    endpoint: "/proxyapi/gateway_algo/autolm/test_query"
    data:
      max_tokens: 1024
      stream: false
      dialogue:
        - role: "user"
          content: "请详细介绍人工智能的发展历史和未来趋势"
    
  - name: "stream_query"
    description: "流式查询测试"
    method: "POST"
    endpoint: "/proxyapi/gateway_algo/autolm/test_query"
    data:
      max_tokens: 512
      stream: true
      dialogue:
        - role: "user"
          content: "写一首关于春天的诗"

# 网络环境配置
network_conditions:
  - name: "normal"
    description: "正常网络环境"
    latency_ms: 0
    bandwidth_mbps: 100
    packet_loss_percent: 0.0
    jitter_ms: 0
    
  - name: "high_latency"
    description: "高延迟网络"
    latency_ms: 500
    bandwidth_mbps: 100
    packet_loss_percent: 0.0
    jitter_ms: 50
    
  - name: "low_bandwidth"
    description: "低带宽网络"
    latency_ms: 100
    bandwidth_mbps: 1
    packet_loss_percent: 0.0
    jitter_ms: 20
    
  - name: "unstable"
    description: "不稳定网络"
    latency_ms: 200
    bandwidth_mbps: 10
    packet_loss_percent: 5.0
    jitter_ms: 100
    
  - name: "mobile_3g"
    description: "3G移动网络"
    latency_ms: 300
    bandwidth_mbps: 0.384
    packet_loss_percent: 1.0
    jitter_ms: 150
    
  - name: "edge_network"
    description: "边缘网络环境"
    latency_ms: 150
    bandwidth_mbps: 5
    packet_loss_percent: 2.0
    jitter_ms: 80

# 测试计划配置
test_plans:
  quick_test:
    description: "快速测试方案"
    duration_minutes: 5
    test_cases: ["simple_query"]
    network_conditions: ["normal", "high_latency"]
    concurrent_users: 3
    iterations: 3
    
  comprehensive_test:
    description: "全面测试方案"
    duration_minutes: 30
    test_cases: ["simple_query", "complex_query", "stream_query"]
    network_conditions: ["normal", "high_latency", "low_bandwidth", "unstable"]
    concurrent_users: 10
    iterations: 5
    
  stability_test:
    description: "稳定性测试方案"
    duration_minutes: 120
    test_cases: ["simple_query", "complex_query"]
    network_conditions: ["normal", "unstable"]
    concurrent_users: 5
    iterations: 1
    continuous_duration_minutes: 60
    
  performance_test:
    description: "性能压力测试"
    duration_minutes: 15
    test_cases: ["simple_query"]
    network_conditions: ["normal"]
    concurrent_users: [1, 5, 10, 20, 50]
    iterations: 10

# 监控配置
monitoring:
  enable_real_time_monitoring: true
  metrics_collection_interval: 5  # 秒
  alert_thresholds:
    max_response_time_ms: 5000
    min_success_rate: 0.95
    max_error_rate: 0.05
  
  # 监控指标
  metrics:
    - response_time
    - success_rate
    - throughput
    - error_rate
    - connection_count
    - memory_usage
    - cpu_usage

# 报告配置
reporting:
  output_formats: ["json", "markdown", "html"]
  include_raw_data: true
  include_charts: true
  auto_open_report: false
  
  # 报告内容配置
  sections:
    - executive_summary
    - test_overview
    - performance_metrics
    - error_analysis
    - network_impact_analysis
    - recommendations
    
# 告警配置
alerts:
  enable_email_alerts: false
  enable_webhook_alerts: true
  webhook_url: "https://hooks.slack.com/services/YOUR/WEBHOOK/URL"
  
  alert_conditions:
    - condition: "success_rate < 0.9"
      severity: "high"
      message: "成功率低于90%"
    
    - condition: "avg_response_time > 3000"
      severity: "medium"
      message: "平均响应时间超过3秒"
    
    - condition: "error_rate > 0.1"
      severity: "high"
      message: "错误率超过10%"

# 环境特定配置
environments:
  development:
    base_url: "http://localhost:8080"
    timeout: 10
    max_retries: 1
    
  staging:
    base_url: "https://staging-api.chatmax.net"
    timeout: 20
    max_retries: 2
    
  production:
    base_url: "https://api.chatmax.net"
    timeout: 30
    max_retries: 3 