import subprocess


def convert_mp3_to_wav(input_file, output_file):
    ffmpeg_command = [
        'ffmpeg',
        '-i', input_file,
        output_file
    ]

    try:
        # 执行FFmpeg命令
        subprocess.run(ffmpeg_command, check=True)
        print(f"转换成功: {output_file}")
    except subprocess.CalledProcessError as e:
        print(f"转换失败: {e}")


# 输入和输出文件路径
input_file = '/Users/<USER>/Downloads/古窑博览区-普通话.mp3'
output_file = '/Users/<USER>/Downloads/古窑博览区-普通话.wav'

# 调用转换函数
convert_mp3_to_wav(input_file, output_file)
