import os
from gtts import gTTS
from pydub import AudioSegment

# # 读取文本文件
# with open("/Users/<USER>/workspace/liangwei/测试集源文档/社保政策.txt", "r", encoding="utf-8") as file:
#     text = file.read()

# # 使用 gTTS 将文本转换为音频
# tts = gTTS(text=text, lang='zh')

# # 保存为临时的 WAV 文件
# tts.save("temp.wav")

# # 使用 pydub 将 WAV 文件转换为 MP3 文件
# audio = AudioSegment.from_wav("temp.wav")
# audio.export("社保政策.mp3", format="mp3")

# # 删除临时的 WAV 文件
# os.remove("temp.wav")

# print("转换完成：output.mp3")


# 生成10秒的无声音频
duration_ms = 10 * 1000  # 10秒
silent_audio = AudioSegment.silent(duration=duration_ms)

# 保存为文件
silent_audio.export("silent_audio.wav", format="wav")
