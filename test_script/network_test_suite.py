#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器人网络环境测试套件
测试不同网络条件下机器人的响应性能和稳定性
"""

import time
import json
import logging
import statistics
from dataclasses import dataclass, asdict
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
import socket

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('network_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class NetworkCondition:
    """网络条件配置"""
    name: str
    latency_ms: int = 0
    bandwidth_mbps: int = 100
    packet_loss_percent: float = 0.0
    jitter_ms: int = 0
    description: str = ""


@dataclass
class TestResult:
    """测试结果数据结构"""
    test_name: str
    network_condition: str
    start_time: float
    end_time: float
    response_time_ms: float
    status_code: int
    success: bool
    error_message: str = ""
    content_length: int = 0


class NetworkTestSuite:
    """网络测试套件"""

    def __init__(self, base_url: str, auth_token: str = ""):
        self.base_url = base_url
        self.auth_token = auth_token
        self.session = requests.Session()

        # 设置请求头
        if auth_token:
            self.session.headers.update({
                'Authorization': f'Bearer {auth_token}',
                'Content-Type': 'application/json'
            })

        # 定义网络条件
        self.network_conditions = [
            NetworkCondition("normal", 0, 100, 0.0, 0, "正常网络"),
            NetworkCondition("high_latency", 500, 100, 0.0, 0, "高延迟网络"),
            NetworkCondition("low_bandwidth", 0, 1, 0.0, 0, "低带宽网络"),
            NetworkCondition("unstable", 100, 10, 5.0, 50, "不稳定网络"),
            NetworkCondition("edge_network", 200, 5, 2.0, 30, "边缘网络"),
        ]

        # 测试用例配置
        self.test_cases = [
            {
                "name": "simple_query",
                "method": "POST",
                "endpoint": "/proxyapi/gateway_algo/autolm/test_query",
                "data": {
                    "max_tokens": 512,
                    "stream": False,
                    "dialogue": [{"role": "user", "content": "你好"}]
                }
            },
            {
                "name": "complex_query",
                "method": "POST",
                "endpoint": "/proxyapi/gateway_algo/autolm/test_query",
                "data": {
                    "max_tokens": 1024,
                    "stream": False,
                    "dialogue": [{"role": "user", "content": "请详细介绍人工智能的发展历史"}]
                }
            },
            {
                "name": "stream_query",
                "method": "POST",
                "endpoint": "/proxyapi/gateway_algo/autolm/test_query",
                "data": {
                    "max_tokens": 512,
                    "stream": True,
                    "dialogue": [{"role": "user", "content": "写一首诗"}]
                }
            }
        ]

        self.results: List[TestResult] = []

    def test_connection(self, host: str, port: int, timeout: float = 5.0) -> bool:
        """测试基础连接"""
        try:
            socket.create_connection((host, port), timeout)
            return True
        except Exception as e:
            logger.error(f"连接失败 {host}:{port} - {e}")
            return False

    def single_request_test(self, test_case: Dict, network_condition: NetworkCondition) -> TestResult:
        """单次请求测试"""
        start_time = time.time()

        try:
            # 设置超时时间（根据网络条件调整）
            timeout = 10 + (network_condition.latency_ms / 1000) * 2

            url = f"{self.base_url}{test_case['endpoint']}"

            if test_case.get('data'):
                response = self.session.post(
                    url,
                    json=test_case['data'],
                    timeout=timeout,
                    stream=test_case['data'].get('stream', False)
                )
            else:
                response = self.session.get(url, timeout=timeout)

            end_time = time.time()
            response_time = (end_time - start_time) * 1000

            # 处理流式响应
            content_length = 0
            if test_case['data'].get('stream', False):
                for chunk in response.iter_lines(chunk_size=512):
                    if chunk:
                        content_length += len(chunk)
            else:
                content_length = len(response.content)

            result = TestResult(
                test_name=test_case['name'],
                network_condition=network_condition.name,
                start_time=start_time,
                end_time=end_time,
                response_time_ms=response_time,
                status_code=response.status_code,
                success=response.status_code == 200,
                content_length=content_length
            )

        except requests.exceptions.Timeout:
            end_time = time.time()
            result = TestResult(
                test_name=test_case['name'],
                network_condition=network_condition.name,
                start_time=start_time,
                end_time=end_time,
                response_time_ms=(end_time - start_time) * 1000,
                status_code=408,
                success=False,
                error_message="Request timeout"
            )

        except Exception as e:
            end_time = time.time()
            result = TestResult(
                test_name=test_case['name'],
                network_condition=network_condition.name,
                start_time=start_time,
                end_time=end_time,
                response_time_ms=(end_time - start_time) * 1000,
                status_code=0,
                success=False,
                error_message=str(e)
            )

        return result

    def concurrent_test(self, test_case: Dict, network_condition: NetworkCondition,
                        concurrent_users: int = 10, duration_seconds: int = 60) -> List[TestResult]:
        """并发测试"""
        logger.info(f"开始并发测试: {test_case['name']} - {network_condition.name} - {concurrent_users}并发用户")

        results = []
        end_time = time.time() + duration_seconds

        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = []

            while time.time() < end_time:
                # 提交并发请求
                for _ in range(concurrent_users):
                    future = executor.submit(self.single_request_test, test_case, network_condition)
                    futures.append(future)

                # 收集已完成的结果
                completed_futures = []
                future_to_result = {future: future for future in futures}

                for future in as_completed(future_to_result, timeout=1):
                    try:
                        result = future.result()
                        results.append(result)
                        completed_futures.append(future)
                    except Exception as e:
                        logger.error(f"并发测试异常: {e}")

                # 移除已完成的futures
                futures = [f for f in futures if f not in completed_futures]

                time.sleep(0.1)  # 避免过于频繁的请求

        return results

    def stability_test(self, test_case: Dict, network_condition: NetworkCondition,
                       duration_minutes: int = 30) -> List[TestResult]:
        """稳定性测试"""
        logger.info(f"开始稳定性测试: {test_case['name']} - {network_condition.name} - {duration_minutes}分钟")

        results = []
        end_time = time.time() + (duration_minutes * 60)

        while time.time() < end_time:
            result = self.single_request_test(test_case, network_condition)
            results.append(result)

            # 记录进度
            if len(results) % 10 == 0:
                success_rate = sum(1 for r in results if r.success) / len(results)
                avg_response_time = statistics.mean([r.response_time_ms for r in results])
                logger.info(f"稳定性测试进度: {len(results)}次请求, 成功率: {success_rate:.2%}, 平均响应时间: {avg_response_time:.2f}ms")

            time.sleep(2)  # 每2秒发送一次请求

        return results

    def analyze_results(self, results: List[TestResult]) -> Dict[str, Any]:
        """分析测试结果"""
        if not results:
            return {}

        success_results = [r for r in results if r.success]
        failed_results = [r for r in results if not r.success]

        response_times = [r.response_time_ms for r in success_results]

        analysis = {
            "total_requests": len(results),
            "successful_requests": len(success_results),
            "failed_requests": len(failed_results),
            "success_rate": len(success_results) / len(results) if results else 0,
            "response_time_stats": {
                "min": min(response_times) if response_times else 0,
                "max": max(response_times) if response_times else 0,
                "avg": statistics.mean(response_times) if response_times else 0,
                "median": statistics.median(response_times) if response_times else 0,
                "p95": self._percentile(response_times, 95) if response_times else 0,
                "p99": self._percentile(response_times, 99) if response_times else 0,
            },
            "error_distribution": self._get_error_distribution(failed_results),
            "throughput_qps": len(results) / ((results[-1].end_time - results[0].start_time) if len(results) > 1 else 1)
        }

        return analysis

    def _percentile(self, data: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = int(len(sorted_data) * percentile / 100)
        return sorted_data[min(index, len(sorted_data) - 1)]

    def _get_error_distribution(self, failed_results: List[TestResult]) -> Dict[str, int]:
        """获取错误分布"""
        error_dist = {}
        for result in failed_results:
            key = f"{result.status_code}_{result.error_message}"
            error_dist[key] = error_dist.get(key, 0) + 1
        return error_dist

    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行全面测试"""
        logger.info("开始运行网络环境综合测试")

        all_results = {}

        for network_condition in self.network_conditions:
            logger.info(f"测试网络条件: {network_condition.name} - {network_condition.description}")

            condition_results = {}

            for test_case in self.test_cases:
                logger.info(f"执行测试用例: {test_case['name']}")

                # 基础功能测试
                basic_results = []
                for i in range(5):  # 执行5次基础测试
                    result = self.single_request_test(test_case, network_condition)
                    basic_results.append(result)

                # 并发测试
                concurrent_results = self.concurrent_test(test_case, network_condition,
                                                          concurrent_users=5, duration_seconds=30)

                # 合并结果
                test_results = basic_results + concurrent_results

                # 分析结果
                analysis = self.analyze_results(test_results)

                condition_results[test_case['name']] = {
                    "results": [asdict(r) for r in test_results],
                    "analysis": analysis
                }

            all_results[network_condition.name] = condition_results

        return all_results

    def generate_report(self, test_results: Dict[str, Any]) -> str:
        """生成测试报告"""
        report = []
        report.append("# 机器人网络环境测试报告\n")
        report.append(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        report.append("=" * 60 + "\n")

        # 汇总信息
        total_requests = 0
        total_success = 0

        for network_name, network_results in test_results.items():
            report.append(f"\n## 网络条件: {network_name}\n")

            for test_name, test_data in network_results.items():
                analysis = test_data['analysis']
                total_requests += analysis['total_requests']
                total_success += analysis['successful_requests']

                report.append(f"### 测试用例: {test_name}\n")
                report.append(f"- 总请求数: {analysis['total_requests']}\n")
                report.append(f"- 成功请求数: {analysis['successful_requests']}\n")
                report.append(f"- 成功率: {analysis['success_rate']:.2%}\n")
                report.append(f"- 平均响应时间: {analysis['response_time_stats']['avg']:.2f}ms\n")
                report.append(f"- P95响应时间: {analysis['response_time_stats']['p95']:.2f}ms\n")
                report.append(f"- P99响应时间: {analysis['response_time_stats']['p99']:.2f}ms\n")
                report.append(f"- 吞吐量: {analysis['throughput_qps']:.2f} QPS\n")

                if analysis['error_distribution']:
                    report.append("- 错误分布:\n")
                    for error, count in analysis['error_distribution'].items():
                        report.append(f"  - {error}: {count}次\n")
                report.append("\n")

        # 总体汇总
        overall_success_rate = total_success / total_requests if total_requests > 0 else 0
        report.append("\n## 总体汇总\n")
        report.append(f"- 总请求数: {total_requests}\n")
        report.append(f"- 总成功率: {overall_success_rate:.2%}\n")

        return "".join(report)

    def save_results(self, test_results: Dict[str, Any], filename: str = ""):
        """保存测试结果"""
        if not filename:
            filename = f"network_test_results_{int(time.time())}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)

        logger.info(f"测试结果已保存到: {filename}")

        # 同时生成报告
        report_filename = filename.replace('.json', '_report.md')
        report = self.generate_report(test_results)
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report)

        logger.info(f"测试报告已保存到: {report_filename}")


def main():
    """主函数"""
    # 配置测试参数
    base_url = "https://api.chatmax.net"  # 根据实际情况修改
    auth_token = "pw9SwJLvLLeuarN1W3u9X8fu3oZZvG"  # 根据实际情况修改

    # 创建测试套件
    test_suite = NetworkTestSuite(base_url, auth_token)

    try:
        # 运行测试
        results = test_suite.run_comprehensive_test()

        # 保存结果
        test_suite.save_results(results)

        logger.info("网络环境测试完成")

    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试执行异常: {e}")


if __name__ == "__main__":
    main()
