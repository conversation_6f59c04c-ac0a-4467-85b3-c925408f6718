import xml.etree.ElementTree as ET
from openpyxl import Workbook


def parse_mm_to_excel(mm_file, excel_file):
    # Parse the .mm file
    tree = ET.parse(mm_file)
    root = tree.getroot()
    
    # Create a new Excel workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "Mind Map Data"

    # Recursive function to parse nodes and write to Excel
    def write_node_to_excel(node, depth=0, row=1):
        nonlocal ws
        # Write the current node's text to Excel
        ws.cell(row=row, column=depth + 1, value=node.get('TEXT'))
        current_row = row

        # Recursively process child nodes
        for child in node.findall('node'):
            current_row = write_node_to_excel(child, depth + 1, current_row + 1)
        
        return current_row

    # Process the root node of the mind map
    for child in root.findall('node'):
        write_node_to_excel(child)

    # Save the Excel file
    wb.save(excel_file)


# Usage example
mm_file = '/Users/<USER>/Downloads/智能带教V1.0.0.mm'  # Your .mm file path
excel_file = '智能带教V1.0.0.xlsx'
parse_mm_to_excel(mm_file, excel_file)
print(f"Data has been successfully written to {excel_file}")
