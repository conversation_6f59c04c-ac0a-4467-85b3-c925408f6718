# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : clean_data.py
<AUTHOR> <EMAIL>
@Time   : 2023-11-24 10:21:55
@Version: v1.0
"""
import openpyxl

from tool.util import open_xlsx_for_path,wordinjaFunc
import re


def get_10_len_data():
    book = openpyxl.Workbook()
    sheet = book.active
    sheet.append(["一级分类", "二级分类", "敏感词"])
    workbook = open_xlsx_for_path("数美提供敏感词库.xlsx")
    workbook_list = list(workbook)
    f = open("del_sensitive.txt", "w")
    num = 0
    total= 0
    group_s=0
    excel = 0
    for sheet_name in workbook_list:

        for row in workbook[sheet_name].iterrows():
            sensitive = str(row[1][2])
            total+=1
            group = re.match("^[#a-zA-Z].+[a-zA-Z]$", sensitive)
            if group:
                # sensitive=wordinjaFunc(sensitive)
                group_s+=1
                # word_list = sensitive.split(" ")
                # if word_list.__len__() <= 5:
                sheet.append([row[1][0], row[1][1], sensitive])
            elif sensitive.__len__() <= 10 or sensitive.__contains__("|"):
                excel+=1
                sheet.append([row[1][0], row[1][1], sensitive.replace("|","==")])
            else:
                num += 1
                # print(row[1][2])
                f.write(str(row[1][2]) + "\n")
        print("总计：{}\ngroup 写入{}\n,execl 写入：{}\n总计写入表格：{}\ntxt写入：{}".format(total,group_s,excel,group_s+excel,num))
    f.close()
    book.save("敏感词.xlsx")

# if __name__ == '__main__':
    # get_10_len_data()
