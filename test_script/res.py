import pandas as pd
import json
import os
import sys
from decimal import Decimal
try:
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    print(BASE_DIR)
    sys.path.append(BASE_DIR)
except Exception:
    raise BaseException("添加当前目录到path失败")


def read_xlsx_to_dict(file_name, sheet="Sheet1"):
    """

    :param file_name:
    :param sheet:
    :return: dict{} or list[{}]
    """
    xlsx = pd.read_excel(file_name)
    dict_data = xlsx.to_dict(orient="records")
    return dict_data

# 定义自定义序列化函数


def decimal_default(obj):
    if isinstance(obj, Decimal):
        return float(obj)
    raise TypeError


f_path = "/Users/<USER>/Desktop/AMBI_数据问答测试集_数据问答评测_0920_100条.xlsx"
question_list = read_xlsx_to_dict(f_path)

index = 0
data_list = []
for question in question_list:

    print(index)
    log_info = question.get("日志")
    chart = "====指标chart结果====\\n"
    end_str = "\\n====开始执行报告===="
    chart_len = chart.__len__()
    sql_start_index = log_info.find(chart)
    sql_end_index = log_info.find(end_str)
    if sql_end_index != -1:
        forward_goal_sql = log_info[sql_start_index + chart_len:sql_end_index].replace("\\", "").replace(' ', '')
        print(forward_goal_sql)
        try:
            data = eval(forward_goal_sql)
            json_data = json.dumps(data, default=decimal_default, ensure_ascii=False)
            question["解析日志"] = json_data
        except Exception as e:
            print("报错了")
            question["解析报错"] = "true"
# 将字典转换为 JSON 格式

        data_list.append(question)
    else:
        question["解析日志"] = ""
        data_list.append(question)
    index += 1
df = pd.DataFrame(data_list)
df.to_excel("data.xlsx", index=False)
print("done")

#   快速排序算法自己实现
def quickSort(arr):
    # 递归结束条件：数组长度小于等于1时，直接返回
    if len(arr) <= 1:
        return arr
    # 选取基准值
    pivot = arr[0]
    left = [x for x in arr[1:] if x <= pivot]
    right = [x for x in arr[1:] if x > pivot]
    # 递归调用
    return quickSort(left) + [pivot] + quickSort(right)
    # 测试代码
arr = [5, 3, 8, 6, 2, 7, 1, 4]
sorted_arr = quickSort(arr)
print(sorted_arr)
