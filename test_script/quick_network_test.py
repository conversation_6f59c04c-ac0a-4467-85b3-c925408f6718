#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速网络测试示例
演示如何使用网络测试套件进行简单的测试
"""

import sys
import time
import logging
from network_test_suite import NetworkTestSuite, NetworkCondition

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def quick_test():
    """快速测试示例"""
    # 配置测试参数（请根据实际情况修改）
    base_url = "https://api.chatmax.net"
    auth_token = "pw9SwJLvLLeuarN1W3u9X8fu3oZZvG"

    # 创建测试套件
    test_suite = NetworkTestSuite(base_url, auth_token)

    # 简化的测试用例
    simple_test_case = {
        "name": "quick_test",
        "method": "POST",
        "endpoint": "/proxyapi/gateway_algo/autolm/test_query",
        "data": {
            "max_tokens": 256,
            "stream": False,
            "dialogue": [{"role": "user", "content": "你好，请简单介绍一下自己"}]
        }
    }

    # 测试不同网络条件
    network_conditions = [
        NetworkCondition("normal", 0, 100, 0.0, 0, "正常网络"),
        NetworkCondition("slow", 200, 1, 2.0, 50, "慢速网络"),
    ]

    all_results = {}

    logger.info("开始快速网络测试...")

    for condition in network_conditions:
        logger.info(f"测试网络条件: {condition.name}")

        # 执行3次测试
        results = []
        for i in range(3):
            logger.info(f"执行第 {i+1} 次测试")
            result = test_suite.single_request_test(simple_test_case, condition)
            results.append(result)

            # 显示结果
            if result.success:
                logger.info(f"成功 - 响应时间: {result.response_time_ms:.2f}ms")
            else:
                logger.error(f"失败 - {result.error_message}")

            time.sleep(1)  # 间隔1秒

        # 分析结果
        analysis = test_suite.analyze_results(results)
        all_results[condition.name] = {
            "condition": condition.description,
            "analysis": analysis
        }

        logger.info(f"{condition.name} 测试完成:")
        logger.info(f"  成功率: {analysis.get('success_rate', 0):.2%}")
        logger.info(f"  平均响应时间: {analysis.get('response_time_stats', {}).get('avg', 0):.2f}ms")
        logger.info("")

    # 生成简单报告
    logger.info("=" * 50)
    logger.info("测试总结报告")
    logger.info("=" * 50)

    for condition_name, data in all_results.items():
        analysis = data['analysis']
        logger.info(f"{data['condition']}:")
        logger.info(f"  总请求数: {analysis.get('total_requests', 0)}")
        logger.info(f"  成功率: {analysis.get('success_rate', 0):.2%}")

        stats = analysis.get('response_time_stats', {})
        logger.info(f"  响应时间 - 最小: {stats.get('min', 0):.2f}ms")
        logger.info(f"  响应时间 - 平均: {stats.get('avg', 0):.2f}ms")
        logger.info(f"  响应时间 - 最大: {stats.get('max', 0):.2f}ms")
        logger.info("")


def connectivity_test():
    """连通性测试"""
    logger.info("开始连通性测试...")

    import socket

    # 测试目标
    test_targets = [
        ("api.chatmax.net", 443),
        ("*******", 53),
        ("github.com", 443)
    ]

    for host, port in test_targets:
        try:
            start_time = time.time()
            socket.create_connection((host, port), timeout=5)
            end_time = time.time()

            connect_time = (end_time - start_time) * 1000
            logger.info(f"✓ {host}:{port} - 连接成功，耗时: {connect_time:.2f}ms")

        except Exception as e:
            logger.error(f"✗ {host}:{port} - 连接失败: {e}")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='快速网络测试工具')
    parser.add_argument('--mode', choices=['quick', 'connectivity'],
                        default='quick', help='测试模式')

    args = parser.parse_args()

    try:
        if args.mode == 'quick':
            quick_test()
        elif args.mode == 'connectivity':
            connectivity_test()

    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
