# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : T.py
<AUTHOR> <EMAIL>
@Time   : 2024-02-21 19:14:46
@Version: v1.0
"""
from tool.util import open_xlsx_for_path
import jsonlines

def gen_all_rule():
    path = "/Users/<USER>/workspace/liangwei/双汇需求/25条case/25条优化改写后的.xlsx"
    out_file = "25_modify_rule.jsonl"
    x = open_xlsx_for_path(path,sheet_name="Sheet1")
    i = 1
    for row in x.iterrows():
        f = jsonlines.open(out_file, "a")
        tmp = {
            "number": "1",
            "user_rule": "",
            "prompt_versions": {
                "optimize_version": "1",
                "interpret_sql_version": "1",
                "interpret_code_version": "1",
                "interpret_insight_version": "1",
                "generate_sql_version": "1",
                "generate_coding_version": "1",
                "generate_insight_version": "1",
                "generate_chart_version": "1"
            },
            "data": "双汇"
        }
        rule = row[1].get("data")
        tmp["user_rule"] = rule
        tmp["number"] = str(i)
        f.write(tmp)
        f.close()
        i += 1

gen_all_rule()