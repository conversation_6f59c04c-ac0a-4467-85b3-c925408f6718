# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : get_meg.py
<AUTHOR> <EMAIL>
@Time   : 2024-03-14 11:11:30
@Version: v1.0
"""

import jsonlines,regex,re
import pandas as pd
def optimizeProcess(prompt:str):
  if not prompt:return None
  reg = "## 用户规则\n(.+)\n\n## 返回格式"
  reg=r'User Rule\n(.+)\n\n\## Return Format'
  res = re.search(reg,prompt,re.DOTALL)
  rule = res.group(1)
  return rule

def pre_sql(prompt:str):
  if not prompt:return None
  reg = "# 用户规则\n(.+)\n\n返回的结果是一个JSON格式的字符串"
  reg = r"\*\*User Rule\*\*\n(.+)\n\nThe returned"
  res = regex.search(reg,prompt)
  rule = res.group(1)
  return rule

def pre_code(prompt:str):
  if not prompt:return None
  reg = "## 指令\n(.+)\n\n\n返回的结果是一个JSON格式的字符串"
  reg = r"## instruction\n(.+)\n\nresponse is"
  res = regex.search(reg,prompt)
  rule = res.group(1)
  return rule

def pre_insight(prompt:str):
  if not prompt:return None
  reg = "## 问题\n+\s*(.+)\n+\s*## 示例"
  res = regex.search(reg,prompt)
  rule = res.group(1)
  return rule

def gen_sql(prompt:str,version:str):
  if not prompt:return None
  v2_rule_pattern = r"Background:(.*?)SQL Instruction:"
  v2_plan_pattern = r"SQL Instruction:(.*?)\n\nBelow"
  v1_rule_pattern = r"## 分析规则\n(.+)\n\n##优化后的规则"
  v1_plan_pattern = r"\n\n##优化后的规则(.*?)\n\n## 数据库结构"
  if version =="v1":
    rule_match = re.search(v1_rule_pattern, prompt,re.DOTALL)
    plan_match = re.search(v1_plan_pattern, prompt,re.DOTALL)
  elif version=="v2":

    rule_match = re.search(v2_rule_pattern, prompt,re.DOTALL)
    plan_match = re.search(v2_plan_pattern, prompt,re.DOTALL)
  data={}

  rule = rule_match.group(1).strip()
  data["rule"]=rule


  plan = plan_match.group(1).strip()
  data["plan"]=plan
  return data


def gen_code(prompt:str,version:str):
  if not prompt:return None
  rule_pattern_v2 = r"Background: (.*?)\nto addresses this goal: "
  plan_pattern_v2 = r"addresses this goal: (.*?). DO NOT "
  rule_pattern_v1 = r"规则:\n(.*?)\n\n##优化后的规则"
  plan_pattern_v1 = r"##优化后的规则\n(.*?)\n\n##历史运行错误"
  if version=="v2":
    rule_match = re.search(rule_pattern_v2, prompt,re.DOTALL)
    plan_match = re.search(plan_pattern_v2, prompt,re.DOTALL)
  elif version=="v1":
    rule_match = re.search(rule_pattern_v1, prompt,re.DOTALL)
    plan_match = re.search(plan_pattern_v1, prompt,re.DOTALL)
  data={}

  rule = rule_match.group(1).strip()
  data["rule"]=rule


  plan = plan_match.group(1).strip()
  data["plan"]=plan
  return data

def gen_chart(prompt:str):
  if not prompt:return None
  reg = r"this goal: (.+). DO NOT"
  res = re.search(reg,prompt,re.DOTALL)
  rule = res.group(1)
  return rule

def gen_insight(prompt:str):
  if not prompt:return None
  reg = r"问题：\n+(.+)\n+## 计算完成后的数据结果"
  res = re.search(reg,prompt,re.DOTALL)
  if res:
    rule = res.group(1)
    return rule
  else:
    rule_pattern = r"## BACKGROUND(.*?)\n## RULE"
    plan_pattern = r"## RULE(.*?)\n\n## METRIC"

    rule_match = re.search(rule_pattern, prompt,re.DOTALL)
    plan_match = re.search(plan_pattern, prompt,re.DOTALL)
    data={}
    if rule_match:
      rule = rule_match.group(1).strip()
      data["rule"]=rule

    if plan_match:
      plan = plan_match.group(1).strip()
      data["plan"]=plan
    return data


def extract_field():
  gap="/Users/<USER>/workspace/liangwei/双汇需求/25条case/QWEN32B/4.13-sql/qwen32b_output_20240411123401/全流程任务测试集/result.jsonl"

  with open(gap, "r+", encoding="utf8") as f:
    rule_result=[]
    for item in jsonlines.Reader(f):
      data = get_data(item)
      # data = get_qwen_result(item)
      rule_result.append(data)
    df = pd.DataFrame(rule_result)
    df.to_excel("0413_gensql_qwen32b.xlsx")

def get_qwen_result(item):
  # 获取QWEN和Yi34B的结果
  tmp={}
  res = item.get("raw_result")
  prompt=item.get("prompt")
  tmp["generateInsight"]=res
  tmp["prompt"]=prompt
  return tmp


def get_data(item):
  v="v1"
  v_2="v2"
  tmp={
    "optimizeProcess":{},
    "interpretSqlProcess":{},
    "interpretCodeProcess":{},
    "interpretInsightProcess":{},
    "generateSqlProcess":{},
    "generateCodeProcess":{},
    "generateChartProcess":{},
    "generateInsightProcess":{}
  }
  user_rule = optimizeProcess(item.get("optimizeProcess",{}).get("prompt"))
  if user_rule:
    item.get("optimizeProcess")["prompt"]=user_rule
    tmp["optimizeProcess_rule"]=user_rule
    tmp["optimizeProcess_result"]=item.get("optimizeProcess",{}).get("specific_result")
  tmp["number"]=item.get("optimizeProcess",{}).get("number")
  # pre_sql_rule = pre_sql(item.get("interpretSqlProcess",{}).get("prompt"))
  # if pre_sql_rule:
  #   item.get("interpretSqlProcess")["prompt"]=pre_sql_rule
  #   tmp["interpretSqlProcess"]["rule"]=pre_sql_rule
  #   tmp["interpretSqlProcess"]["result"]=item.get("interpretSqlProcess",{}).get("specific_result")
  # pre_code_rule= pre_code(item.get("interpretCodeProcess",{}).get("prompt"))
  # if pre_code_rule:
  #   item.get("interpretCodeProcess")["prompt"]=pre_code_rule
  #   tmp["interpretCodeProcess"]["rule"]=pre_code_rule
  #   tmp["interpretCodeProcess"]["result"]=item.get("interpretCodeProcess",{}).get("specific_result")
  # pre_insight_rule = pre_insight(item.get("interpretInsightProcess",{}).get("prompt"))
  # if pre_insight_rule:
  #   item.get("interpretInsightProcess")["prompt"]=pre_insight_rule
  #   tmp["interpretInsightProcess"]["rule"]=pre_insight_rule
  #   tmp["interpretInsightProcess"]["result"]=item.get("interpretInsightProcess",{}).get("specific_result")
  # gen_sql_rule = gen_sql(item.get("generateSqlProcess",{}).get("prompt"),version=v)
  # if gen_sql_rule:
  #   item.get("generateSqlProcess")["prompt"]=gen_sql_rule
  #   tmp["generateSqlProcess"]["rule"]=gen_sql_rule
  tmp["generateSqlProcess_result"]=item.get("generateSqlProcess",{}).get("specific_result") if item.get("generateSqlProcess",{}).get("status",{}) else ""
  # gen_code_rule = gen_code(item.get("generateCodeProcess",{}).get("prompt"),version=v_2)
  # if gen_code_rule:
  #   item.get("generateCodeProcess")["prompt"]=gen_code_rule
  #   tmp["generateCodeProcess_rule"]=gen_code_rule
  #   tmp["generateCodeProcess_result"]=item.get("generateCodeProcess",{}).get("specific_result").replace("\n","\n")
  # gen_chart_rule = gen_chart(item.get("generateChartProcess",{}).get("prompt"))
  # if gen_chart_rule:
  #   item.get("generateChartProcess")["prompt"]=gen_chart_rule
  #   tmp["generateChartProcess"]["rule"]=gen_chart_rule
  #   tmp["generateChartProcess"]["result"]=item.get("generateChartProcess",{}).get("specific_result")
  gen_insight_rule= gen_insight(item.get("generateInsightProcess",{}).get("prompt"))
  if gen_insight_rule:
    pass
    # item.get("generateInsightProcess")["prompt"]=gen_insight_rule
    # tmp["generateInsightProcess"]["rule"]=gen_insight_rule
  tmp["generateInsightProcess_result"]=item.get("generateInsightProcess",{}).get("specific_result").replace("\n","\n") if item.get("generateInsightProcess",{}).get("specific_result") else item.get("generateInsightProcess",{}).get("specific_result")
  tmp["number"]=item.get("generateSqlProcess",{}).get("number")

  return tmp
  # f = jsonlines.open(out_file,"a")
  # f.write(tmp)
  # f.close()

extract_field()