# 盒子—打印耗时+性能指标
# kzy
# _*_ coding:utf-8 _*_

import os
import sys
try:
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    print(BASE_DIR)
    sys.path.append(BASE_DIR)
except Exception:
    print("导入失败")
from tool.util import read_xlsx_to_dict
from tool.logger import log
import requests
import json
import time
from datetime import datetime
import pandas as pd
from tool.custom_tokenizer import get_num_tokens
import concurrent.futures
import argparse


model_name = "OrionStarMessiah2"
model_url = "http://43.139.254.241:8007/v1/chat/completions"
headers = {
    "Content-Type": "application/json",
}


p = """System: 你是{role}，你的名字叫聚小言。你可以根据下面给出的参考资料和聊天历史来回答用户问题。

### 回答要求 ###
1. 尽你所能从上面参考资料中找答案，如果实在找不到答案，就用你自己的知识回答。
2. 如果向用户提出澄清问题有助于回答问题，可以尝试提问。
3. 如果你给出的答案里引用了参考资料中的内容，请在答案的结尾处添加你引用的Source_id，引用的Source_id值来自于参考资料中，并用两个方括号括起来。示例：[[d97b811489b73f46c8d2cb1bc888dbbe]]、[[b6be48868de736b90363d001c092c019]]
4. 请你以第一人称并且用严谨风格来回答问题，一定要用中文来回答，并且基于事实详细阐述。
Human: ### 参考资料 ###
["Source_id": 25e1f671b19f11ed03e68d8477d84cb5,"Content": "--社保卡一人一卡政策问题：我手上有多张社保卡，哪张才可以用呢？政策：您好，目前实行一人一卡政策，如若您同时持有多张社保卡，广东省内的社保卡只能保留一张作为全省通用卡，其余的省内异地社保卡需办理“一人一卡清理”业务；省外的社保卡，金融账户全国通用，但医保账户无法在我市使用。温馨提示：1.为减少对您社会保障卡医保个账正常使用造成的影响，建议优先选择个人基本医疗保险所在地的卡；2.办理“一人一卡清理”业务后，不影响个人社保参保缴费记录，被清理的社保卡金融账户应用不受影响，医保账户暂时仅支持原参保地使用。--电子社保卡亲情服务绑定人数问题：电子社保卡亲情服务可以绑定几个人？政策：好，电子社保卡亲情服务的办事指南如下：1.【办理条件】：需同时满足以下条件：（1）被绑定亲情服务的用户未申领电子社保卡；（2）被绑定亲情服务的用户未被他人绑定；（3）主卡绑定用户不超过8个。2.【办理方法】："]

["Source_id": 25e1f671b19f11ed03e68d8477d84cb5,"Content": "1.【缴费年限】：缴费年限越长，养老金越多；2.【缴费基数】：缴费基数越高，养老金越多；3.【全省上年度在岗职工月平均工资】：全省上年度在岗职工月平均工资越高，养老金越多。--社保卡信息查询问题1：如何查询社保卡号？政策1：1.【直接查看卡面（仅三代社保卡支持）】：社保卡卡号直接印制在卡面，持卡人可以查看社保卡卡面了解卡号信息，如下图所示。1)【现场查询】：您可携带有效身份证前往社保卡合作银行服务网点或社保经办机构前台查询。问题2：如何查询社保卡的有效期？政策2：您好，目前东市的社保卡并没有设定有效期。但按照人民银行规定，卡片的芯片有效期是10年，到期后有可能会影响卡片办理存款等业务，金融账户是否可正常使用请拨打各发卡银行热线电话咨询。问题3：如何查询社保卡的开户行？政策3：您好，持卡人可到发卡银行任一网点免费查询社保卡金融账户收支明细。--社保卡补办政策问题：社保卡如何补办政策：一、办理材料：1.持卡人有效身份证件原件。
您好，持卡人可到发卡银行任一网点免费查询社保卡金融账户收支明细。--社保卡补办政策问题：社保卡如何补办政策：一、办理材料：1.持卡人有效身份证件原件。2.持卡人18周岁以下的，由监护人代办，还需提供以下材料：（1）法定监护人有效身份证件原件；（2）可证明法定监护关系的材料：可直接体现父子、母子关系的居民户口簿，出生医学证明等。注：持卡人18周岁及以上的，因特殊情况无法由本人办理时，可咨询发卡银行并按发卡银行的有关规定办理。二、办理地点：发卡银行（即卡面所示银行）在我市辖区内任一网点。具体以当地经办机构为准。--养老待遇发放问题：企业职工的养老待遇几号发放政策：您好，企业职工养老金的发放时间为每月15日之前。具体以当地经办机构为准--社保退保问题：社保退保条件和社保退保办理指南政策：一、社保退保条件：1.在达到法定退休年龄时，不满足退休的缴费年限条件且不办理转移手续；"]

["Source_id": 25e1f671b19f11ed03e68d8477d84cb5,"Content": "（5）死亡待遇需划拨至法定继承人账户的，还需提供：①所属地公证机关出具的继承公证，《公证书》需要明确继承人具体有几位；②继承人活期银行存折或银行储蓄卡的原件。--办理地点1.向服务中心政务大厅或各镇（街）政务大厅提出申请；2.“社银合作”银行服务网点。--办理费用免费。--社保卡的功能问题：社保卡有哪些功能？政策：您好，社保卡功能应用涵盖了全市的政务服务、交通出行、医疗健康、金融服务、待遇发放、身份认证等民生服务领域的应用。市民持社保卡或开通电子市民码后，可使用以下功能应用：1.【公共交通一卡行】：依托市民卡实现公交、地铁一卡通行，并享受市政优惠政策。2.【诊疗服务一卡通】：全面升级“诊疗一卡通”平台，持续拓展市民卡医疗服务应用，实现市民卡在全市1300多家定点药店和医疗机构就医购药全流程应用，支持扫码、刷卡应用。
2.【诊疗服务一卡通】：全面升级“诊疗一卡通”平台，持续拓展市民卡医疗服务应用，实现市民卡在全市1300多家定点药店和医疗机构就医购药全流程应用，支持扫码、刷卡应用。3.【金融优惠一卡享】：通过发卡银行和银联云闪付、微信、支付宝等开展各类消费支付活动，让持卡人享受消费和理财优惠，更好地实现银行汇兑、城市公共服务、生活消费等安全便捷的金融服务。4.【公共场所一卡认】：依托市民卡实现政务服务大厅、公共资源交易中心、医院、学校、图书馆、景区、公园等公共场所一卡通认。5.【资金补贴一卡查】：推动社保待遇、就业人才补贴、各项惠民惠农补贴资金等通过市民卡“一卡通”发放，并依托市民卡实现资金发放明细查询。6.【信用付费一卡清】：打通公共场所停车场、医院诊疗系统的数据共享，探索信用数字账户免签支付模式。实现先停车离场后付费、先就医后付费的应用场景，未来将推广应用至全市公共场所停车场和公立医院。--养老待遇资格认证问题：养老待遇资格认证政策：一、受理条件居住我市的省内非本市离退休人员。二、所需材料参保人有效身份证明。
--养老待遇资格认证问题：养老待遇资格认证政策：一、受理条件居住我市的省内非本市离退休人员。二、所需材料参保人有效身份证明。三、线下办理具体步骤1.打开菜单：公共服务柜台系统-业务受理-养老待遇（柜台）-企业职工领取养老保险待遇资格认证----media/image1.png----2.受理：进入受理页面，在[信息查询]界面中输入个人编号或身份证号码，点击“查询”按钮进行数据查询，填写[申请人信息]及[受理信息]内容，点击提交。提交成功后打印受理回执并扫描相应材料，随后业务流转至初审阶段。----media/image2.png----四、网上办具体步骤网上办途径推荐使用粤省事、广东省人社厅网上服务平台。--失业保险待遇查询问题：失业保险待遇查询政策：个人待遇明细查询（失业保险）：一、受理条件存在失业待遇支付记录。二、所需材料本人身份证。备注：如涉及非本人查询的情形，请指引办理人到就近社保经办机构前台办理。三、功能介绍"]

["Source_id": 25e1f671b19f11ed03e68d8477d84cb5,"Content": "该政策文件详细说明了不同人群的退休年龄和办理退休的条件，包括职工、灵活就业人员、村社区参保人等。此外，文件还提供了参加养老保险的办理指南，包括网上办理和现场办理的步骤。对于企业职工的养老关系转移，文件也提供了详细的办理流程。最后，文件对比了灵活就业人员和城乡居民的缴费金额和领取金额的政策。"]

["Source_id": 25e1f671b19f11ed03e68d8477d84cb5,"Content": "在一人一卡政策下，需要保留一张社保卡作为全省通用卡，其余的需要办理“一人一卡清理”业务。电子社保卡亲情服务有绑定人数限制，办理方式有线上和线下两种。社保关系转移政策规定，跨省时需要办理转移手续。非本地户籍办理社保卡需要满足一定条件。该政策文件详细说明了不同人群的退休年龄和办理退休的条件，提供了参加养老保险的办理指南和企业职工养老关系转移流程。最后，文件介绍了社保卡的使用和补办政策，企业职工养老金的发放时间，社保退保的条件和指南，以及社保卡的功能等。"]

### 用户问题 ###
该政策文件详细说明了哪些内容？
"""

# qa_prompt = """你是一个角色识别AI助手，需要基于用户输入，生成一个AI助手的角色定义。请考虑以下几个方面：
# 1. 角色的专长领域
# 2. 角色的主要目标
# 3. 角色的个性特征
# 4. 角色的擅长技能
# 5. 角色与用户交互时的语气和行为准则
# 最终生成的提示应该描述这个角色的名称、背景、个性特征和在与用户互动时的表现方式。
# ### 用户输入 ###
# {query}
# ### 回答要求 ###
# 1.回答不应包含任何有害、不道德、种族主义、性别歧视、有毒、危险或非法的内容。
# 2.一定要用中文回答问题。
# """


def send_request(query, expect_answer=None):
    # p = qa_prompt.format(query=query)
    data = {
        "model": model_name,
        "stream": True,
        "messages": [{"role": "system", "content": p}],
        "temperature": 0
    }

    # 记录请求发送的时间（精确到毫秒）
    request_send_time = time.time()
    response = requests.post(model_url, headers=headers, json=data, stream=True)

    first_packet_time = None
    total_character_count = 0
    answer = ""
    # 处理响应数据
    error = 0
    error_answer = ""
    for line in response.iter_lines():
        response_line = line.decode()
        # print("返回数据包:", response_line)
        # 只处理第二行，因为第一行是空行
        if not first_packet_time:
            # 记录第一个数据包返回的时间（精确到毫秒）
            first_packet_time = time.time()

        # 处理响应数据
        if not line.startswith(b"data: "):
            error_answer = response_line
        else:
            # if line.startswith(b"data: "):
            json_data = line[6:]
            try:
                data_dict = json.loads(json_data)
            except json.JSONDecodeError:
                data = line.decode("utf-8")
                if data.__contains__("DONE"):
                    continue
                else:
                    log.error("json loads error:->{}".format(line))
                    continue
            if 'choices' in data_dict:
                choices = data_dict['choices']
                for choice in choices:
                    if choice["finish_reason"] == "stop":
                        break
                    if 'delta' in choice and 'content' in choice['delta']:
                        content = choice['delta']['content']
                        content_length = len(content)
                        answer += content
                        total_character_count += content_length
                    else:
                        error += 1
                        if error > 1:
                            log.error("prompt : {}".format(qa_prompt))
                            log.error("choices not delta or delta not content:->{}".format(line))
            else:
                log.error("response not choices:->{}".format(line))
    return answer
    # 等待整个请求完全返回
    response.close()
    # 记录请求完成的时间（精确到毫秒）
    request_complete_time = time.time()

    # 转换为易读的时分秒格式（精确到毫秒）并保留毫秒的前3位
    request_send_time_str = datetime.fromtimestamp(request_send_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    first_packet_time_str = datetime.fromtimestamp(first_packet_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    request_complete_time_str = datetime.fromtimestamp(request_complete_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]

    # 计算并打印首包耗时和总耗时
    first_packet_duration = (first_packet_time - request_send_time) * 1000
    total_duration = request_complete_time - request_send_time
    tokens_time = (request_complete_time - first_packet_time)
    # 计算性能指标（字/s)
    performance_metric = round(total_character_count / tokens_time, 2),  # 先除以1.54换算成token
    #   计算输出、输入token数

    output_token = get_num_tokens(provider="orionstar", tokenizer_name="yi", text=answer)
    input_token = get_num_tokens(provider="orionstar", tokenizer_name="yi", text=qa_prompt)
    performance_token = round(output_token / tokens_time, 2),

    result = {
        # "请求发送的时间": request_send_time_str,
        # "第一个数据包返回的时间": first_packet_time_str,
        # "请求完成的时间": request_complete_time_str,
        # "首包耗时(ms)": first_packet_duration,
        # "总耗时(s)": total_duration,
        # "输入字数": len(qa_prompt),
        # "输出字数": total_character_count,
        # "输入token": input_token,
        # "输出token": output_token,
        # "性能指标(字/s)": performance_metric,
        # "推理速度(tokens/s)": performance_token,
        "query": query,
        "answer": answer,
        "expect_answer": expect_answer,
        "error_answer": error_answer,
        "prompt": p,
    }
    return result


def save_print_to_excel(file_path, index):
    results = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        futures = []

        question_list = read_xlsx_to_dict(file_path)

        for question in question_list:

            futures.append(executor.submit(send_request, question.get("question"), expect_answer=question.get("answer")))
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as exc:
                print(f"Error: {exc}")
    df = pd.DataFrame(results)
    df.to_excel(f"out_{index}.xlsx", index=False)
    return results


if __name__ == "__main__":
    file_path = ""
    parser = argparse.ArgumentParser(description="处理命令行参数")
    # 并发数量参数化，不填默认为1
    parser.add_argument("--f", type=str, required=False, help="文件")
    # 解析参数
    args = parser.parse_args()
    if args.f is not None:
        file_path = args.f
    else:
        print("请输入文件路径")
        exit()
    for i in range(2, 10):
        save_print_to_excel(file_path=file_path, index=i)
