import hashlib
import requests
QUESTION_COLLECTION_PREFIX = "questions_embeddings_v01_orionstar_text-embedding-bge_"
SECTION_COLLECTION_PREFIX = "section_embeddings_v01_orionstar_text-embedding-bge_"
SUMMARY_COLLECTION_PREFIX = "summary_embeddings_v01_orionstar_text-embedding-bge_"


def get_collection_name(dataset_id):
    index = int.from_bytes(hashlib.md5(dataset_id.encode("utf-8")).digest(), byteorder="big") % 100
    print(index)
    print(f"question:{QUESTION_COLLECTION_PREFIX}{index:0{2}d}")
    print(f"section:{SECTION_COLLECTION_PREFIX}{index:0{2}d}")
    print(f"summary:{SUMMARY_COLLECTION_PREFIX}{index:0{2}d}")
    # return f"{QUESTION_COLLECTION_PREFIX}{index:0{2}d}"
    return index


def query(collection_prefix):
    #   查询QA问答对
    # index = get_collection_name(dataset_id)
    # if query_type == "question":
    #     collection_prefix = f"{QUESTION_COLLECTION_PREFIX}{index:0{2}d}"
    # elif query_type == "section":
    #     collection_prefix = f"{SECTION_COLLECTION_PREFIX}{index:0{2}d}"
    # elif query_type == "summary":
    #     collection_prefix = f"{SUMMARY_COLLECTION_PREFIX}{index:0{2}d}"
    # else:
    #     raise ValueError("query type must be question or section")
    url = f"http://10.118.13.232:6333/collections/{collection_prefix}/points/scroll"
    # payload = {"limit": 4000, "filter": {"must": [{"key": "metadata.source_type", "match": {"any": ["intervention", "section"]}}]}}
    # payload = {"limit": 4000, "filter": {"must": [{"key": "metadata.doc_id", "match": {"any": ["b67e56746c31f024265b34cdfb89da47"]}}]}}
    true_payload = {
        "limit": 20000,
        "params": {
            "hnsw_ef": 128,
            "exact": False,
            "indexed_only": True
        },

        "filter": {
            "must": [
                {
                    "key": "metadata.source_type",
                    "match": {
                        "any": [
                            "intervention"
                        ]
                    }
                }
            ]
        }
    }
    f_payload = {
        "limit": 20000,
        "params": {
            "hnsw_ef": 128,
            "exact": False,
            "indexed_only": False
        },

        "filter": {
            "must": [
                {
                    "key": "metadata.source_type",
                    "match": {
                        "any": [
                            "intervention"
                        ]
                    }
                }
            ]
        }
    }
    t_response = requests.post(url, json=true_payload).json()
    f_response = requests.post(url, json=f_payload).json()
    t = set(i.get("id") for i in t_response.get("result", {}).get("points", {}))
    f = set(i.get("id") for i in f_response.get("result", {}).get("points", {}))
    gap = f - t
    if len(gap) > 0:
        return False, gap, collection_prefix
    else:
        return True, None, collection_prefix


#   数据格式参考，根据数据格式，修改payload里的filter条件
questions = {"id": "0425376e-6f78-44c4-9891-a091524332e3",
             "payload": {
                 "metadata": {
                     "answer_id": "2fe4526a6d42236cd9e07fb2a99963a1",
                     "data_id": "a410fb9768a0c28257e0b4f549d4a675",
                     "doc_id": "",
                     "intervention_type": "batchqa",
                     "is_using": 1,
                     "question_id": "38ad8df22d58d6804b92dc218dbe8f73",
                     "source_ids": "",
                     "source_type": "intervention"
                 },
                 "page_content": "广东省东莞市塘厦宏业支行"
             },
             "vector": None
             }
section = {
    "id": "1cdd8339-a2db-4eb8-9a70-1c6b7b5fd4c7",
    "payload": {
        "metadata": {
            "data_id": "1fbaf73f8e36d353fad6de7c8d410634",
            "data_number": 12,
            "digest": "b6a0545958cfa33f575488498d6ff44f",
            "doc_id": "f195d93392e2071f99bfe1cb58546a18",
            "doc_name": "医疗政策",
            "doc_url": "https://test-api-chatmax.orionstar.com/orics/down/ct003_20240723_99ebe938062ad401a2724625d85b09a9.xlsx",
            "file_path": "/autolm/download/1fbaf73f8e36d353fad6de7c8d410634/f195d93392e2071f99bfe1cb58546a18/ct003_20240723_99ebe938062ad401a2724625d85b09a9.xlsx",
            "index": 11,
            "is_using": 1,
            "num_sect": 13,
            "page_id": 1,
            "sheet_name": "医疗政策",
            "source": "/autolm/download/1fbaf73f8e36d353fad6de7c8d410634/f195d93392e2071f99bfe1cb58546a18/ct003_20240723_99ebe938062ad401a2724625d85b09a9.xlsx",
            "source_type": "section",
            "status": 4,
            "total_count": 13
        },
        "page_content": "测试数据"
    },
}
summary = {
    "id": "07566af8-38cf-443b-b635-fb17e02cfa53",
    "payload": {
        "metadata": {
            "child_ids": "64_65_66_67_68_69_70_71",
            "data_id": "a54f235e991612114e7550d35cff32f4",
            "doc_id": "bc019c95f7a988128056e4a84e07e2f0",
            "index": 9,
            "is_using": 1,
            "lan": "en",
            "level": 1,
            "source_type": "summary"
        },
        "page_content": "There are many risks in outsourcing, including a decline in business knowledge, a decrease in product selection freedom, a decline in specification change flexibility, excellent talent leaving, a decline in employee work enthusiasm and loyalty, a decrease in investment in talent training, difficulty in changing scope and conditions during the contract period, maintenance and operational costs becoming a black box, a rigid pricing system, a decrease in price sensitivity, a decline in system maintenance and operational quality, talent loss and ability decline, a decrease in cost transparency, a decline in information security awareness, the generation of additional cost burdens, and experience loss. The response strategies include clarifying the conditions for contract termination, defining service level agreements, contributing to the growth of the business through partnerships, the strengths of partners, and the strengths of the company itself."
    },
    "vector": None
}

if __name__ == '__main__':
    #   项目数据集ID
    dataset_id = 'f69905ad4049c1cd232201fa7485c919'
    #   项目数据集ID
    #   fedbb169c6d66b8a854e01e5a817b083（德生测试）
    # 77ceea21be245a5876f51df5b2199ef9 （梁伟测试）
    #   查询类型，question、section、summary
    query_type = 'question'
    # query_type = 'section'
    for i in range(1, 100):
        collection_prefix = f"questions_embeddings_v01_orionstar_text-embedding-bge_{i}"
        a, b, c = query(collection_prefix)
        print(b, c)
        if not a:
            break
