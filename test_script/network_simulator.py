#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络环境模拟器
用于在Linux/macOS系统上模拟不同的网络条件
"""

import os
import sys
import time
import subprocess
import logging
from typing import Dict, Any
from dataclasses import dataclass

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class NetworkConfig:
    """网络配置"""
    name: str
    delay_ms: int = 0          # 延迟（毫秒）
    jitter_ms: int = 0         # 抖动（毫秒）
    loss_percent: float = 0.0  # 丢包率（百分比）
    rate_limit: str = ""       # 带宽限制（如 "1mbit"）
    corrupt_percent: float = 0.0  # 包损坏率
    duplicate_percent: float = 0.0  # 包重复率


class NetworkSimulator:
    """网络模拟器"""

    def __init__(self, interface: str = "eth0"):
        self.interface = interface
        self.is_active = False
        self.current_config = None

        # 检测操作系统
        self.is_linux = sys.platform.startswith('linux')
        self.is_macos = sys.platform == 'darwin'

        if not (self.is_linux or self.is_macos):
            raise RuntimeError("仅支持Linux和macOS系统")

    def _run_command(self, command: str, check: bool = True) -> subprocess.CompletedProcess:
        """执行系统命令"""
        logger.info(f"执行命令: {command}")
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                check=check
            )
            if result.stdout:
                logger.debug(f"输出: {result.stdout}")
            if result.stderr:
                logger.warning(f"错误: {result.stderr}")
            return result
        except subprocess.CalledProcessError as e:
            logger.error(f"命令执行失败: {e}")
            raise

    def _check_permissions(self):
        """检查权限"""
        if os.geteuid() != 0:
            logger.warning("建议使用sudo运行以获得网络管理权限")

    def _get_default_interface(self) -> str:
        """获取默认网络接口"""
        if self.is_macos:
            # macOS上查找默认路由接口
            try:
                result = self._run_command("route get default")
                for line in result.stdout.split('\n'):
                    if 'interface:' in line:
                        interface = line.split(':')[1].strip()
                        return interface
            except Exception:
                pass
            return "en0"  # macOS默认Wi-Fi接口
        else:
            # Linux上查找默认路由接口
            try:
                result = self._run_command("ip route | grep default")
                parts = result.stdout.split()
                for i, part in enumerate(parts):
                    if part == "dev" and i + 1 < len(parts):
                        return parts[i + 1]
            except Exception:
                pass
            return "eth0"  # Linux默认接口

    def apply_config(self, config: NetworkConfig):
        """应用网络配置"""
        self._check_permissions()

        # 先清除现有配置
        self.clear_config()

        logger.info(f"应用网络配置: {config.name}")

        if self.is_linux:
            self._apply_linux_config(config)
        elif self.is_macos:
            self._apply_macos_config(config)

        self.is_active = True
        self.current_config = config
        logger.info(f"网络配置已应用: {config.name}")

    def _apply_linux_config(self, config: NetworkConfig):
        """在Linux上应用网络配置（使用tc命令）"""
        commands = []

        # 创建根qdisc
        if any([config.delay_ms, config.jitter_ms, config.loss_percent,
                config.corrupt_percent, config.duplicate_percent]):
            netem_params = []

            if config.delay_ms > 0:
                delay_cmd = f"delay {config.delay_ms}ms"
                if config.jitter_ms > 0:
                    delay_cmd += f" {config.jitter_ms}ms"
                netem_params.append(delay_cmd)

            if config.loss_percent > 0:
                netem_params.append(f"loss {config.loss_percent}%")

            if config.corrupt_percent > 0:
                netem_params.append(f"corrupt {config.corrupt_percent}%")

            if config.duplicate_percent > 0:
                netem_params.append(f"duplicate {config.duplicate_percent}%")

            netem_cmd = f"sudo tc qdisc add dev {self.interface} root handle 1: netem {' '.join(netem_params)}"
            commands.append(netem_cmd)

        # 添加带宽限制
        if config.rate_limit:
            if commands:
                # 如果已有netem，添加tbf作为子qdisc
                tbf_cmd = f"sudo tc qdisc add dev {self.interface} parent 1: handle 2: tbf rate {config.rate_limit} burst 32kbit latency 400ms"
            else:
                # 直接添加tbf作为根qdisc
                tbf_cmd = f"sudo tc qdisc add dev {self.interface} root tbf rate {config.rate_limit} burst 32kbit latency 400ms"
            commands.append(tbf_cmd)

        # 执行命令
        for cmd in commands:
            self._run_command(cmd)

    def _apply_macos_config(self, config: NetworkConfig):
        """在macOS上应用网络配置（使用pfctl或dummynet）"""
        # macOS的网络模拟较为复杂，这里提供基本实现
        # 实际使用中可能需要安装额外工具如Network Link Conditioner

        if config.rate_limit:
            # 使用pfctl限制带宽（需要额外配置）
            logger.warning("macOS带宽限制需要额外配置pfctl规则")

        if any([config.delay_ms, config.loss_percent]):
            # 可以考虑使用Network Link Conditioner
            logger.warning("macOS网络延迟和丢包模拟建议使用Xcode的Network Link Conditioner工具")

    def clear_config(self):
        """清除网络配置"""
        if not self.is_active:
            return

        logger.info("清除网络配置")

        if self.is_linux:
            # 删除所有tc规则
            self._run_command(f"sudo tc qdisc del dev {self.interface} root", check=False)
        elif self.is_macos:
            # macOS的清理操作
            logger.info("macOS网络配置清理")

        self.is_active = False
        self.current_config = None
        logger.info("网络配置已清除")

    def show_current_config(self):
        """显示当前网络配置"""
        if self.is_linux:
            result = self._run_command(f"tc qdisc show dev {self.interface}", check=False)
            logger.info(f"当前tc配置:\n{result.stdout}")
        elif self.is_macos:
            logger.info("macOS当前网络配置需要手动检查")

    def test_connectivity(self, host: str = "*******", count: int = 5) -> Dict[str, Any]:
        """测试网络连通性"""
        logger.info(f"测试连通性: ping {host}")

        ping_cmd = f"ping -c {count} {host}"
        try:
            result = self._run_command(ping_cmd)

            # 解析ping结果
            lines = result.stdout.split('\n')
            stats = {}

            for line in lines:
                if 'packet loss' in line:
                    # 提取丢包率
                    parts = line.split(',')
                    for part in parts:
                        if 'packet loss' in part:
                            loss = part.strip().split('%')[0].split()[-1]
                            stats['packet_loss'] = float(loss)

                elif 'min/avg/max' in line or 'round-trip' in line:
                    # 提取延迟统计
                    parts = line.split('=')
                    if len(parts) > 1:
                        times = parts[-1].strip().split('/')
                        if len(times) >= 3:
                            stats['rtt_min'] = float(times[0])
                            stats['rtt_avg'] = float(times[1])
                            stats['rtt_max'] = float(times[2])

            return stats

        except subprocess.CalledProcessError:
            logger.error(f"ping {host} 失败")
            return {'error': 'ping failed'}


# 预定义的网络配置
NETWORK_CONFIGS = {
    'normal': NetworkConfig(
        name="正常网络",
        delay_ms=0,
        loss_percent=0.0,
        rate_limit=""
    ),
    'high_latency': NetworkConfig(
        name="高延迟网络",
        delay_ms=500,
        jitter_ms=50,
        loss_percent=0.0
    ),
    'unstable': NetworkConfig(
        name="不稳定网络",
        delay_ms=100,
        jitter_ms=50,
        loss_percent=5.0,
        rate_limit="10mbit"
    ),
    'slow': NetworkConfig(
        name="慢速网络",
        delay_ms=200,
        loss_percent=2.0,
        rate_limit="1mbit"
    ),
    'mobile_3g': NetworkConfig(
        name="3G移动网络",
        delay_ms=300,
        jitter_ms=100,
        loss_percent=1.0,
        rate_limit="384kbit"
    ),
    'mobile_edge': NetworkConfig(
        name="EDGE网络",
        delay_ms=500,
        jitter_ms=150,
        loss_percent=3.0,
        rate_limit="256kbit"
    ),
    'satellite': NetworkConfig(
        name="卫星网络",
        delay_ms=600,
        jitter_ms=200,
        loss_percent=1.0,
        rate_limit="2mbit"
    )
}


def main():
    """主函数 - 演示网络模拟器的使用"""
    import argparse

    parser = argparse.ArgumentParser(description='网络环境模拟器')
    parser.add_argument('--config', choices=list(NETWORK_CONFIGS.keys()),
                        help='选择预定义的网络配置')
    parser.add_argument('--interface', default='',
                        help='指定网络接口（默认自动检测）')
    parser.add_argument('--duration', type=int, default=60,
                        help='模拟持续时间（秒）')
    parser.add_argument('--test', action='store_true',
                        help='运行连通性测试')
    parser.add_argument('--clear', action='store_true',
                        help='清除当前网络配置')
    parser.add_argument('--show', action='store_true',
                        help='显示当前网络配置')

    args = parser.parse_args()

    # 创建模拟器
    interface = args.interface if args.interface else None
    simulator = NetworkSimulator(interface) if interface else NetworkSimulator()

    try:
        if args.clear:
            simulator.clear_config()
            return

        if args.show:
            simulator.show_current_config()
            return

        if args.config:
            config = NETWORK_CONFIGS[args.config]
            simulator.apply_config(config)

            if args.test:
                # 运行连通性测试
                logger.info("运行连通性测试...")
                stats = simulator.test_connectivity()
                logger.info(f"连通性测试结果: {stats}")

            if args.duration > 0:
                logger.info(f"网络模拟将持续 {args.duration} 秒...")
                logger.info("按 Ctrl+C 提前停止")

                try:
                    time.sleep(args.duration)
                except KeyboardInterrupt:
                    logger.info("用户中断模拟")

            # 清理
            simulator.clear_config()

        else:
            # 列出可用配置
            logger.info("可用的网络配置:")
            for name, config in NETWORK_CONFIGS.items():
                logger.info(f"  {name}: {config.name}")
                logger.info(f"    延迟: {config.delay_ms}ms, 丢包: {config.loss_percent}%, 带宽: {config.rate_limit or '无限制'}")

    except KeyboardInterrupt:
        logger.info("程序被中断")
        simulator.clear_config()
    except Exception as e:
        logger.error(f"程序异常: {e}")
        simulator.clear_config()
        sys.exit(1)


if __name__ == "__main__":
    main()
