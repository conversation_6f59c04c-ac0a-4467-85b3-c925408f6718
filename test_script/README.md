# 机器人网络环境测试工具使用说明

## 概述

本测试工具套件用于测试机器人在不同网络环境下的响应性能和稳定性，包含以下组件：

- `network_test_suite.py` - 核心测试套件
- `network_simulator.py` - 网络环境模拟器
- `quick_network_test.py` - 快速测试示例
- `network_test_config.yaml` - 配置文件
- `test_network_environments.md` - 完整测试方案文档

## 快速开始

### 1. 环境准备

确保已安装所需依赖：

```bash
pip install requests dataclasses typing
```

对于网络模拟功能（Linux/macOS）：
```bash
# Linux
sudo apt-get install iproute2

# macOS 
# 建议安装Xcode的Network Link Conditioner
```

### 2. 配置测试参数

编辑 `network_test_config.yaml` 或直接在代码中修改：

```python
base_url = "https://your-api-endpoint.com"
auth_token = "your-auth-token"
```

### 3. 运行快速测试

```bash
# 快速功能测试
python quick_network_test.py --mode quick

# 连通性测试
python quick_network_test.py --mode connectivity
```

## 详细使用

### 核心测试套件使用

```python
from network_test_suite import NetworkTestSuite, NetworkCondition

# 创建测试套件
test_suite = NetworkTestSuite("https://api.example.com", "your-token")

# 定义网络条件
condition = NetworkCondition("test", latency_ms=500, packet_loss_percent=2.0)

# 定义测试用例
test_case = {
    "name": "api_test",
    "method": "POST",
    "endpoint": "/api/chat",
    "data": {"message": "hello"}
}

# 执行单次测试
result = test_suite.single_request_test(test_case, condition)

# 执行并发测试
results = test_suite.concurrent_test(test_case, condition, concurrent_users=10)

# 运行完整测试
all_results = test_suite.run_comprehensive_test()
```

### 网络模拟器使用

```bash
# 列出可用的网络配置
python network_simulator.py

# 应用高延迟网络配置
sudo python network_simulator.py --config high_latency --duration 300

# 应用不稳定网络配置并运行测试
sudo python network_simulator.py --config unstable --test --duration 60

# 清除网络配置
sudo python network_simulator.py --clear

# 显示当前配置
python network_simulator.py --show
```

### 自定义网络条件

```python
from network_simulator import NetworkConfig, NetworkSimulator

# 自定义网络配置
custom_config = NetworkConfig(
    name="custom_slow",
    delay_ms=1000,        # 1秒延迟
    jitter_ms=200,        # 200ms抖动
    loss_percent=10.0,    # 10%丢包
    rate_limit="512kbit"  # 512k带宽限制
)

# 应用配置
simulator = NetworkSimulator()
simulator.apply_config(custom_config)

# 运行测试...

# 清理
simulator.clear_config()
```

## 测试场景

### 1. 基础功能测试

验证机器人在不同网络条件下的基本功能：

```bash
python network_test_suite.py
```

### 2. 性能压力测试

测试高并发情况下的性能：

```python
# 并发用户测试
results = test_suite.concurrent_test(
    test_case, 
    network_condition, 
    concurrent_users=50, 
    duration_seconds=300
)
```

### 3. 稳定性测试

长时间运行测试：

```python
# 稳定性测试（30分钟）
results = test_suite.stability_test(
    test_case, 
    network_condition, 
    duration_minutes=30
)
```

### 4. 网络异常模拟

模拟各种网络异常情况：

```bash
# 高延迟网络
sudo python network_simulator.py --config high_latency

# 不稳定网络（丢包+抖动）
sudo python network_simulator.py --config unstable

# 低带宽网络
sudo python network_simulator.py --config slow
```

## 结果分析

### 测试报告

测试完成后会生成：

- `network_test_results_[timestamp].json` - 详细测试数据
- `network_test_results_[timestamp]_report.md` - 可读性报告

### 关键指标

- **响应时间**: 平均、最小、最大、P95、P99
- **成功率**: 请求成功的比例
- **吞吐量**: 每秒处理的请求数（QPS）
- **错误分布**: 各类错误的统计

### 报告示例

```
## 网络条件: high_latency

### 测试用例: simple_query
- 总请求数: 25
- 成功请求数: 24
- 成功率: 96.00%
- 平均响应时间: 1250.45ms
- P95响应时间: 1500.23ms
- P99响应时间: 1650.78ms
- 吞吐量: 8.32 QPS
```

## 最佳实践

### 1. 测试准备

- 确保测试环境与生产环境网络拓扑相似
- 准备充分的测试数据
- 设置合理的超时时间
- 监控系统资源使用情况

### 2. 测试执行

- 从简单测试开始，逐步增加复杂度
- 每种网络条件至少运行3次取平均值
- 记录测试环境的详细信息
- 监控服务端的性能指标

### 3. 结果分析

- 对比不同网络条件下的性能差异
- 识别性能瓶颈和异常点
- 分析错误模式和出现频率
- 制定优化改进方案

### 4. 持续改进

- 将测试集成到CI/CD流水线
- 建立性能基线和告警机制
- 定期回归测试验证改进效果
- 根据用户反馈调整测试策略

## 故障排除

### 常见问题

1. **权限不足**
   ```bash
   # 网络模拟需要root权限
   sudo python network_simulator.py --config high_latency
   ```

2. **连接超时**
   ```python
   # 增加超时时间
   test_suite.session.timeout = 30
   ```

3. **内存不足**
   ```bash
   # 监控内存使用
   top -p $(pgrep python)
   
   # 减少并发数
   concurrent_users=5
   ```

4. **网络配置未生效**
   ```bash
   # 检查当前配置
   tc qdisc show
   
   # 手动清理
   sudo tc qdisc del dev eth0 root
   ```

### 调试技巧

- 启用详细日志：设置 `logging.DEBUG`
- 使用 Wireshark 抓包分析网络流量
- 监控系统资源：CPU、内存、网络I/O
- 检查防火墙和代理设置

## 扩展功能

### 自定义测试用例

```python
custom_test_case = {
    "name": "custom_test",
    "method": "POST",
    "endpoint": "/custom/api",
    "data": {
        "custom_param": "value"
    },
    "headers": {
        "Custom-Header": "value"
    }
}
```

### 集成监控系统

```python
# 集成Prometheus监控
from prometheus_client import Counter, Histogram

request_counter = Counter('api_requests_total', 'Total API requests')
response_time_histogram = Histogram('api_response_time_seconds', 'API response time')
```

### 自动化测试

```bash
#!/bin/bash
# 自动化测试脚本

# 应用网络配置
sudo python network_simulator.py --config unstable --duration 600 &

# 运行测试
python network_test_suite.py

# 清理
sudo python network_simulator.py --clear
```

## 贡献指南

欢迎提交Issue和Pull Request来改进测试工具：

1. Fork项目
2. 创建特性分支
3. 提交代码
4. 创建Pull Request

## 许可证

MIT License 