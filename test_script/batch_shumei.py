# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : batch_shumei.py
<AUTHOR> <EMAIL>
@Time   : 2023-11-15 16:03:03
@Version: v1.0
"""
import json
import os
import re
import time
import jsonlines
import requests
import uuid

from tool.decorators import method_logs
from tool.util import *
from tool.logger import log
from concurrent.futures import ThreadPoolExecutor
from concurrent.futures import as_completed
from test_conifg.config import ip, port
from test_conifg.path_config import DOWNLOAD_PATH

ex = ThreadPoolExecutor(max_workers=10)

URL = "http://api-text-bj.fengkongcloud.com/text/v4"
header = {"content-Type": "application/json"}
accessKey = "ZnTPUJUSZ8DGCUKSsMm8"
app_id = "default"
#   question传 user，answer传aitext
event_id = "aitext"


# @method_logs
def request(text: str) -> dict:
    token_id = str(uuid.uuid4()).replace("-", "")
    payload = {
        "accessKey": accessKey,
        "appId": app_id,
        "eventId": event_id,
        "type": "TEXTRISK",
        "data":
            {
                "text": text,
                "tokenId": token_id
            }
    }
    res = requests.request(url=URL, method="post", json=payload, headers=header)

    if res.status_code == 200:
        res_json = res.json()

        return res_json
    else:

        log.error("request error:{}".format(res.text))
        return {"data": res}


def get_answer(args):
    query_text = args.get("line").strip()
    #   TODO 生成数据临时改动，后续回复
    data = json.loads(args.get("line"))
    query_text = data.get("txt").strip()
    text_id = str(data.get("id")).strip()
    wd = data.get("wd").strip()
    # label = data.get("label")[0].strip()
    f = args.get("fs")

    data_list = split_for_len(query_text, 5000)
    for index, query_text in enumerate(data_list):
        print(index)
        time.sleep(1)
        response_data = request(query_text)
        # f.write(query_text + "-->" + json.dumps(response_data, ensure_ascii=False) + "\n")
        f.write(text_id + "-->" + json.dumps(response_data, ensure_ascii=False) + "\n")
        code = response_data.get("code", 999)
        if code != 1100:
            log.error("数据返回非1100：-->{}".format(code))
        msg = response_data.get("message", "")
        request_id = response_data.get("requestId", "")
        risk_level = response_data.get("riskLevel", "")
        risk_detail = response_data.get("riskDetail", {})
        risk_description = response_data.get("riskDescription", "")
        if risk_detail.get("matchedLists"):
            wd_list = []
            for word in risk_detail.get("matchedLists", {}):
                wd_list = [wd.get("word") for wd in word.get("words")]

            wd_str = ",".join(wd_list)
        else:
            wd_str = ""
        if risk_detail.get("riskSegments"):
            risk_segments = str([risk_segments.get("segment") for risk_segments in risk_detail.get("riskSegments", {})])
        else:
            risk_segments = ""
        # args.get("sheet").append(
        #     [query_text, word_list, risk_segments, code, msg, request_id, risk_level, risk_description])
        #   TODO 生成数据临时改动，后续回复
        args.get("sheet").append(
            [text_id, wd, wd_str, risk_segments, code, msg, request_id, risk_level, risk_description,
             query_text])
        # args.get("work").save(args.get("file"))


def upload_sensitive_shumei(input_file, f_type: str, output_f: str):
    """
    调用数美检验敏感词
    根据question列表获取模型给的回答，并将回答记录在Excel表格，包含question、返回的code、Illegal Status、Answer Text
    :return:
    """
    workbook = openpyxl.Workbook()
    sheet = workbook.active
    # sheet.append(
    #     ["Query Text", "illegal_word", "riskSegments", "code", "message", "requestId", "riskLevel", "riskDescription"])
    #   TODO 生成数据临时改动，后续回复
    sheet.append(
        ["Id", "wd", "illegal_word", "riskSegments", "code", "message", "requestId", "riskLevel",
         "riskDescription", "Query Text", ])
    output_path = "{output}{sep}".format(output=DOWNLOAD_PATH, sep=os.sep)
    fs_path = DOWNLOAD_PATH + os.sep + "inspur_sensitive.txt"
    if os.path.exists(fs_path):
        os.remove(fs_path)
    f = open(fs_path, mode="w+")

    if not os.path.exists(output_path):
        os.makedirs(output_path)
    future_list = []
    if f_type == "json":
        with input_file as file:
            index = 0
            for line in file:
                # if index>=1:break
                index += 1
                agrs = {
                    "line": line,
                    "index": index,
                    "work": workbook,
                    "sheet": sheet,
                    "file": output_path + output_f,
                    "fs": f
                }
                future = ex.submit(get_answer, agrs)
                future_list.append(future)
            log.info("任务全部发出，执行中")
    else:
        sheet_name_list = list(input_file)  # 获取所有sheet_name
        sheet_index = 1  # 标记当前sheet索引

        for sheet_name in sheet_name_list:
            index = 0
            for row in input_file[sheet_name].iterrows():
                # if index >= 200: break
                index += 1
                agrs = {
                    #   默认第一列就是question，且只有一列
                    "line": row[1][0],
                    "index": index,
                    "work": workbook,
                    "sheet": sheet,
                    "file": output_path + output_f,
                    "fs": f
                }
                future = ex.submit(get_answer, agrs)
                future_list.append(future)
                if index % 100 == 0:
                    time.sleep(3)
            log.info("任务全部发出，执行中")
    for future in as_completed(future_list): pass
    f.close()
    workbook.save(output_path + output_f)
    log.info("save result file to: {}".format(output_path+output_f))
    path = "/api/download/{}".format(output_f)
    title = "清洗数据任务完成"
    down_url = "http://{ip}:{port}{path}".format(ip=ip, port=port, path=path)
    send_card_message(title=title, down_url=down_url)


if __name__ == '__main__':
    # request("私密我，加我微信")
    # data = "以下是中国关于注册会计师考试的单项选择题，请选出其中的正确答案。\n甲公司是国内一家上市公司。甲公司对其各子公司实行全面预算管理，并通常使用增量预算方式进行战略控制，子公司预算需要经甲公司预算管理委员会批准后执行。2015年10月，甲公司投资了一个新的项目乙(子公司)。2015年11月，甲公司启动2016年度预算编制工作，此时甲公司应要求乙公司编制____。\nA. 增量预算\nB. 零基预算\nC. 固定预算\nD. 弹性预算\n答案: \nB\n\n\n以下是中国关于注册会计师考试的单项选择题，请选出其中的正确答案。\n债务人转让全部合同义务的，下列说法不正确的是____。\nA. 须债权人同意方可进行\nB. 新债务人可主张原债务人对债权人的抗辩\nC. 债务人转移债务的，原债务人对债权人享有债权的，新债务人可以向债权人主张抵销\nD. 非专属于原债务人自身的从债务，一并转让给新债务人\n答案: \nC\n\n\n以下是中国关于注册会计师考试的单项选择题，请选出其中的正确答案。\n某公司2012年以150万元的价格进口了1台仪器；2014年6月因出现故障运往日本修理(出境时已向海关报明)，2014年10月，按海关规定的期限复运进境。此时，该仪器的国际市场价格为200万元。若经海关审定的修理费和料件费为40万元，运费为1.5万元，保险费用为2.8万元，进口关税税率为6%。该仪器复运进境时，应缴纳的进口关税为____万元。\nA. 9\nB. 3\nC. 2.4\nD. 12\n答案: \nC\n\n\n以下是中国关于注册会计师考试的单项选择题，请选出其中的正确答案。\n公开发行公司债券，证监会同意注册的决定自作出之日起一定期限内有效，发行人应当该期限内发行公司债券。该期限是____。\nA. 6个月\nB. 1年\nC. 2年\nD. 3年\n答案: \nC\n\n\n以下是中国关于注册会计师考试的单项选择题，请选出其中的正确答案。\n某集团公司在一家银行开设了许多不同的银行账户，那么，该集团公司可以要求银行在考虑其利息及透支限额时将其子公司的账户余额集中起来。下列不属于现金余额集中的好处是____。\nA. 盈余与赤字相抵\nB. 加强控制\nC. 增加投资机会\nD. 匹配\n答案: \nD\n\n\n\n\n以下是中国关于注册会计师考试的单项选择题，请选出其中的正确答案。\n下列关于税法基本原则的表述中，不正确的是____。\nA. 税收法定原则包括税收要件法定原则和税务合法性原则\nB. 税收公平原则源于法律上的平等性原则\nC. 税收效率原则包含经济效率和行政效率两个方面\nD. 税务机关按法定程序依法征税，可以自由做出减征、停征或免征税款的决定\n答案: \nD"
    #
    # patten = r"(?s)(?<=\n)\n(.+?)\n答案: \n(.+)"
    # res = re.search(patten,data,re.DOTALL)
    # if res:
    #     # print(res.group(0))
    #     print(res.group(1))
    #     print(res.group(2))
    #     print(res.group(3))
    #     print(res.group(4))

    file_path = "/Users/<USER>/workspace/liangwei/网信办备案/ceval_ppl/".replace("/", os.sep)
    t = time.time()
    all_file = get_dir_file(file_path=file_path)
    for file in all_file:
        out = "{filename}_{T}.xlsx".format(filename=file, T=get_time())
        fs = open(file_path+file)
        for line in fs:
            d = json.loads(line)
            questions = d.get("prompt")
            last_index = questions.rindex("请选出其中的正确答案。")
            last_question = questions[last_index+11:-7]
            print(last_question)
            answer = questions[-3:]
            print(answer)





    print(time.time() - t)
