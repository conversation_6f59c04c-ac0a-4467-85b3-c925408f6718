import os
from pydub import AudioSegment
import requests


def convert_mp3_to_aac(input_file, output_file):
    # 检查输入文件是否存在
    if not os.path.isfile(input_file):
        print(f"输入文件不存在: {input_file}")
        return

    # 加载 MP3 文件
    audio = AudioSegment.from_mp3(input_file)

    # 导出为 AAC 文件
    audio.export(output_file, format="ipod")
    print(f"转换完成：{input_file} -> {output_file}")



def get_data():
    # 示例数据
    data = [5, 2, 9, 1, 7, 6, 3]
    print("原始数据:", data)
    sorted_data = quick_sort(data)
    print("排序后数据:", sorted_data)

url = 'https://test-openapi-chatmax.orionstar.com/v1/ctai/ctai_doc_list'
headers = {
    'Orionstar-Api-Key': 'bz6deb13fc27f2981588a95ed4440553e9z77002c02fc1150e6720200d414e76743',
    'Content-Type': 'application/json',
    'Accept': 'application/json'
}
data = {
    'page': '1',
    'page_rows': '10'
}

response = requests.post(url, headers=headers, json=data)

print(response.status_code)
print(response.json())


if __name__ == "__main__":
    get_data()
    # 输入和输出文件路径
    input_mp3 = "/Users/<USER>/Downloads/古窑博览区-普通话.mp3"
    output_aac = "/Users/<USER>/Downloads/古窑博览区-普通话.m4a"

    # 调用转换函数
    convert_mp3_to_aac(input_mp3, output_aac)
