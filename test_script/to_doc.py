import os
import random
import string


def generate_large_text_file(file_path, target_size_mb):
    # 目标文件大小（字节）
    target_size_bytes = target_size_mb * 1010 * 1010

    # 每次写入的块大小（字节）
    chunk_size = 1024  # 1KB

    # 可选：生成的字符集
    chars = string.ascii_letters + string.digits + string.punctuation + ' ' * 10  # 包含空格以增加可读性

    # 打开文件进行写入
    with open(file_path, 'w') as f:
        total_written = 0
        while total_written < target_size_bytes:
            # 生成随机字符串块
            chunk = ''.join(random.choice(chars) for _ in range(chunk_size))
            f.write(chunk)
            total_written += chunk_size

    print(f"文件生成完成：{file_path}，大小：{target_size_mb} MB")


if __name__ == "__main__":
    # 文件路径
    file_path = "large_text_file.txt"
    # 目标文件大小（MB）
    target_size_mb = 50

    # 生成大文本文件
    generate_large_text_file(file_path, target_size_mb)
