import xmindparser
from openpyxl import Workbook


def parse_xmind_to_excel(xmind_file, excel_file):
    # 解析XMind文件
    xmind_data = xmindparser.xmind_to_dict(xmind_file)
    
    # 创建一个新的Excel工作簿
    wb = Workbook()
    ws = wb.active
    ws.title = "XMind Data"

    # 递归解析XMind节点并写入Excel
    def write_node_to_excel(node, depth=0, row=1):
        nonlocal ws
        # 写入当前节点的标题到Excel
        ws.cell(row=row, column=depth + 1, value=node['title'])
        current_row = row

        # 递归处理子节点
        if 'topics' in node:
            for topic in node['topics']:
                current_row = write_node_to_excel(topic, depth + 1, current_row + 1)
        
        return current_row

    # 处理XMind的根节点
    for sheet in xmind_data:
        for topic in sheet['topic']['topics']:
            write_node_to_excel(topic)

    # 保存Excel文件
    wb.save(excel_file)


# 使用示例
xmind_file = '/Users/<USER>/Desktop/test.xmind'  # 替换为实际的Xmind文件路径
excel_file = 'output.xlsx'
parse_xmind_to_excel(xmind_file, excel_file)
print(f"Data has been successfully written to {excel_file}")
