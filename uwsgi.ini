[uwsgi]
# 项目根目录
chdir = .

# Python 虚拟环境路径（如果有的话）
# virtualenv = /path/to/your/virtualenv

# Flask 应用实例
module = manage:app

# 进程相关
master = true
processes = 4
threads = 2

# 通信方式
socket = 127.0.0.1:5000
http-socket = :5000

# 权限设置
uid = orionstar
gid = staff

# 日志设置
logto = ./logs/uwsgi.log
log-maxsize = 50000000
log-backupname = ./logs/uwsgi.log.old

# 自动重启
py-autoreload = 1

# 其他设置
vacuum = true
die-on-term = true 