# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : settings.py
<AUTHOR> <EMAIL>
@Time   : 2023-10-29 20:01:06
@Version: v1.0
"""

from test_conifg.path_config import ENV_CONFIG_PATH
from tool.common import load_config
from dbutils.pooled_db import PooledDB
import pymysql

db_info = load_config(ENV_CONFIG_PATH)['db']
default_pool = PooledDB(creator=pymysql, mincached=1, maxcached=10,
                        host=db_info['host'], port=int(db_info['port']), user=db_info['user'],
                        passwd=db_info['passwd'],
                        db=db_info['database'], charset="utf8")


class POOL(object):

    @staticmethod
    def get_pool(env):
        if not env:
            return default_pool
        else:
            db_info = load_config(ENV_CONFIG_PATH)[env]
            pool = PooledDB(creator=pymysql, mincached=1, maxcached=10,
                            host=db_info['host'], port=int(db_info['port']), user=db_info['user'],
                            passwd=db_info['passwd'],
                            db=db_info['database'], charset="utf8")
            return pool
