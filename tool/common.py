# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : common.py
<AUTHOR> <EMAIL>
@Time   : 2023-10-30 10:05:12
@Version: v1.0
"""
import yaml
import json
from configparser import ConfigParser
from flask import request
from tool.logger import log


def load_yml(filepath):
    """
    get data from yml
    """
    file = open(filepath, mode='r', encoding='utf-8')
    return yaml.safe_load(file)


def get_sql(datas, key, *arg, **kwargs):
    """
    :rtype: object
    :param datas: sql_pool
    :param key: sql_tag
    :param *arg, **kwargs: replace str
    """
    try:
        if key in datas:
            sql = datas[key].replace('\n', '')
            return sql.format(*arg, **kwargs)
    except Exception as e:
        log.error('replace sql is error: {0}'.format(e))
        raise 'replace sql is error:{0}'.format(e)


def load_config(config_path):
    """
    get data what type is .ini form target path
    """
    cfg = ConfigParser()
    cfg.read(config_path, encoding='utf-8')
    dictionary = {}
    for section in cfg.sections():
        dictionary[section] = {}
        for options in cfg.options(section):
            dictionary[section][options] = cfg.get(section, options)
    return dictionary


def query_condition_joint(sql, arguments):
    """
    query sql add query condition
    """
    condition_list = []
    fuzzy_list = ["providers"]
    big_condition = ["task_id"]
    if isinstance(arguments, dict):
        for i in arguments:
            if i != 'length' and i != 'limit' and arguments[i]:
                if i in fuzzy_list:
                    condition_list.append("{key} like '%{value}%'".format(key=i, value=arguments[i]))
                elif i in big_condition:
                    condition_list.append("{key} {value}".format(key=i, value=arguments[i]))
                else:
                    condition_list.append("{key} = '{value}'".format(key=i, value=arguments[i]))
        condition_str = " and ".join(condition_list)
        if 'length' in arguments.keys():
            # 翻页的处理默认逻辑
            if not arguments['length'] or not arguments['limit']:
                start = 0
                end = 20
            # length递增值等于limit值
            else:
                start = int(arguments['length']) - int(arguments['limit'])
                end = int(arguments['limit'])
            have_condition_limit = '{origin_sql}  where {condition} ORDER BY id desc  limit {start},{end}; '
            not_condition_limit = '{origin_sql} ORDER BY id desc  limit {start},{end};'
            return have_condition_limit.format(
                origin_sql=sql, start=start, end=end, condition=condition_str) if condition_list \
                else not_condition_limit.format(
                origin_sql=sql, start=start, end=end)
            # 查询所有符合条件
        else:
            return '{origin_sql}  where {condition}'.format(
                origin_sql=sql, condition=condition_str
            ) if condition_list else '{origin_sql} '.format(origin_sql=sql)
    else:
        raise Exception('arguments type is not dict')


def api_response(msg=None, code=None, data=None, **kwargs):
    response = {'code': 0, 'msg': 'success', 'data': []}

    if code:
        response['code'] = code
    if msg:
        response['msg'] = msg
    if data:
        response['data'] = data
    for key, value in kwargs.items():
        response[key] = value
    return response


def get_request_params():
    if request.method == 'GET':
        data = request.args.to_dict()
    else:
        data = json.loads(request.get_data())
    return data


def get_upload_param():
    return request.files["file"]
