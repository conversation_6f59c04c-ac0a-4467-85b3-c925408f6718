import json
import os.path
import traceback
import jsonlines
import re
from tool.util import zip_dictionary, send_card_message, open_xlsx_for_path
from test_conifg.path_config import DOWNLOAD_PATH
from test_conifg.config import ip, port
from tool.logger import log

url_data_list = ["autoqa", "search", "generate_summary", "generate_question", ]
normal_data = ['model_footprint', 'repeated_detection', 'leak_prompt', 'follow_format', 'personality',
               'fixed_context_qa',
               'jailbreak', 'fallback', 'inference', 'concise_vs_abundunt', "abnormal_language"]
question_sheet = ['rebuild_query', 'similar_questions']


def get_chunks():
    return {
        "source_id": "",
        "content": ""
    }


def chunks_split(debug_info):
    try:
        pa_prompt = debug_info.get("ialgo_debug_info").get("qa_prompt")
    except Exception:
        pa_prompt = debug_info.get("qa_prompt")

    #  如果空直接返回空数组
    if not pa_prompt: return []

    source_id_patten = re.compile("Source_id\":(.*?),\"Content")
    content_patten = re.compile("Content\":\s+\"(.*?)\"]")
    res_source_id = re.findall(source_id_patten, pa_prompt)
    res_content = re.findall(content_patten, pa_prompt)

    if res_source_id and len(res_source_id) == len(res_content):
        chunk_list = []
        for i in range(len(res_source_id)):
            tmp = get_chunks()
            tmp["source_id"] = str(res_source_id[i]).strip()
            tmp["content"] = res_content[i]
            chunk_list.append(tmp)
            # print(res_source_id,res_content)
            # print(json.dumps(chunk_list,ensure_ascii=False))
        return chunk_list
    else:
        print("解析错误,没有找到sourceId、content")
        return []


def get_role():
    return {
        "name": "聚言",
        "type": "universal",
        "style": "rigorous",
        "company": "猎户星空",
        "fallback": "抱歉，请上传资料让我学习",
        "talking_style": "严谨",
        "is_fallback": ""
    }


def get_tmp():
    return {
        "question": "",
        "chunks": [
            {
                "source_id": "",
                "content": ""
            }
        ],
        "standard_answers": [

        ],
        "case_types": [

        ],
        "keywords": [

        ],
        "role": {
            "name": "聚言",
            "type": "universal",
            "style": "rigorous",
            "company": "猎户星空",
            "fallback": "抱歉，请上传资料让我学习",
            "talking_style": "严谨"
        }
    }


class ConvertData(object):
    def __init__(self, file_path):
        self.xls = open_xlsx_for_path(file_path=file_path, sheet_name=None)

    def gen_question(self, file_name, sheet_name):
        i = 0  # 记录当前的执行的索引
        for row in self.xls[sheet_name].iterrows():
            temp = {
                "question": ""
            }
            try:
                i += 1
                if row[1].get("question"):
                    temp["question"] = row[1].get("question")
                f = jsonlines.open(file=file_name, mode="a")
                f.write(temp)
                # f.write(json.dumps(temp,ensure_ascii=False))
                f.close()
            except Exception:
                log.error("执行异常数据：处理【{sheet_name}】中第{i}条数据。 {error_msg}".format(sheet_name=sheet_name, i=i,
                                                                                             error_msg=traceback.format_exc()))

    def gen_doc_url(self, file_name: str, sheet_name):
        i = 0  # 记录当前的执行的索引
        for row in self.xls[sheet_name].iterrows():
            temp = {
                "doc_name": "",
                "doc_url": "",
            }
            try:
                i += 1
                # if row[1].get("question"):
                #     temp["question"] = row[1].get("question")
                temp["doc_name"] = row[1].get("doc_name")
                temp["doc_url"] = row[1].get("doc_url")
                f = jsonlines.open(file=file_name, mode="a")
                f.write(temp)
                # f.write(json.dumps(temp,ensure_ascii=False))
                f.close()
            except Exception:
                log.error("执行异常数据：处理【{sheet_name}】中第{i}条数据。 {error_msg}".format(sheet_name=sheet_name, i=i,
                                                                                             error_msg=traceback.format_exc()))
        if sheet_name == "search":
            file_name = file_name.replace("file.jsonl", "question.jsonl")
            self.gen_question(file_name, sheet_name)

    def gen_normal(self, file_name, sheet_name):
        i = 0  # 记录当前的执行的索引
        for row in self.xls[sheet_name].iterrows():
            try:
                i += 1
                # print(i)
                temp = get_tmp()
                debug = row[1].get("content").replace("\\n", "").replace("true", "True").replace("false", "False")
                try:
                    chunks = chunks_split(eval(debug)) if debug else []
                    temp["chunks"] = chunks
                except Exception:
                    temp["chunks"] = []
                temp["question"] = str(row[1].get("question")).strip().replace("\n", "")
                #   answer 、case_type 、matched_source_ids预定义按照 “,” 逗号分割，后续看根据实际分隔符调整即可
                temp["standard_answers"] = re.split(",",
                                                    str(row[1].get("standard_answers")).strip().replace("\n", "")) if \
                    row[1].get(
                        "standard_answers") else []
                temp["case_types"] = re.split(",", str(row[1].get("case_types")).strip().replace("\n", "")) if row[
                    1].get("case_types") else []
                temp["keywords"] = re.split("[｜|]", str(row[1].get("key_words")).replace(" ","") .replace("\n", "")) if row[
                    1].get("key_words") else []
                temp["matched_source_ids"] = re.split(",", str(row[1].get("matched_source_ids")).strip().replace("\n",
                                                                                                                 "")) if \
                    row[1].get(
                        "matched_source_ids") else []
                role = get_role()
                role["name"] = row[1].get("name")
                role["type"] = row[1].get("type")
                role["style"] = row[1].get("style")
                role["company"] = row[1].get("company")
                role["fallback"] = row[1].get("fallback")
                role["talking_style"] = row[1].get("talking_style")
                role["fallback"] = row[1].get("fallback")
                if str(sheet_name).strip() == "fallback":
                    role["is_fallback"] = row[1].get("is_fallback")
                temp["role"] = role
                f = jsonlines.open(file=file_name, mode="a")
                f.write(temp)
                # f.write(json.dumps(temp,ensure_ascii=False))
                f.close()
                # data_list.append(temp)
            except Exception:
                log.error("执行异常数据：处理【{sheet_name}】中第{i}条数据。 {error_msg}".format(sheet_name=sheet_name, i=i,
                                                                                             error_msg=traceback.format_exc()))

    def gen_data(self):
        sheet_name_list = list(self.xls)  # 获取所有sheet_name
        sheet_index = 1  # 标记当前sheet索引
        for sheet_name in sheet_name_list:
            sheet_n = str(sheet_name).strip()
            output_path = "{download_path}{sep}效果优化测试数据{sep}{task_name}{sep}".format(
                download_path=DOWNLOAD_PATH,
                task_name=sheet_n, sep=os.sep)
            if not os.path.exists(output_path):
                os.makedirs(output_path)
            #   遍历所有sheet
            data_list = []  # 保存所有的要输出的数据
            log.info("当前sheet为：{sheet_name},当前第{index}个,共有{count}个sheet".format(sheet_name=sheet_name,
                                                                                          index=sheet_index,
                                                                                          count=len(sheet_name_list)))
            if sheet_n in url_data_list:
                f_name = "file.jsonl"
            else:
                f_name = "qa.jsonl"
            file_name = "{out_path}{file_name}".format(out_path=output_path, file_name=f_name)
            if os.path.exists(file_name):
                os.remove(file_name)
            if sheet_n in url_data_list:
                self.gen_doc_url(file_name=file_name, sheet_name=sheet_name)
            elif sheet_n in normal_data:
                self.gen_normal(file_name=file_name, sheet_name=sheet_name)
            elif sheet_n in question_sheet:
                self.gen_question(file_name=file_name, sheet_name=sheet_name)
            else:
                print("next")
                # gen_normal(file_name=file_name,sheet_name=sheet_name)
            sheet_index += 1
        zip_dictionary(DOWNLOAD_PATH + "{}data".format(os.sep), DOWNLOAD_PATH + "{}效果优化测试数据".format(os.sep))
        title = "回复优化效果数据生成任务执行完成"
        path = "/api/download/data.zip"
        url = "http://{ip}:{port}{path}".format(ip=ip, port=port, path=path)
        send_card_message(title=title, down_url=url)
#
# if __name__ == '__main__':
    # convert =ConvertData()
    # convert.gen_data("效果优化测试数据准备.xlsx")