# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : shumei.py
<AUTHOR> <EMAIL>
@Time   : 2023-11-11 13:05:14
@Version: v1.0
"""
import json
import os
import time

import requests
import uuid
from tool.util import *
from tool.logger import log
from concurrent.futures import ThreadPoolExecutor
from concurrent.futures import as_completed
from test_conifg.config import ip, port
from test_conifg.path_config import DOWNLOAD_PATH

ex = ThreadPoolExecutor(max_workers=100)

URL = "http://api-text-bj.fengkongcloud.com/text/v4"
header = {"content-Type": "application/json"}
accessKey = "ZnTPUJUSZ8DGCUKSsMm8"
app_id = "default"
#   question传 user，answer传aitext aitext_gy user_gy
event_id = "user_gy"


# @method_logs
def request(text: str) -> dict:
    token_id = str(uuid.uuid4()).replace("-", "")
    payload = {
        "accessKey": accessKey,
        "appId": app_id,
        "eventId": event_id,
        "type": "TEXTRISK",
        "data":
            {
                "text": text,
                "tokenId": token_id
            }
    }
    res = requests.request(url=URL, method="post", json=payload, headers=header)
    if res.status_code == 200:
        res_json = res.json()
        return res_json
    else:
        log.error("request error:{}".format(res.text))
        return {"data": res}


def get_answer(args):
    query_text = str(args.get("line")).strip()
    f = args.get("fs")
    response_data = request(query_text)
    f.write(query_text + "-->" + json.dumps(response_data, ensure_ascii=False) + "\n")
    code = response_data.get("code", 999)
    if code != 1100:
        log.error("数据返回非1100：-->{}".format(code))
    msg = response_data.get("message", "")
    request_id = response_data.get("requestId", "")
    risk_level = response_data.get("riskLevel", "")
    risk_detail = response_data.get("riskDetail", {})
    risk_description = response_data.get("riskDescription", "")
    if risk_detail.get("matchedLists"):
        wd_list = []
        for word in risk_detail.get("matchedLists", {}):
            wd_list = [wd.get("word") for wd in word.get("words")]

        wd_str = ",".join(wd_list)
    else:
        wd_str = ""
    if risk_detail.get("riskSegments"):
        risk_segments = str([risk_segments.get("segment") for risk_segments in risk_detail.get("riskSegments", {})])
    else:
        risk_segments = ""
    args.get("sheet").append(
        [args.get("indx", ""), args.get("Id", ""), query_text, wd_str, risk_segments, code, msg, request_id, risk_level,
         risk_description])


def upload_sensitive_shumei(input_file, f_type: str, output_f: str):
    """
    调用数美检验敏感词
    根据question列表获取模型给的回答，并将回答记录在Excel表格，包含question、返回的code、Illegal Status、Answer Text
    :return:
    """
    workbook = openpyxl.Workbook()
    sheet = workbook.active
    sheet.append(
        ["index", "Id", "Query Text", "illegal_word", "riskSegments", "code", "message", "requestId", "riskLevel",
         "riskDescription"])
    output_path = "{output}{sep}".format(output=DOWNLOAD_PATH, sep=os.sep)
    fs_path = DOWNLOAD_PATH + os.sep + "shumei.txt"
    if os.path.exists(fs_path):
        os.remove(fs_path)
    f = open(fs_path, mode="w+")
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    future_list = []
    if f_type == "json":
        with input_file as file:
            index = 0
            for line in file:
                # if index>=1:break
                index += 1
                agrs = {
                    "line": line,
                    "index": index,
                    "work": workbook,
                    "sheet": sheet,
                    "file": output_path + output_f,
                    "fs": f
                }
                future = ex.submit(get_answer, agrs)
                future_list.append(future)
            log.info("任务全部发出，执行中")
    else:
        sheet_name_list = list(input_file)  # 获取所有sheet_name
        sheet_index = 1  # 标记当前sheet索引

        for sheet_name in sheet_name_list:
            index = 0
            for row in input_file[sheet_name].iterrows():
                # if index >= 1: break
                index += 1
                agrs = {
                    "indx": row[1].get("index", ""),
                    "Id": row[1].get("ID", ""),
                    #   默认第一列就是question，且只有一列
                    "line": row[1][0],
                    "index": index,
                    "work": workbook,
                    "sheet": sheet,
                    "file": output_path + output_f,
                    "fs": f
                }
                future = ex.submit(get_answer, agrs)
                future_list.append(future)
                if index % 100 == 0:
                    time.sleep(3)
            log.info("任务全部发出，执行中")
    tqdm_task = get_tqdm(iter_len=len(future_list), desc="敏感词进度")
    for future in as_completed(future_list):
        tqdm_task.update(1)
    tqdm_task.close()
    f.close()
    workbook.save(output_path + output_f)
    log.info("save result file to: {}".format(output_path + output_f))
    path = "/api/download/{}".format(output_f)
    title = "数美敏感词任务完成"
    down_url = "http://{ip}:{port}{path}".format(ip=ip, port=port, path=path)
    send_card_message(title=title, down_url=down_url)
