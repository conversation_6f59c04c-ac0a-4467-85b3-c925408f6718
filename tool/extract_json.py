#提取json文件中所有传参，并依次放入Excel中各列中，标准json
#kzy
# _*_ coding:utf-8 _*_
import json
import pandas as pd

with open('/Users/<USER>/Downloads/orionstarzhenge311ans.json', 'r', encoding='utf-8') as file:
    lines = file.readlines()

data = []
for line in lines:
    try:
        item = json.loads(line)
        data.extend(item)  # 使用extend将每行的JSON对象添加到data列表中
    except json.JSONDecodeError:
        pass

questions = []
answers = []
texts = []
for item in data:
    if isinstance(item, dict):
        question = item.get('question', '')
        answer = item.get('answer', '')
        # text = item.get('text', '')
        questions.append(question)
        answers.append(answer)
        # texts.append(text)

df = pd.DataFrame({
    'question': questions,
    'answer': answers,
    # 'text': texts
})

df.to_excel('/Users/<USER>/Downloads/orionstarzhenge311ans.xlsx', index=False)
