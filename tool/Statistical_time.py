#统计各种耗时标准
#kzy
# _*_ coding:utf-8 _*_


import pandas as pd

# 读取Excel文件
df = pd.read_excel('/Users/<USER>/Downloads/msg_privatization.xlsx')

# 计算第一列数据的99%的平均值
mean_99 = df.iloc[:, 0].quantile(0.99)

# 计算第一列数据的95%的平均值
mean_95 = df.iloc[:, 0].quantile(0.95)

# 计算第一列数据的中位数
median = df.iloc[:, 0].median()

# # 计算大于2的数据数量
# count_gt_number = df[df.iloc[:, 9] < 30].shape[0]
# count_gt_number1 = df[df.iloc[:, 9] < 35].shape[0]



# 打印结果
print("99%的平均值:", mean_99)
print("95%的平均值:", mean_95)
print("中位数:", median)
# print("小于15字/S的数据数量:", count_gt_number)
# print("小于20字/S的数据数量:", count_gt_number1)