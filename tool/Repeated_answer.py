import pandas as pd
import re

df = pd.read_excel('/Users/<USER>/Downloads/chatQA.xlsx')


result_data = []


for index, row in df.iterrows():
    source_text = str(row['回复'])

    sentences = re.split(r'。|！|？|；|;|!|,', source_text)


    sentence_counts = {}


    for sentence in sentences:
        sentence = sentence.strip()
        if sentence in sentence_counts:
            sentence_counts[sentence] += 1
        else:
            sentence_counts[sentence] = 1


    duplicate_sentences = [k for k, v in sentence_counts.items() if v >= 2]


    if duplicate_sentences:
        duplicate_text = '|'.join(duplicate_sentences)
        result_data.append({'原句': source_text, '重复句子': duplicate_text})
    else:

        result_data.append({'原句': source_text, '重复句子': ''})


result_df = pd.DataFrame(result_data)

result_df.to_excel('/Users/<USER>/Downloads/chatQA1.xlsx', index=False)
