
import tkinter as tk
from tkinter import messagebox
from itertools import combinations
from xmindparser import xmind_to_dict
import pandas as pd
import os

# 当用户点击按钮时触发的函数


def add_title():
    file_path = entry.get()
    if file_path:
        try:
            gen(file_path)
            update_titles_label("case生成结束")
        except:
            update_titles_label("case生成异常，检查路径")

# 更新显示标题列表的标签


def update_titles_label(content):
    titles_label.config(text=content)


def gen(file_path):
    update_titles_label("case生成中")
    out = xmind_to_dict(file_path)
    case_orion_data = out[0]['topic']
    all_titles = get_all_titles(case_orion_data, i=0)
    df = pd.DataFrame(all_titles)
    start_index = str(file_path).rfind(os.sep) + 1
    end_index = str(file_path).rfind(".")
    file_name = file_path[start_index:end_index]
    df.to_excel(f"{file_name}.xlsx", index=False)


def get_all_titles(node, current_path=None, i=0):
    if current_path is None:
        current_path = dict()
    # 添加当前节点的标题到路径中
    current_path[i] = node['title']

    # 如果没有子节点，则返回当前路径
    if not node.get('topics'):
        return [current_path]

    paths = []
    # 遍历子节点
    for topic in node['topics']:
        # 递归获取子节点的标题组合
        sub_paths = get_all_titles(topic, dict(current_path), i + 1)
        paths.extend(sub_paths)

    return paths


def center_window(root, width, height):
    # 获取屏幕宽度和高度
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()

    # 计算窗口的位置坐标
    x = (screen_width // 2) - (width // 2)
    y = (screen_height // 2) - (height // 2)

    # 设置窗口位置
    root.geometry(f'{width}x{height}+{x}+{y}')


def clear_entry_on_click(entry):
    """当用户点击输入框时清除默认文本"""
    if entry.cget("fg") == "grey":
        entry.delete(0, tk.END)
        entry.config(fg='black')


def restore_default_text(entry, default_text):
    """当输入框失去焦点且为空时恢复默认文本"""
    if not entry.get():
        entry.insert(0, default_text)
        entry.config(fg='grey')


def setup_default_text(entry, default_text):
    """设置输入框的默认文本和相关事件"""
    entry.insert(0, default_text)
    entry.config(fg='grey')
    entry.bind('<FocusIn>', lambda event: clear_entry_on_click(entry))
    entry.bind('<FocusOut>', lambda event: restore_default_text(entry, default_text))


# 创建主窗口
root = tk.Tk()
center_window(root, 1000, 650)
root.title("上传文件")

# 创建一个输入框
entry = tk.Entry(root, width=70, font=30)
entry.pack(pady=50)
setup_default_text(entry, "请输入文件路径...")

# 创建一个按钮
button = tk.Button(root, text="确  定", font=30, width=20, height=5, fg='red', command=add_title)
button.pack(pady=100)

# 创建一个标签用于显示标题列表
titles_label = tk.Label(root, text="请上传文件,生成的case在当前路径哦~", font=40, fg='red')
titles_label.pack(pady=50)
root.geometry("800x600")
# 运行主循环
root.mainloop()
