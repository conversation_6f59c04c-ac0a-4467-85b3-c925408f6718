# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : decorators.py
<AUTHOR> <EMAIL>
@Time   : 2023-10-30 10:25:58
@Version: v1.0
"""

from functools import wraps
from flask import jsonify, make_response
from tool.logger import log
from tool.common import load_yml
import traceback


def response_format(func):
    """
    return jsonify format
    decorator function
    """

    @wraps(func)
    def wrappers(*args, **kwargs):
        if not args and not kwargs:
            ret = jsonify(func())
        else:
            ret = jsonify(func(*args , **kwargs))

        return ret

    return wrappers


def cros(func):
    @wraps(func)
    def wrappers():
        ret = make_response(func())
        ret.headers['Access-Control-Allow-Methods'] = 'HEAD, GET, POST, PUT, DELETE, PATCH, OPTIONS'
        ret.headers['Access-Control-Allow-Headers'] = "User-Agent, X-Requested-With, Content-Type"
        return ret

    return wrappers


def method_logs(func):
    @wraps(func)
    def wrappers(*args, **kwargs):
        log.info("【{method}】 receive request params {data}{key}".format(method=func.__name__, data=args,
                                                                        key=kwargs))
        return func(*args, **kwargs)

    return wrappers


def load_payload(func):
    def function(*args, **kwargs):
        sql_data = load_yml(*args)
        payload = sql_data[func.__name__]
        return payload

    return function


def except_log(function_feature):
    def try_decorator(func):
        @wraps(func)
        def wrapped_function(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                log.error('function ' + func.__name__ + ' error {0}'.format(traceback.format_exc()))
                log.info('入参： {0},{1}'.format(args, kwargs))
                log.error(e)
                return function_feature + '异常，请排查'

        return wrapped_function

    return try_decorator
