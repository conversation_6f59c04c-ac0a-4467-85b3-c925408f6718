import pandas as pd
import random

def generate_random_chinese_string(min_length, max_length):
    chinese_characters = '一二三四五六七八九十百千万亿的一是在不了有和人这中大为上个国我以要他时来用们生到作地于出就分对成会可主发年动同工也' \
                         '能下过子说产种面而方后多定行学法所民得经十三之进着等部度家电力里如水化高自二理起小物现实加量都两体制机当使点从业本去把' \
                         '性好应开它合还因由其些然前外天政四日那社义事平形相全表间样与关各重新线内数正心反你明看原又么利比或但质气第向道命此变条只' \
                         '没结解问意建月公无系军很情者最立代想已通并提直题党程展五果料象员革位入常文总在一个小村庄的边缘住着一个少年名叫李明每天他都带着一群' \
                         '羊走进那片宁静的山林他喜欢在那里漫步聆听风吹过树叶的声音沐浴在阳光的洗礼跳跃的溪水在石头间欢快地流淌李明的家境并不富裕但他过得很知足村里' \
                         '的人们都很喜欢他因为他总是乐于助人他帮助村里的老人们修理房屋劈柴进山他总是一副乐呵呵的样子而村里的孩子们也都喜欢和他玩李明有一颗渴' \
                         '望知识的心每当夜幕降临他会偷偷摸摸地点燃蜡烛摸索着在书本中汲取知识他知道知识可以改变命运于是他日复一日地努力学习终于在一次次的努力后考上' \
                         '了县城的学校远离了家乡的他独自一人生活在陌生的环境中面对生活的压力李明没有退缩他用勤奋和智慧去克服困难在大学的日子里李明认识了许多志同' \
                         '道合的朋友他们一起学习一起成长李明追状着自己的梦想努力成为一个对社会有贡献的人他知道这不会是一条坦途但他愿意去迎接挑战用汗水浇灌希望的' \
                         '花朵直到有一天能够看到它们绽放的笑容李明相信只要坚持不懈终究会收获美好的未来'
    length = random.randint(min_length, max_length)
    return ''.join(random.choice(chinese_characters) for _ in range(length))

def generate_data(num_rows):
    data = []
    for _ in range(num_rows):
        col = generate_random_chinese_string(3, 5)
        data.append((col, col + ';' + generate_random_chinese_string(3, 5)))
    return data

def save_to_excel(data, filename):
    df = pd.DataFrame(data, columns=['汉字(3-5个字)', '汉字(3-5个字);汉字(3-5个字)'])
    df.to_excel(filename, index=False, engine='openpyxl')

if __name__ == "__main__":
    num_rows = 20000  # 你可以根据需要修改生成的行数
    data = generate_data(num_rows)
    save_to_excel(data, 'generated_data.xlsx')
