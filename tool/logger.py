# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : logger.py
<AUTHOR> <EMAIL>
@Time   : 2023-10-29 20:02:43
@Version: v1.0
"""

import gzip
import logging
import logging.handlers
import os
import time
from logging.handlers import TimedRotatingFileHandler

from test_conifg.path_config import LOG_PATH

logging.basicConfig()
logging.getLogger("requests").setLevel(logging.WARNING)


def singleton(cls, *args, **kw):
    instances = {}

    def _singleton():
        if cls not in instances:
            instances[cls] = cls(*args, **kw)
        return instances[cls]

    return _singleton


@singleton
class CreateLog(object):
    level_relations = {
        "debug": logging.DEBUG,
        "info": logging.INFO,
        "warning": logging.WARNING,
        "error": logging.ERROR
    }
    logger = logging.getLogger()

    def __init__(self, level='info'):
        logging_msg_format = '[%(asctime)s]--[%(levelname)s]--[%(module)s.py - %(lineno)d] %(message)s'
        logging_date_format = '%Y-%m-%d %H:%M:%S'
        logging_file_suffix = '%Y-%m-%d'
        logging.basicConfig(format=logging_msg_format, datefmt=logging_date_format)
        self.logger.setLevel(self.level_relations.get(level))
        if not os.path.exists(LOG_PATH):
            os.mkdir(LOG_PATH)
        log_file = LOG_PATH + '/lhtest.log'
        file_handler = SafeTimedRotatingFileHandler(log_file, 'midnight', 1, 30)
        file_handler.suffix = logging_file_suffix
        file_handler.setFormatter(logging.Formatter(logging_msg_format))
        self.logger.addHandler(file_handler)

    def get_logger(self):
        return self.logger


class SafeTimedRotatingFileHandler(TimedRotatingFileHandler):
    def __init__(self, filename, when='h', interval=1, backupCount=0, encoding=None, delay=False, utc=False):
        super().__init__(filename, when, interval, backupCount, encoding, delay, utc)

    # Gzip
    def doGzip(self, old_log):
        with open(old_log, 'rb') as old:
            with gzip.open(old_log + '.gz', 'wb') as comp_log:
                comp_log.writelines(old)
        os.remove(old_log)

    # Override doRollover
    def doRollover(self):
        """
        Override:
        1. if dfn not exist then do rename
        2. _open with "a" model
        """
        if self.stream:
            self.stream.close()
            self.stream = None
        # get the time that this sequence started at and make it a TimeTuple
        currentTime = int(time.time())
        dstNow = time.localtime(currentTime)[-1]
        t = self.rolloverAt - self.interval
        if self.utc:
            timeTuple = time.gmtime(t)
        else:
            timeTuple = time.localtime(t)
            dstThen = timeTuple[-1]
            if dstNow != dstThen:
                if dstNow:
                    addend = 3600
                else:
                    addend = -3600
                timeTuple = time.localtime(t + addend)
        dfn = self.baseFilename + "." + time.strftime(self.suffix, timeTuple)
        dfngz = dfn + ".gz"
        if not os.path.exists(dfngz) and os.path.exists(self.baseFilename):
            os.rename(self.baseFilename, dfn)
            self.doGzip(dfn)
        if self.backupCount > 0:
            for s in self.getFilesToDelete():
                os.remove(s)
        if not self.delay:
            self.mode = 'a'
            self.stream = self._open()
        newRolloverAt = self.computeRollover(currentTime)
        while newRolloverAt <= currentTime:
            newRolloverAt = newRolloverAt + self.interval
        # If DST changes and midnight or weekly rollover, adjust for this.
        if (self.when == 'MIDNIGHT' or self.when.startswith('W')) and not self.utc:
            dstAtRollover = time.localtime(newRolloverAt)[-1]
            if dstNow != dstAtRollover:
                if not dstNow:  # DST kicks in before next rollover, so we need to deduct an hour
                    addend = -3600
                else:  # DST bows out before next rollover, so we need to add an hour
                    addend = 3600
                newRolloverAt += addend
        self.rolloverAt = newRolloverAt


log = CreateLog().get_logger()
