import tiktoken
from transformers import AutoTokenizer
import os
base_dir = os.path.dirname(os.path.abspath(__file__))
print(base_dir)

ORIONSTAR_TOKENIZER_ZOO = {
    # "chatglm":  os.path.join(base_dir, "../resource/tokenizers/chatglm"),  # like chatglm tokenizer
    # "baichuan":  os.path.join(base_dir, "../resource/tokenizers/baichuan"),  # like chatglm tokenizer
    "qwen": os.path.join(base_dir, "..", "resource", "tokenizers", "qwen"),  # tokenizer for Qwen-7B-Chat
    "yi": os.path.join(base_dir, "..", "resource", "tokenizers", "yi"),   # like chatglm tokenizer
}
ORIONSTAR_SUPPORT_TOKENIZERS = list(ORIONSTAR_TOKENIZER_ZOO.keys())

TOKENIZER_OBJECT_MAP = dict()
for name in ORIONSTAR_SUPPORT_TOKENIZERS:
    _id = ("orionstar", name)
    TOKENIZER_OBJECT_MAP[_id] = AutoTokenizer.from_pretrained(
        ORIONSTAR_TOKENIZER_ZOO[name], trust_remote_code=True, revision="main"
    )

# cl100k_base_tokenizer = tiktoken.encoding_for_model("gpt-3.5-turbo")  # "cl100k_base"
# TOKENIZER_OBJECT_MAP[("openai", "gpt-3.5-turbo")] = cl100k_base_tokenizer
# TOKENIZER_OBJECT_MAP[("openai", "gpt-4")] = cl100k_base_tokenizer


def get_num_tokens(provider: str, tokenizer_name: str, text: str):
    _id = (provider, tokenizer_name)
    if provider == "orionstar":
        tmp = TOKENIZER_OBJECT_MAP[_id](text, return_tensors='pt', truncation=False)
        return tmp.input_ids.numel()
    elif provider == "openai":
        return len(TOKENIZER_OBJECT_MAP[_id].encode(text))
    else:
        raise NotImplementedError(f"{provider} not support yet")


if __name__ == "__main__":
    text = """你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊你谁谁啊"""
    token_len = get_num_tokens("orionstar", "qwen", text)
    print(token_len)
