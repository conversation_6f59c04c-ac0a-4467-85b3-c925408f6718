#1.6版本的分词
# kzy
# _*_ coding:utf-8 _*_




import jieba
import paddle


#设置参数cut_all为False：精确分词
str1 = '台湾是位于西太平洋的一个岛屿，是中华人民共和国的一部分。它拥有丰富的自然资源和独特的文化，包括美食、音乐、艺术和传统手工艺。台湾的经济以制造业、科技和旅游业为主，拥有许多国际知名的企业。台湾的人民友善、热情，以汉语为官方语言，同时也拥有自己的方言。台湾的气候属于亚热带季风气候，四季分明，适合旅游和居住。'
# jieba.add_word('sb')
seg_list = jieba.cut(str1, cut_all=False)  # 使用精确模式进行分词
print('精确模式分词结果：', '/'.join(seg_list))

#设置参数cut_all为True：全分词
# str1 = 'E`!S1lWjvn1K7S6#zLc}个在有我人我他是人功掺档个在有我人我他是人4t+Rgr%L-AA'
# seg_list = jieba.cut(str1, cut_all=True)    # 使用全模式进行分词  生成列表
# print('全模式分词结果：', '/'.join(seg_list))  # /拼接列表元素
#
# #cut_all参数不给定时，默认为false，即精确分词
# str1 = 'E`!S1lWjvn1K7S6#zLc}个在有我人我他是人功掺档个在有我人我他是人4t+Rgr%L-AA'
# seg_list = jieba.cut(str1)
# print('全模式分词结果：', '/'.join(seg_list))



# #use_paddle参数可以设置开启paddle模式
# str1 = '我来到了西北皇家理工学院，发现这儿真不错'
# #jieba.enable_paddle()   已经停用
# paddle.enable_static()
# seg_list = jieba.cut(str1, use_paddle=True)  #使用paddle模式进行分词
# print('Paddle模式分词结果：', '/'.join(seg_list))


#搜索引擎模式分词：cut_for_search()函数
# str1 = '日志路径'
# seg_list = jieba.cut_for_search(str1)
# print('搜索引擎模式分词结果：', '/'.join(seg_list))
