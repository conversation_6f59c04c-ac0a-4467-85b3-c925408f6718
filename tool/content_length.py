# 统计流式返回的字的总个数
# kzy
# _*_ coding:utf-8 _*_



import requests
import json

url = "http://10.60.13.172:8007/v1/chat/completions"
headers = {
    "Content-Type": "application/json"
}

data = {
    "model": "orionstar",
    "stream": True,
    "messages": [{"role": "user", "content": "你好"}]
}

response = requests.post(url, headers=headers, json=data, stream=True)

# 解析响应并统计字的个数
content_data = []
total_character_count = 0

# 遍历响应中的每一行数据
for line in response.iter_lines(decode_unicode=True):
    if line.startswith("data: "):
        json_data = line[6:]

        try:
            data_dict = json.loads(json_data)
        except json.JSONDecodeError:
            continue

        # 检查是否有 'choices' 字段
        if 'choices' in data_dict:
            choices = data_dict['choices']
            for choice in choices:
                # 检查是否有 'delta' 和 'content' 字段
                if 'delta' in choice and 'content' in choice['delta']:
                    content = choice['delta']['content']
                    content_length = len(content)
                    print(f"Content: {content} (Length: {content_length})")

                    content_data.append((content, content_length))

                    total_character_count += content_length

print(f"Total Character Count: {total_character_count}")
