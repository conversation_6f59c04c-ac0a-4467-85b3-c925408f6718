import random
import string
import zipfile
import traceback
import socket
import jsonlines
import openpyxl
import pandas as pd
from openpyxl import Workbook
import os
import datetime
import time
import re
import jieba
import shutil
import re
import requests
import json
from test_conifg.path_config import DOCUMENT_PATH, HOME_PATH, ENV_CONFIG_PATH
from tqdm import tqdm
from concurrent.futures.thread import Thread<PERSON>oolExecutor
from tool.common import load_config


def open_xlsx(file_path, sheet_name=""):
    """
    返回excel对象
    :param file_path:
    :return:
    """

    # wb = openpyxl.load_workbook("..//document_file//{}".format(file_path))

    # sheet = wb[sheet_name]
    xls = pd.read_excel("..{sep}document_file{sep}{path}".format(paht=file_path, sep=os.sep), sheet_name=sheet_name,
                        keep_default_na=False)
    return xls


def open_xlsx_for_path(file_path, sheet_name=None):
    """
    返回指定路径的Excel句柄
    :param file_path:
    :param sheet_name:
    :return:
    """
    xls = pd.read_excel(file_path, sheet_name=sheet_name, keep_default_na=False)
    return xls


def read_xlsx_to_dict(file_name, sheet="Sheet1"):
    """

    :param file_name:
    :param sheet:
    :return: dict{} or list[{}]
    """
    xlsx = pd.read_excel(file_name)
    dict_data = xlsx.to_dict(orient="records")
    return dict_data


def get_time(time_res="T", format="%Y%m%d%H%M%S"):
    """
    返回时间
    time_res 支持 "T","T+1D","T+1H","T+1M","T-1D","T-1H","T-1M"
    :param time_res:
    :param format:
    :return:
    """
    # log.info("get time receive data:time_res={time_res},format={format}".format(time_res=time_res,format=format))
    #   实时时间
    now = datetime.datetime.now()
    # if args.startswith("$") and args.endswith("}") and args.__contains__("{T"):
    time_format = 0
    day = 0
    hour = 0
    m = 0
    s = 0
    time_fix = 0
    # time_strs = args.replace("${", "").replace("}", "")
    # if time_strs.__contains__(","):
    #     list_args = time_strs.split(",")
    #     time_strs = list_args[0]
    #     time_format = list_args[1]
    time_strs = time_res
    if format is not None:
        time_format = format
    if time_strs.__contains__("h") or time_strs.__contains__("d") or time_strs.__contains__(
            "m") or time_strs.__contains__("s"):
        #   加减小时
        if time_strs.__contains__("h"):
            list_args = time_strs.split("h")
            time_strs = list_args[0].replace("T", "")
            hour = int(time_strs)
            # print("加减小时{}".format(hour))
        #   加减天
        elif time_strs.__contains__("d"):
            list_args = time_strs.split("d")
            time_strs = list_args[0].replace("T", "")
            if list_args.__len__() > 1:
                time_fix = list_args[1][1:]
            # print(time_fix)
            day = int(time_strs)
            # print("加减天数{}".format(day))
        #   加减分钟
        elif time_strs.__contains__("m"):
            list_args = time_strs.split("m")
            time_strs = list_args[0].replace("T", "")
            m = int(time_strs)
            # print("加减分钟{}".format(m))
        #   加减秒
        elif time_strs.__contains__("s"):
            list_args = time_strs.split("s")
            time_strs = list_args[0].replace("T", "")
            s = int(time_strs)
            # print("加减秒{}".format(s))

    offset = datetime.timedelta(days=day, hours=hour, minutes=m, seconds=s)
    re_time = (now + offset).strftime("%Y-%m-%d %H:%M:%S")
    #   时间正则表达式
    reg = "[1-9]\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])"
    is_find = re.findall(reg, time_strs)
    if is_find:
        time_strs = time_strs.replace("T:", "")
        re_time = time_strs
    elif time_strs.__contains__(":"):
        time_strs = time_strs.replace("T", "")
        time_fix = time_strs[1:]
    if time_fix:
        re_date = re_time.split(" ")[0]
        re_time = "{0} {1}".format(re_date, time_fix)
        # print(re_time)
    time_array = time.strptime(re_time, "%Y-%m-%d %H:%M:%S")
    if not time_format or time_format.__contains__("ms"):
        time_stamp = int(time.mktime(time_array)) * 1000
        # log.info("return result timestamp:{times}".format(times=time_stamp))
        return time_stamp
    elif time_format == "s":
        time_stamp_s = int(time.mktime(time_array))
        # log.info("return result timestamp:{times}".format(times=time_stamp_s))
        return time_stamp_s
    else:
        re_date = time.strftime(time_format, time_array)
        # log.info("return result timestamp:{times}".format(times=re_date))
        return re_date


def check_container_illegal(answer: str) -> str:
    """
    判断文档中的词是否出现在answer里
    :param answer:
    :return:
    """
    cut_answer = list(jieba.cut(answer))

    print(cut_answer)
    illegal_word = open("{path}{sep}illegal_words.txt".format(path=DOCUMENT_PATH, sep=os.sep))
    #   将敏感词分割
    illegal_word_list = str(illegal_word.read()).split("\n")
    for word in illegal_word_list:
        if word.__contains__("=="):
            w_list = word.split("==")
            pattern = ".+".join(w_list)
            res = re.findall(pattern, answer)
            if res:
                return pattern.replace(".+", "==")
        elif word in answer:
            return word

    return ""


def xlsx_to_jsonl(xls_file, sheet_name, json_file):
    """
    将指定的Excel表数据转为json文本
    :param xls_file:
    :param sheet_name:
    :param json_file:
    :return:
    """
    f = open_xlsx(xls_file, sheet_name)
    file = open(json_file, "a")
    for row in f.iterrows():
        dd = row[1].get("输入内容")
        if dd:
            file.writelines(dd + "\n")


def get_random_A_Z():
    """
    随机返回A-Z字母
    :return:
    """
    letters = string.ascii_uppercase
    return letters[random.randint()]


def send_message(msg: str, send_url: str = None) -> None:
    """
    发送飞书文本消息
    :param url:
    :param msg:
    :return:
    """
    if not send_url:
        url = 'https://open.feishu.cn/open-apis/bot/v2/hook/747b2c6d-dc31-4978-8b5a-43d3bf6306a7'
    else:
        url = send_url

    # 发送文本消息

    # url = chatGPT_url
    headers = {
        "Content-Type": "application/json; charset=utf-8",
    }
    payload_message = {
        "msg_type": "text",
        "content": {
            # @ 单个用户 <at user_id="ou_xxx">名字</at>
            # "text": content + "<at user_id=\"bf888888\">test</at>"
            # @ 所有人 <at user_id="all">所有人</at>
            "text": msg
        }
    }
    response = requests.post(url=url, data=json.dumps(payload_message), headers=headers)
    return response.json


def send_card_message(title: str, down_url="", msg="\n请查看:", tag="点击下载", send_url='https://open.feishu.cn/open-apis/bot/v2/hook/b611f11b-63de-48c5-9964-9268a69376e0', msg_data=None, at_msg=None, at_all=None, load_msg=None, load_data=None) -> None:
    """
    发送飞书卡片消息
    :param title:
    :param down_url:
    :param msg:
    :param tag:
    :return:
    """
    url = send_url

    headers = {
        "Content-Type": "application/json; charset=utf-8",
    }
    if msg_data:
        content = [{"tag": "text", "text": msg_data},
                   {"tag": "text", "text": msg},
                   {"tag": "a", "href": down_url, "text": tag}
                   ]
    elif at_msg:
        content = [{"tag": "text", "text": at_msg},
                   {"tag": "at", "user_id": "d4cg61de"},
                   {"tag": "at", "user_id": "489bf19b"},
                   {"tag": "at", "user_id": "8c669gcd"},
                   {"tag": "text", "text": msg},
                   {"tag": "a", "href": down_url, "text": tag}
                   ]
    elif at_all:
        content = [{"tag": "text", "text": at_all},
                   {"tag": "at", "user_id": "all"},
                   {"tag": "text", "text": msg},
                   {"tag": "a", "href": down_url, "text": tag}
                   ]
    elif load_msg:
        content = [
            {"tag": "text", "text": load_msg},
            # {"tag": "text", "text": "以下是压测结果数据:"},
            # {"tag": "code", "text": load_data}
        ]
    else:
        content = [
            {"tag": "text", "text": msg},
            {"tag": "a", "href": down_url, "text": tag}
        ]

    payload_message = {
        "msg_type": "post",
        "content": {
            "post": {
                "zh_cn": {
                    "title": title,
                    "content": [
                        content
                    ]
                }
            }
        }
    }
    response = requests.post(url=url, data=json.dumps(payload_message), headers=headers)
    return response.json


def zip_dictionary(file_name, folder):
    """
    根据指定的路径打zip包
    :param file_name:
    :param folder:
    :return:
    """
    shutil.make_archive(file_name, 'zip', folder)


def split_for_len(text: str, s_len: int) -> list:
    """
    根据指定大小切割字符串并返回切割的list结果
    :param text:
    :param s_len:
    :return:
    """
    result = []
    for i in range(0, len(text), s_len):
        result.append(text[i:i + s_len])
    return result


def get_dir_file(file_path: str) -> list:
    """
    获取指定文件夹下的所有文件，不包含隐藏文件
    :param file_path:
    :return:
    """
    filename_list = []
    for file_name in os.listdir(file_path):
        if not file_name.startswith("."):
            filename_list.append(file_name)
    return filename_list


def get_local_ip():
    try:
        # 创建一个 UDP 套接字
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # 连接任意可用地址（不需要真实建立连接）
        s.connect(('*******', 80))
        # 获取本地 IP
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        # 如果上述方法失败，返回 localhost
        return '127.0.0.1'


def wordinjaFunc(word: str):
    '''
    https://github.com/yishuihanhan/wordninja
    '''
    import wordninja
    pattern = re.compile(u"[\u4e00-\u9fa5]+")

    match_CN = pattern.findall(word)

    if match_CN:
        return word
    word_list = wordninja.split(word)
    result = " ".join(word_list)
    # print(result)
    return result


def get_tqdm(iter_len: int, desc="进度"):
    if not isinstance(iter_len, int):
        return "可遍历长度错误"
    tqdm_task = tqdm(total=iter_len, desc=desc, unit="item", ncols=80,
                     bar_format="\033[38;5;2m{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining},{rate_fmt}{postfix}]\033[0m")
    return tqdm_task


def get_path(prefix, dirs=None):
    if dirs:
        output_path = "{prefix_path}{sep}{dirs}{sep}{T}".format(prefix_path=prefix, sep=os.sep, dirs=dirs,
                                                                T=get_time(format="%Y%m%d"))
    else:
        output_path = "{prefix_path}{sep}{T}".format(prefix_path=prefix, sep=os.sep,
                                                     T=get_time(format="%Y%m%d"))
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    return output_path


def task_time(d, h, y=2024, m=1, mu=1):
    # 设置要执行脚本的时间
    execute_time = datetime.datetime(y, m, d, h, mu, 0)  # 示例为2023年12月31日上午8点

    # 获取当前时间
    current_time = datetime.datetime.now()

    # 计算需要等待的时间差
    time_difference = execute_time - current_time

    # 等待到指定时间执行脚本
    time.sleep(time_difference.total_seconds())
    return True


def calculate_score(content, keywords_str):
    # 拆分关键词并转换为小写
    keywords = [word.strip().lower() for word in keywords_str]
    # 转换内容为小写，并确保content是字符串类型
    content = str(content).lower()

    # 统计匹配词数
    matched = sum(1 for keyword in keywords if keyword in content)

    # 计算得分
    total = len(keywords)
    score = matched / total if total > 0 else 0.0
    score = round(score, 2)  # 小数点后两位
    return score


def get_exe(max_works=1):
    exe = ThreadPoolExecutor(max_workers=max_works)
    return exe


def flush_key(env="test"):
    config_data = load_config(ENV_CONFIG_PATH)

    #   获取用户名密码
    user_name = config_data[env]["user_name"]
    pwd = config_data[env]["pwd"]
    #   测试环境的地址
    BASE_HOST = "https://test-service-chatmax.orionstar.com"
    login_path = "/auth/v1/token?grant_type=password"
    get_key_host = "https://test-chatmax.orionstar.com"
    get_key_path = "/api/user-command/upload.getUrlAndKey"
    session = requests.session()
    headers = {
        "Content-Type": "application/json;charset=UTF-8",
        "Apikey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ey AgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE"
    }
    data = {"email": user_name, "gotrue_meta_security": {}, "password": pwd,
            "grant_type": "password"}
    r = session.post(url=BASE_HOST + login_path, json=data, headers=headers)
    res = r.json()
    refresh_token = res.get("refresh_token")
    token = res.get("access_token")
    cookie = "supabase-auth-token=[%22{}%22%2C%22{}%22]".format(token, refresh_token)
    cookie_1 = "supabase-auth-token=[\"{}\",\"{}\"]".format(token, refresh_token)
    # print(cookie)
    headers = {
        "Content-Type": "multipart/form-data",
        "Cookie": cookie
    }
    r = session.post(url=get_key_host + get_key_path, headers=headers)
    res = r.json()
    key = res.get("data").get("api_key")
    print(f"获取key成功:->{key}")
    return key


t = get_time("T-10h", "%Y-%m-%d")
print(t)

ip = get_local_ip()
print(ip)
