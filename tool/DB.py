# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : DB.py
<AUTHOR> <EMAIL>
@Time   : 2023-10-29 17:16:24
@Version: v1.0
"""

import pymysql
from tool.logger import log
import threading
from settings import POOL,default_pool
import pymysql.cursors
lock = threading.Lock()


class DB(object):

    @staticmethod
    def query_one(sql, env=None):
        # 查询一个
        pool = POOL.get_pool(env) if env else default_pool
        con = pool.connection()
        cursors = con.cursor(cursor=pymysql.cursors.DictCursor)
        # log.info("pool is :{0},con is {1}".format(pool, con))
        log.debug('execute query one sql: {0}'.format(sql))
        try:
            lock.acquire()
            cursors.execute(sql)
            result = cursors.fetchone()
            cursors.close()
            con.close()
            lock.release()
            return result
        except Exception as e:
            log.error('query one is error sql = {0} msg = {1}'.format(sql, e))
            return None

    @staticmethod
    def query_all(sql, env=None):

        # 查询所有
        pool = POOL.get_pool(env) if env else default_pool
        con = pool.connection()
        cursors = con.cursor(cursor=pymysql.cursors.DictCursor)
        # log.info("pool is :{0},con is {1}".format(pool, con))
        log.debug('execute query all sql: {0}'.format(sql))
        try:
            lock.acquire()
            cursors.execute(sql)
            result = cursors.fetchall()
            cursors.close()
            lock.release()
            # log.info('query all result: {0}'.format(result))
            con.close()
            return result
        except Exception as e:
            lock.release()
            log.error('query all is error sql = {0} msg = {1}'.format(sql, e))
            return None

    @staticmethod
    def change_datas(sql, args=None, env=None):
        # 增，删，改
        pool = POOL.get_pool(env) if env else default_pool
        con = pool.connection()
        cursors = con.cursor(cursor=pymysql.cursors.DictCursor)
        log.debug('inert into sql: {0}'.format(sql))
        try:
            lock = threading.Lock()
            lock.acquire()
            result = cursors.execute(sql, args)
            cursors.close()
            con.commit()
            con.close()
            lock.release()
            # log.info('inert into reault sql = {0} msg = {1}'.format(sql, result))
            return result
        except Exception as e:
            log.error('change_data sql error sql = {0} msg = {1}'.format(sql, e))
            return None

    @staticmethod
    def batch_insert(sql, values, env=None):
        try:
            pool = POOL.get_pool(env) if env else default_pool
            con = pool.connection()
            cursors = con.cursor(cursor=pymysql.cursors.DictCursor)
            result = cursors.executemany(sql, values)
            cursors.close()
            con.commit()
            con.close()
            return result
        except Exception as err:
            log.error('import failed with error: {0}'.format(err))
            return None

    def closes(self):
        # 关闭数据库连接
        self._start()
        try:
            self.active = False
            self.con.close()
            log.debug('database is closed: {0}'.format(self.host))
        except Exception as e:
            self.active = False
            log.error('database is closed error: {0}'.format(e))
            raise 'server is error {0}'.format(e)
