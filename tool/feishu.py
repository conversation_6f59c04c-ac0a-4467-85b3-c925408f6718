# -- coding: utf-8 --
# @Time : 2025/3/27 10:00
# <AUTHOR> <EMAIL>
# @File : feishu.py
# @Software: cursor
# 飞书API文档：https://open.larkoffice.com/document/server-docs/im-v1/message/create
# 描述：操作飞书文档


import lark_oapi as lark
from typing import Optional, List
import json
from lark_oapi.api.bitable.v1 import (
    BaseResponse, CreateAppResponseBody, CreateAppTableResponseBody,
    CreateAppRequest, ReqApp, CreateAppTableRecordRequest,
    AppTableRecord, CreateAppTableRecordResponse, ReqTable, AppTableCreateHeader,
    CreateAppTableRequest, CreateAppTableRequestBody,
    BatchCreateAppTableRecordRequest,
    BatchCreateAppTableRecordRequestBody
)


class CreateAppAndTablesRequest(object):
    def __init__(self, name, folder_token, tables) -> None:
        super().__init__()
        self.name: Optional[str] = name  # 多维表格名称，必填
        self.folder_token: Optional[str] = folder_token  # 多维表格归属文件夹，必填
        self.tables: List[ReqTable] = tables  # 数据表，不填则不创建


class CreateAppAndTablesResponse(BaseResponse):
    def __init__(self):
        super().__init__()
        self.create_app_response: Optional[CreateAppResponseBody] = None
        self.create_app_tables_response: List[CreateAppTableResponseBody] = []


# 创建多维表格
def create_app_and_tables(client: lark.Client, request: CreateAppAndTablesRequest) -> BaseResponse:
    # 创建多维表格
    create_app_req = CreateAppRequest.builder() \
        .request_body(ReqApp.builder()
                      .name(request.name)
                      .folder_token(request.folder_token)
                      .build()) \
        .build()

    create_app_resp = client.bitable.v1.app.create(create_app_req)
    lark.logger.info("创建多维表格")
    # lark.logger.info(lark.JSON.marshal(create_app_resp, indent=4))
    if not create_app_resp.success():
        lark.logger.error(
            f"client.bitable.v1.app.create failed, "
            f"code: {create_app_resp.code}, "
            f"msg: {create_app_resp.msg}, "
            f"log_id: {create_app_resp.get_log_id()}")
        return create_app_resp
    # 添加数据表
    option = lark.RequestOption.builder().headers({"X-Tt-Logid": create_app_resp.get_log_id()}).build()
    tables = []
    for table in request.tables:
        create_app_table_req = CreateAppTableRequest.builder() \
            .app_token(create_app_resp.data.app.app_token) \
            .request_body(CreateAppTableRequestBody.builder()
                          .table(table)
                          .build()) \
            .build()

        create_app_table_resp = client.bitable.v1.app_table.create(create_app_table_req, option)

        if not create_app_table_resp.success():
            lark.logger.error(
                f"client.bitable.v1.app_table.create failed, "
                f"code: {create_app_table_resp.code}, "
                f"msg: {create_app_table_resp.msg}, "
                f"log_id: {create_app_table_resp.get_log_id()}")
            return create_app_table_resp

        tables.append(create_app_table_resp.data)

    # 返回结果
    response = CreateAppAndTablesResponse()
    response.code = 0
    response.msg = "success"
    response.create_app_response = create_app_resp.data
    response.create_app_tables_response = tables
    lark.logger.info("创建多维表格sheet")
    lark.logger.info(lark.JSON.marshal(response, indent=4))
    return response


def create_table(client: lark.Client, folder_token: str, table: ReqTable):
    tables = []
    create_app_table_req = CreateAppTableRequest.builder() \
        .app_token(folder_token) \
        .request_body(CreateAppTableRequestBody.builder()
                      .table(table)
                      .build()) \
        .build()
    create_app_table_resp = client.bitable.v1.app_table.create(create_app_table_req)
    if not create_app_table_resp.success():
        lark.logger.error(
            f"client.bitable.v1.app_table.create failed, "
            f"code: {create_app_table_resp.code}, "
            f"msg: {create_app_table_resp.msg}, "
            f"log_id: {create_app_table_resp.get_log_id()}")
        return create_app_table_resp

    tables.append(create_app_table_resp.data)

    # 返回结果
    response = CreateAppAndTablesResponse()
    response.code = 0
    response.msg = "success"
    response.create_app_response = create_app_table_resp.data
    response.create_app_tables_response = tables
    lark.logger.info("创建多维表格sheet")
    lark.logger.info(lark.JSON.marshal(response, indent=4))
    return response


class Feishu:
    def __init__(self, folder_token: str):
        self.app_id = "cli_a76ca5f1a528d00c"
        self.app_secret = "V6ku4CXIW4W1fiviinyRudEyPLe6NRIS"
        self.folder_token = folder_token

    def get_client(self):
        client = lark.Client.builder() \
            .app_id(self.app_id) \
            .app_secret(self.app_secret) \
            .log_level(lark.LogLevel.DEBUG) \
            .build()
        return client

    def addBitableRecordBatch(self, client, file_id, table_id, records_list, user_id_type="user_id"):
        new_records_list = []
        for record in records_list:
            # 将字典类型的数据转换为字符串,其他类型数据保持不变
            for k, v in record.items():
                if isinstance(v, (dict, list)):
                    record[k] = json.dumps(v, ensure_ascii=False)
                elif isinstance(v, (int, float)):
                    record[k] = str(v)
            new_records_list.append(record)
        # 构造请求对象
        request = BatchCreateAppTableRecordRequest.builder() \
            .app_token(file_id) \
            .table_id(table_id) \
            .user_id_type(user_id_type) \
            .request_body(BatchCreateAppTableRecordRequestBody.builder()
                          .records([AppTableRecord.builder()
                                    .fields(record).build() for record in new_records_list])
                          .build()) \
            .build()

        # 发起请求
        response = client.bitable.v1.app_table_record.batch_create(request)

        # 处理失败返回
        if not response.success():
            lark.logger.error(
                f"batch create records failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}")
            return None

        # 处理业务结果
        lark.logger.info(lark.JSON.marshal(response.data, indent=4))
        return response.data


def get_fieled(records_list):
    fields = []
    for key, value in records_list[0].items():
        fields.append(AppTableCreateHeader.builder().field_name(key).type(1).build())
        # if isinstance(value, (int, float)):
        #     fields.append(AppTableCreateHeader.builder().field_name(key).type(2).build())
        # # elif isinstance(value, list):
        #     # fields.append(AppTableCreateHeader.builder().field_name(key).type(11).build())
        # else:
        #     fields.append(AppTableCreateHeader.builder().field_name(key).type(1).build())

    return fields


def get_field_url(data: BaseResponse) -> tuple:
    file_id = data.create_app_response.app.app_token
    table_id = data.create_app_tables_response[0].table_id
    view_id = data.create_app_tables_response[0].default_view_id
    url = f"https://cheetah-mobile.feishu.cn/base/{file_id}?table={table_id}&view={view_id}"
    return file_id, table_id, url


def add_feishu_record(file_name: str, table_name: str, records_list: list, folder_token: str) -> dict:
    feishu = Feishu(folder_token)
    client = feishu.get_client()
    fields = get_fieled(records_list)
    tables = [ReqTable.builder().name(table_name).fields(fields).build()]
    req = CreateAppAndTablesRequest(name=file_name, folder_token=folder_token, tables=tables)
    app_table_resp = create_app_and_tables(client, req)
    if app_table_resp.code != 0:
        return {"status": False, "url": "创建表格失败"}
    file_id, table_id, url = get_field_url(app_table_resp)
    # 一次只处理100条数据，超过100条数据，分批处理,结果合并
    # if len(records_list) > 100:
    success = True
    for i in range(0, len(records_list), 5):
        new_records_list = records_list[i:i + 5]
        res = feishu.addBitableRecordBatch(client, file_id, table_id, new_records_list)
        if not res:
            success = False
            break
    if not success:
        return {"status": False, "url": "写入飞书表格失败"}
    return {"status": True, "url": url, "file_id": file_id, "table_id": table_id}


def add_feishu_record_by_table(table_name: str, records_list: list, folder_token: str) -> tuple:
    # 在现有的飞书文档下创建飞书表格里的table
    feishu = Feishu(folder_token)
    client = feishu.get_client()
    fields = get_fieled(records_list)
    table = ReqTable.builder().name(table_name).fields(fields).build()
    app_table_resp = create_table(client, folder_token, table)
    if app_table_resp.code != 0:
        return False, "创建表格失败"
    # file_id, table_id, url = get_field_url(app_table_resp)
    table_id = app_table_resp.create_app_tables_response[0].table_id
    # 一次只处理100条数据，超过100条数据，分批处理,结果合并
    # if len(records_list) > 100:
    success = True
    for i in range(0, len(records_list), 100):
        new_records_list = records_list[i:i + 100]
        res = feishu.addBitableRecordBatch(client, folder_token, table_id, new_records_list)
        if not res:
            success = False
            break
    if not success:
        return False, "写入飞书表格失败"
    return True, "写入飞书表格成功"


# if __name__ == "__main__":
#     import pandas as pd
#     data = pd.DataFrame([{"name": "梁伟", "age": 3.5, "msg": [{"token": 30}]}, {"name": "张三", "age": 30, "msg": [{"token": 30}]}])
#     # data = pd.DataFrame([{"name": "梁伟"}, {"name": "张三"}])
#     file_name = "梁伟测试自动化2"
#     table_name = "V1.6.2"

#     # res = add_feishu_record(file_name=file_name, table_name=table_name, records_list=data.to_dict('records'), folder_token="Do5HboA5naRG0tsWIkGceUyynAc")
#     add_feishu_record_by_table(table_name=table_name, records_list=data.to_dict('records'), folder_token="GHKNbfOIcaNr4nslHa1c33TSnQM")
