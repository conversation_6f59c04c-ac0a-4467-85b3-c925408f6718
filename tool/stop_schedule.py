# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : stop_schedule.py
<AUTHOR> <EMAIL>
@Time   : 2024-06-07 19:14:40
@Version: v1.0
"""
import schedule
import time
import os

def end_script():
    # 获取当前时间
    current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"当前时间为: {current_time}")

    # 结束脚本
    os.system("pkill -f kill_script.py")
    print("脚本已结束")

# 设置脚本结束日期和时间（6月10日早晨6点）
end_date = "2024-06-07"
end_time = "19:20"
end_datetime = f"{end_date} {end_time}"
import datetime
import time

end_date = datetime.datetime(2024, 6, 7, 19, 43, 0)

if __name__ == '__main__':
    i=0
    while True:
        if datetime.datetime.now() > end_date:
            end_script()
            time.sleep(1)
            break
