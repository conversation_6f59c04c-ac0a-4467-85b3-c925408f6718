# 国内版本线上环境批量获取问答结果
# kzy
# _*_ coding:utf-8 _*_




import requests
import json
import openpyxl

url = 'https://openapi.chatmax.net/v1/ctai/query_text_chat'

headers = {
    'Content-Type': 'application/json',
    'orionstar-api-Key': 'bz6177b5c74abfe029cfc10199e297ad94ze3ccde62f584ee448bd3571563e013c7'
}

workbook = openpyxl.Workbook()
sheet = workbook.active
sheet.append(["Query Text", "Ret", "Msg", "Illegal Status", "Answer Text"])

with open('/Users/<USER>/Downloads/pyshuju/minganciceshiji.json', 'r', encoding='utf-8') as file:
    for line in file:
        query_text = line.strip()
        data = {
            "query_text": query_text,
            "session_id": "orinoutsidb3c1655ee429eb5421844324961ebd00",
            "stream": 0
        }

        response = requests.post(url, headers=headers, data=json.dumps(data))
        response_data = response.json()
        ret = response_data.get("ret", -1)
        msg = response_data.get("msg", "")
        data_illegal_status = -1  # 默认值为-1
        answer_text = ""

        try:
            data_illegal_status = int(response_data["data"]["illegal_status"])
            answer_text = response_data["data"]["answer_text"]
        except (KeyError, ValueError):
            pass
        print(response_data)
        sheet.append([query_text, ret, msg, data_illegal_status, answer_text])

workbook.save('/Users/<USER>/Downloads/pyshuju/gongxuliangsuzhong.xlsx')
