# 国内版本test环境批量获取结果
# kzy
# _*_ coding:utf-8 _*_
import requests
import json
import openpyxl
from tool.util import *
from tool.logger import log

output_file = "..{sep}document_file{sep}output{sep}Sensitive{sep}{D}{sep}".format(sep=os.sep,
                                                                                  D=get_time(format="%Y%m%d"))
if not os.path.exists(output_file):
    os.makedirs(output_file)
file_name = "check_sensitive_{T}.xlsx".format(T=get_time())

url = 'https://test-openapi-chatmax.orionstar.com/v1/ctai/query_text_chat'

headers = {
    'Content-Type': 'application/json',
    'orionstar-api-Key': 'bzbb81c2ec8a8b9b65362f7db9d2007afcz84fac6045c8e13df5ec23a10d6b1d1b1'
}

workbook = openpyxl.Workbook()
sheet = workbook.active
sheet.append(["Query Text", "Ret", "Msg", "Illegal Status", "Answer Text"])

with open('..{sep}document_file{sep}input{sep}sensitive{sep}sensitive.jsonl'.format(sep=os.sep), 'r',
          encoding='utf-8') as file:
    index = 0
    for line in file:
        query_text = line.strip()
        data = {
            "query_text": query_text,
            "session_id": "orinoutsid992708364b697328c3f3fa0ee1656f71",
            "stream": 0
        }
        log.info("执行的文件【{file_name}】,当前第{index}行，question是：{question}".format(file_name="sensitive.jsonl",
                                                                                        index=index + 1,
                                                                                        question=query_text))
        response = requests.post(url, headers=headers, data=json.dumps(data))
        response_data = response.json()
        log.info("执行的文件【{file_name}】response是：{res}".format(file_name="sensitive.jsonl", res=response_data))
        ret = response_data.get("ret", -1)
        msg = response_data.get("msg", "")
        data_illegal_status = -1  # 默认值为-1
        answer_text = ""
        try:
            data_illegal_status = int(response_data["data"]["illegal_status"])
            answer_text = response_data["data"]["answer_text"]
        except (KeyError, ValueError):
            pass
        # print(response_data)
        sheet.append([query_text, ret, msg, data_illegal_status, answer_text])

workbook.save(output_file + file_name)
time.sleep(5)
