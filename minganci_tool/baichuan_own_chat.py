# 百川自有训练模型批量获取问答结果，结果写入txt文件
# kzy
# _*_ coding:utf-8 _*_





import requests
import json

url = "http://*************:8007/v1/chat/completions"
file_path = r"/Users/<USER>/Downloads/pyshuju/budai111.json"

output_txt_path = "/Users/<USER>/Downloads/answer_shuruzhengchang.txt"

with open(file_path, "r", encoding="utf-8") as file, open(output_txt_path, "w", encoding="utf-8") as output_file:
    for line in file:
        input_content = line.strip()

        payload = {
            "model": "OrionStarSaviorBaichuan2",
            "stream": False,
            "messages": [
                {"role": "user", "content": input_content}
            ]
        }

        headers = {
            "Content-Type": "application/json"
        }

        response = requests.post(url, json=payload, headers=headers)

        response_data = response.json()
        content = response_data["choices"][0]["message"]["content"]

        output_file.write(content + "\n")

        print(response.text)

print(f"写入成功 {output_txt_path}")
