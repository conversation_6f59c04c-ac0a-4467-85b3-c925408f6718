# 是否命中敏感词—自研接口
# kzy
# _*_ coding:utf-8 _*_
import os.path

import requests
import json
import pandas as pd
from tool.util import get_time
from tool import util

output_file = "..{sep}document_file{sep}output{sep}Sensitive{sep}{D}{sep}".format(sep=os.sep,
                                                                                  D=get_time(format="%Y%m%d"))
file_name = "check_sensitive_{T}.xlsx".format(T=get_time())
#   志强本地提供的服务
url = "http://************:23320/query/sendetect"

if not os.path.exists(output_file):
    os.makedirs(output_file)

xls = util.open_xlsx("input{sep}Sensitive{sep}sensitive.xlsx".format(sep=os.sep), sheet_name="Sheet1")
queries = []
for row in xls.iterrows():
    queries.append(str(row[1].get("query_text")).strip())
#
# with open('/Users/<USER>/Downloads/pyshuju/minganciceshiji.json', 'r', encoding='utf-8') as file:
#     queries = [line.strip() for line in file]
#
results = []

for query in queries:
    body = json.dumps({
        "query": query,
        "reqid": ""
    })

    headers = {
        'Content-Type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=body)
    response_data = response.json()

    illegal_status = "通过" if response_data.get("data", {}).get("illegal_status", 0) == 0 else "输入命中敏感词"

    results.append([query, illegal_status, response_data.get("data", {}).get("illegal_status", 0)])

df = pd.DataFrame(results, columns=["Query", "Status", "Illegal Status"])
df.to_excel(output_file + file_name, index=False)
