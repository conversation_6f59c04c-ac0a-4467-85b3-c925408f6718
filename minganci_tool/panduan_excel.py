# 读取excel中某行的数据判断是否命中自研敏感词功能
# kzy
# _*_ coding:utf-8 _*_




import requests
import json
import pandas as pd

url = "http://10.60.17.239:23320/query/sendetect"

file_path = '/Users/<USER>/Downloads/ziyou.xlsx'
df = pd.read_excel(file_path)

results = []

for index, row in df.iterrows():
    query = row['Query']
    body = json.dumps({
        "query": query,
        "reqid": ""
    })

    headers = {
        'Content-Type': 'application/json'
    }

    response = requests.request("POST", url, headers=headers, data=body)
    response_data = response.json()

    illegal_status = "通过" if response_data.get("data", {}).get("illegal_status", 0) == 0 else "输出命中敏感词"

    results.append([query, illegal_status, response_data.get("data", {}).get("illegal_status", 0)])

result_df = pd.DataFrame(results, columns=["Query", "Status", "Illegal Status"])
output_excel_path = "/Users/<USER>/Downloads/answer.xlsx"
result_df.to_excel(output_excel_path, index=False)

print(f"写入成功 {output_excel_path}")
