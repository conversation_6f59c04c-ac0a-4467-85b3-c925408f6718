# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : answer_sensitive_check_online.py
<AUTHOR> <EMAIL>
@Time   : 2023-11-03 15:12:07
@Version: v1.0
"""
# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : generation_answer.py
<AUTHOR> <EMAIL>
@Time   : 2023-11-01 18:30:45
@Version: v1.0
"""
import time

import jsonlines
import requests
import json
import openpyxl
from tool.util import *
from tool.logger import log
from concurrent.futures import ThreadPoolExecutor, as_completed

ex = ThreadPoolExecutor(max_workers=5)

output_file = "..{sep}document_file{sep}output{sep}Sensitive{sep}{D}{sep}".format(D=get_time(format="%Y%m%d"),
                                                                                  sep=os.sep)
if not os.path.exists(output_file):
    os.makedirs(output_file)
file_name = "sensitive_gen_answer_online_{T}.xlsx".format(T=get_time())
check_illegal_file_name = "check_illegal_{}.xlsx".format(get_time())
url = 'https://api.chatmax.net/capi/v1/chatmax/query_text_chat'
headers = {
    'Content-Type': 'application/json',
    #   api-key 每次执行需要手动更新
    'Orionstar-Api-Key': 'c.DEMDXLgTh1cPiX7JgFBbUQ.LI_V-rgLnIFcybUKrnD9aS36z1rI_ataFD0NTRVcknH8iYqG8LWdG8KLE89UgVuXLQgc_DZjklEptBImFRwcpuAmBqQEs_cvLkOrdzmSPvRqu6_nEU3K1FWu2ajb82qmvmkhDaNS8mL8yizU2EoA1Pmb8CN7dPf5EUpAVpJEzDziWQUdPiE94toZ44RDhVqTTv1_60QJpafiPcnDDFpUXxHgcQkuNBxd1MpZQI8Cq_NXakaEnc9ResfUIKSsAgsW'
}
workbook = openpyxl.Workbook()
sheet = workbook.active
sheet.append(["ID", "Query Text", "Ret", "Msg", "Illegal Status", "Answer Text", "question_illegal", "answer_illegal"])


def request(args):
    query_text = str(args.get("line")).strip()
    data = {
        "query_text": query_text,
        # "character_id": "895a1599e20a8f10009fd753481a44c1",
        "session_id": "orinoutsid0bebdcf971e09765c{}".format(get_time()),
        "stream": 0,  # 0非流式 1流式
        "recommend": 0,
        "lang": "zh_CN",
        "category": "tpl_chat"
    }
    # log.info("执行的文件【{file_name}】,当前第{index}行，question是：{question}".format(file_name="sensitive.jsonl",
    #                                                                                 index=args.get("index"),
    #                                                                                 question=query_text))
    response = requests.post(url, headers=headers, data=json.dumps(data))
    response_data = response.json()
    # log.info("执行的文件【{file_name}】response是：{res}".format(file_name="sensitive.jsonl", res=response_data))
    ret = response_data.get("ret", -1)
    msg = response_data.get("msg", "")
    data_illegal_status = -1  # 默认值为-1
    answer_text = ""
    try:
        data_illegal_status = int(response_data["data"]["illegal_status"])
        answer_text = response_data["data"]["answer_text"]
    except (KeyError, ValueError):
        pass
    # print(response_data)
    #   如果模型给了answer，校验question和answer是否包含敏感词，正常能返回answer肯定question没有命中敏感词，但还是添加一层校验

    answer_illegal = ""
    question_word = ""
    # if ret == "0":
    #     answer_illegal = check_container_illegal(answer_text)
    # question_word = check_container_illegal(query_text)
    sheet.append(
        [args.get("ID", ""), query_text, ret, msg, data_illegal_status, answer_text, question_word, answer_illegal])
    # workbook.save(output_file + file_name)


def gen_answer():
    """
    根据question列表获取模型给的回答，并将回答记录在Excel表格，包含question、返回的code、Illegal Status、Answer Text
    :return:
    """
    future_list = []
    # with open('..{sep}document_file{sep}input{sep}sensitive{sep}y.txt', 'r', encoding='utf-8') as file:
    #     index = 0
    #     for line in file:
    #         index += 1
    #         agrs = {
    #             "line": line,
    #             # "sheet":sheet,
    #             # "sheet_2":sheet_2,
    #             "index": index
    #         }
    #         future = ex.submit(request, agrs)
    #         future_list.append(future)
    file_path = "/Users/<USER>/workspace/liangwei/source/jytest/data/财产隐私.xlsx".replace("/", os.sep)
    fs = open_xlsx_for_path(file_path)
    sheet_name_list = list(fs)  # 获取所有sheet_name
    sheet_index = 1  # 标记当前sheet索引
    sheet_index=0
    for sheet_name in sheet_name_list:
        if sheet_index>0:break
        index = 0
        for row in fs[sheet_name].iterrows():
            # if index >= 20: break
            index += 1
            agrs = {
                #   默认第一列就是question，且只有一列
                "line": row[1].get("query_text"),
                "ID": row[1].get("ID",""),
                "index": index
            }
            future = ex.submit(request, agrs)
            future_list.append(future)
            if index % 100 == 0:
                time.sleep(3)
        log.info("任务全部发出，执行中")
        sheet_index+=1
    tqdm_task = get_tqdm(iter_len=len(future_list))
    for future in as_completed(future_list):
        tqdm_task.update(1)
    tqdm_task.close()
    workbook.save(output_file + file_name)
    log.info("save result file to: {}".format(output_file + file_name))



gen_answer()
