#百川自有模型chat，提取答案
# kzy
# _*_ coding:utf-8 _*_

import json
import requests
import concurrent.futures
import openpyxl

workbook = openpyxl.Workbook()
sheet = workbook.active

sheet.append(["query_text", "response"])

def send_request(line):
    url = "http://*************:8007/v1/chat/completions"
    headers = {
        "Content-Type": "application/json"
    }

    data = {
        "model": "OrionStarSaviorBaichuan2",
        "stream": False,
        "messages": [
            {"role": "user", "content": line.strip()}
        ]
    }

    response = requests.post(url, json=data, headers=headers)

    response_json = response.json()
    choices = response_json.get('choices', [])

    # 提取choices.content
    content_list = []
    for choice in choices:
        content = choice.get('message', {}).get('content', '')
        content_list.append(content)  # 添加助手的回复内容

    print("\n".join(content_list))

    response_text = "\n".join(content_list)
    sheet.append([line.strip(), response_text])

def read_lines_and_send_requests(filename, num_concurrent_requests):
    with open(filename, 'r') as file:
        lines = file.readlines()

    with concurrent.futures.ThreadPoolExecutor(max_workers=num_concurrent_requests) as executor:
        futures = [executor.submit(send_request, line) for line in lines]

        for future in concurrent.futures.as_completed(futures):
            pass  

    workbook.save("/Users/<USER>/Downloads/ziyou.xlsx")

def main():
    filename = '/Users/<USER>/Downloads/pyshuju/minganci.json' 
    num_concurrent_requests = 1

    read_lines_and_send_requests(filename, num_concurrent_requests)

if __name__ == '__main__':
    main()
