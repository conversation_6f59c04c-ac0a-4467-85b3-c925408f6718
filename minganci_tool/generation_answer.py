# # -*- coding: utf-8 -*-
# """
# @Project: jytest
# @File   : generation_answer.py
# <AUTHOR> <EMAIL>
# @Time   : 2023-11-01 18:30:45
# @Version: v1.0
# """
# import time
#
# import jsonlines
# import requests
# import json
# import openpyxl
# from tool.util import *
# from tool.logger import log
# from concurrent.futures import ThreadPoolExecutor
#
# ex = ThreadPoolExecutor(max_workers=10)
#
# output_file = "..{sep}document_file{sep}output{sep}Sensitive{sep}{D}{sep}".format(sep=os.sep,
#                                                                                   D=get_time(format="%Y%m%d"))
# if not os.path.exists(output_file):
#     os.makedirs(output_file)
# file_name = "sensitive_gen_answer_{T}.xlsx".format(T=get_time())
# check_illegal_file_name = "check_illegal_input_close_{}.xlsx".format(get_time())
# #   测试环境的地址
# #   TODO 准备动态多去api_key
# headers = {
#     "Cookie": "__stripe_mid=dd71447a-06e0-415d-87db-6f450681d6e25fb873; Hm_lvt_1b25453009c89f4b416135caa352da85=1698646032; Hm_lpvt_1b25453009c89f4b416135caa352da85=1698646032; _gcl_au=1.1.1370205591.1698646032; _ga=GA1.1.439294535.1698646032; _ga_423T6BCTX3=GS1.1.1698646033.1.0.1698646033.0.0.0; _fbp=fb.1.1698646033078.322541166; _yjsu_yjad=1698646033.0ef09007-6668-4bca-89cb-d0805a739941; _ga_KPVNPZ0CT8=GS1.1.1698646032.1.0.1698646034.0.0.0; _ga_FT8PSNVF2S=GS1.1.1698646032.1.0.1698646034.0.0.0; supabase-auth-token=[%22eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOiJhdXRoZW50aWNhdGVkIiwiZXhwIjoxNjk5NDQzNjgwLCJzdWIiOiIzYzExNzJmNS1hNWM3LTQ0MjgtODkzYy0zODkyZDMxZTEwN2YiLCJlbWFpbCI6IjE4ODEzMDYxODU4QGp1eWFuLmNvbSIsInBob25lIjoiMTg4MTMwNjE4NTgiLCJhcHBfbWV0YWRhdGEiOnsicHJvdmlkZXIiOiJlbWFpbCIsInByb3ZpZGVycyI6WyJlbWFpbCIsInBob25lIl19LCJ1c2VyX21ldGFkYXRhIjp7ImFwcF9kZW1vX2d1aWRlIjoxLCJjb21wYW55Ijoi54yO5oi3Iiwiam9iIjoi5LiN5ZGK6K-J5L2gIiwic291cmNlIjoi5pyL5Y-L5o6o6I2QIiwidXNlcl9wcm9maWxlIjoidjIuMSIsInVzZXJfc3luY2VkIjp0cnVlLCJ3eF9pZCI6Im9rWFZsNnYwd3NjenBMYVphQTdaMjFYYk9uSGcifSwicm9sZSI6ImF1dGhlbnRpY2F0ZWQiLCJhYWwiOiJhYWwxIiwiYW1yIjpbeyJtZXRob2QiOiJwYXNzd29yZCIsInRpbWVzdGFtcCI6MTY5ODgwNzk1MH1dLCJzZXNzaW9uX2lkIjoiMGFjYmE3OTctNDBlMi00YTY5LTg1NmYtMTEwN2Y5OTAwYTJiIn0.LZ_b7a2Y6B2l3VEbl6KtZqsxgaBigLa5k707ITvZZEE%22%2C%22XNXAL4ZytbQw4N2y7-gu6A%22%2Cnull%2Cnull]"
# }
# url = 'https://test-api-chatmax.orionstar.com/capi/v1/chatmax/query_text_chat'
#
#
# def get_api_key():
#     key = requests.post(url="https://test-chatmax.orionstar.com/api/user-command/upload.getUrlAndKey", headers=headers)
#
#
# headers = {
#     'Content-Type': 'application/json',
#     #   api-key 每次执行需要手动更新
#     'Orionstar-Api-Key': 'c.pTdkszYi_vQEGd7FY7rv-g.ZsonhG5EBh9srr1rQBUqRlkekaE9gizNhVYhZWJNeBI_waYDspJmRLkxhzoQ-cIJaeStRnOWyBD7SoQN_sEUGW5T7-FRlDv5iV9-0aAOqfcOReCc70lsrDapXjNcOgNaYpWvV0J46MXXgY3pu2-LbPAPNBHY03bNbrBYiau8Sz8RIQu44SEKxSydr9oPtl6OeB4iQHlXH3WP_77RZqLRBDn4sN2K54xBm076atBqdffMFyURozB6T5reRcdbzbVP'
# }
# workbook = openpyxl.Workbook()
# sheet = workbook.active
# sheet.append(["Query Text", "Ret", "Msg", "Illegal Status", "Answer Text", "question_illegal", "answer_illegal"])
#
#
# def request(args):
#     query_text = args.get("line").strip()
#     data = {
#         "query_text": query_text,
#         "session_id": "orinoutsid44d9a28c22fd90d945cab390644c7600",
#         "stream": 0,  # 0非流式 1流式
#         "recommend": 0,
#         "lang": "zh_CN",
#         "category": "tpl_chat"
#     }
#     log.info("执行的文件【{file_name}】,当前第{index}行，question是：{question}".format(file_name="sensitive.jsonl",
#                                                                                     index=args.get("index"),
#                                                                                     question=query_text))
#     response = requests.post(url, headers=headers, data=json.dumps(data))
#     response_data = response.json()
#     log.info("执行的文件【{file_name}】response是：{res}".format(file_name="sensitive.jsonl", res=response_data))
#     ret = response_data.get("ret", -1)
#     msg = response_data.get("msg", "")
#     data_illegal_status = -1  # 默认值为-1
#     answer_text = ""
#     try:
#         data_illegal_status = int(response_data["data"]["illegal_status"])
#         answer_text = response_data["data"]["answer_text"]
#     except (KeyError, ValueError):
#         pass
#     # print(response_data)
#     #   如果模型给了answer，校验question和answer是否包含敏感词，正常能返回answer肯定question没有命中敏感词，但还是添加一层校验
#
#     answer_illegal = ""
#     if ret == "0":
#         answer_illegal = check_container_illegal(answer_text)
#     question_word = check_container_illegal(query_text)
#     sheet.append([query_text, ret, msg, data_illegal_status, answer_text, question_word, answer_illegal])
#     workbook.save(output_file + file_name)
#
#
# def gen_answer():
#     """
#     根据question列表获取模型给的回答，并将回答记录在Excel表格，包含question、返回的code、Illegal Status、Answer Text
#     :return:
#     """
#     with open('..{sep}document_file{sep}input{sep}sensitive{sep}y.txt'.format(sep=os.sep), 'r',
#               encoding='utf-8') as file:
#         index = 0
#         for line in file:
#             index += 1
#             agrs = {
#                 "line": line,
#                 # "sheet":sheet,
#                 # "sheet_2":sheet_2,
#                 "index": index
#             }
#             ex.submit(request, agrs)
#         log.info("任务全部发出")
#记录：   11.10号注释，使用services下的任务即可，有问题再修改