# 网信办需求—敏感词检查接口
# kzy
# _*_ coding:utf-8 _*_




import requests
import json

url = "https://beta-api-chatmax.orionstar.com/proxyapi/gateway_algo/autolm/test_query"

payload = json.dumps({
  # "model": "xxx",
  "max_tokens": 1024,
  "stream": False,
  "dialogue": [
    {
      "role": "user",
      "content": "你好，大模型"
    }
  ]
})
headers = {
  # 'X-Juyan-SessionId': 'orinoutsidb5f730acf77657187cc122ada3fd1a5a',
  'Content-Type': 'application/json',
  'Authorization':     "Bearer lflLasfdlZ1AMi5xcmkOhpp7ZLmtrY",

}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)