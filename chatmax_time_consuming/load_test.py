# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : load_test.py
<AUTHOR> <EMAIL>
@Time   : 2023-12-06 10:29:16
@Version: v1.0
"""
import os
import sys
try:
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    print(BASE_DIR)
    sys.path.append(BASE_DIR)
except Exception:
    raise BaseException("添加当前目录到path失败")
from tool.logger import log
import requests
import json
import time
import concurrent.futures
from datetime import datetime
import pandas as pd
from test_conifg.path_config import PROMPT_JSON, MODEL_OUTPUT_PATH, REQUEST_DATA
from tool.util import get_time, read_xlsx_to_dict, flush_key
import uuid
from tool.common import load_yml
import copy

"""
流式
测试环境调用聊天窗口问答，查看性能情况

"""

req_data = load_yml(REQUEST_DATA)["gen_answer"]

#   哪个入口
service = "juyan"
#   线程数和循环次数
task_list = [{"thread": 1, "loop": 50}]
file_path = "{}{}baidu.json".format(PROMPT_JSON, os.sep)  # 设置JSON文件路径（读取json，本地文件路径）


def send_stream(qa_prompt="车脏了怎么洗?", key="", env="test"):
    """
    流式请求
    :param qa_prompt:
    :param key:
    :param env:
    :return:
    """

    data = {"character_id": "8c87bfa242f530076ff97dd37293cf07", "query_text": qa_prompt, "session_id": "", "stream": 1,
            "recommend": 0, "lang": "zh_CN"}
    url = req_data[env]["url"]
    orion_data = req_data[env]["payload"]
    data = copy.deepcopy(orion_data)
    data["stream"] = 1
    data["query_text"] = qa_prompt
    data["session_id"] = "lw" + str(uuid.uuid4()).replace("-", "")
    headers = req_data["headers"]
    headers["Orionstar-Api-Key"] = key
    # 记录请求发送的时间（精确到毫秒）
    request_send_time = time.time()
    response = requests.post(url, headers=headers, json=data, stream=True)
    first_packet_time = None
    total_character_count = 0
    answer = ""
    # 处理响应数据
    error = 0
    for line in response.iter_lines():
        if not first_packet_time:
            # 记录第一个数据包返回的时间（精确到毫秒）
            first_packet_time = time.time()
        # 处理响应数据
        if line.startswith(b"data: "):
            json_data = line[6:]
            try:
                data_dict = json.loads(json_data)
            except json.JSONDecodeError:
                log.error("json loads error:->{}".format(line))
                continue
            if 'data' in data_dict:
                data = data_dict['data']
                if "answer_text" in data:
                    content = data["answer_text"]
                    # print(content)
                    content_length = len(content)
                    total_character_count += content_length
                    answer += content
                else:
                    log.error("response not answer_text:->{}".format(line))
            else:
                log.error("response not data:->{}".format(line))
    # 等待整个请求完全返回
    response.close()
    # 记录请求完成的时间（精确到毫秒）
    request_complete_time = time.time()
    # 转换为易读的时分秒格式（精确到毫秒）并保留毫秒的前3位
    request_send_time_str = datetime.fromtimestamp(request_send_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    first_packet_time_str = datetime.fromtimestamp(first_packet_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    request_complete_time_str = datetime.fromtimestamp(request_complete_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    # 计算并打印首包耗时和总耗时
    first_packet_duration = (first_packet_time - request_send_time)
    total_duration = request_complete_time - request_send_time
    tokens_time = (request_complete_time - first_packet_time)
    # 计算性能指标
    performance_metric = (total_character_count) / tokens_time
    # print("性能指标: {:.3f} 字/秒".format(performance_metric))
    # print("=" * 50)
    result = {
        "请求发送的时间": request_send_time_str,
        "第一个数据包返回的时间": first_packet_time_str,
        "请求完成的时间": request_complete_time_str,
        "首包耗时(s)": first_packet_duration,
        "总耗时(s)": total_duration,
        "输入": len(qa_prompt),
        "输出字数": total_character_count,
        "性能指标(字/秒)": performance_metric,
        "answer": answer,
        # "query": qa_prompt,
        "session_id": data.get("session_id")
    }
    return result


def save_print_to_excel(thread, loop, env="test"):
    results = []
    output_path = "{prefix_path}{sep}{service}{sep}{T}".format(prefix_path=MODEL_OUTPUT_PATH, sep=os.sep,
                                                               T=get_time(format="%Y%m%d"), service=service)
    output_file = f"Tread_{thread}_loop_{loop}_{get_time()}{service}.xlsx"

    excel_path = f"{output_path}{os.sep}{output_file}"
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    with concurrent.futures.ThreadPoolExecutor(max_workers=thread) as executor:
        futures = []
        index = 0
        question_list = read_xlsx_to_dict("question.xlsx")
        key = flush_key()
        for question in question_list:
            # if index > 20: break
            if index % 100 == 0:
                key = flush_key()
            index += 1
            futures.append(executor.submit(send_stream, question.get("question"), key=key, env=env))
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as exc:
                print(f"Error: {exc}")
    df = pd.DataFrame(results)
    df.to_excel(excel_path, index=False)
    print("save result file to: {}".format(output_path))
    return results


if __name__ == "__main__":
    env = "test"
    for task in task_list:
        thread = task.get("thread")
        loop = task.get("loop")
        print("thread:{}\nloop:{}".format(thread, loop))
        save_print_to_excel(thread, loop, env=env)
