#测试飞书消息是否能发送成功
#kzy
# _*_ coding:utf-8 _*_
import requests
import json

class FeishuTalk:
    # 机器人webhook
    chatGPT_url = 'https://open.feishu.cn/open-apis/bot/v2/hook/398d5f31-b358-40a3-af5f-31740f70367c'

    # 发送文本消息
    def sendTextmessage(self, content):
        url = self.chatGPT_url
        headers = {
            "Content-Type": "application/json; charset=utf-8",
        }
        payload_message = {
            "msg_type": "text",
            "content": {
                # @ 单个用户 <at user_id="ou_xxx">名字</at>
                # "text": content + "<at user_id=\"bf888888\">test</at>"
                # @ 所有人 <at user_id="all">所有人</at>
                "text": content + "<at user_id=\"all\">test</at>"
            }
        }
        response = requests.post(url=url, data=json.dumps(payload_message), headers=headers)
        return response.json

    # 执行发送文本消息


content = "测试消息"
FeishuTalk().sendTextmessage(content)