#chatmax生产环境首包耗时
#kzy
# _*_ coding:utf-8 _*_
import requests
import concurrent.futures
import pandas as pd
import time
import os
import shutil
import datetime

url_list = [
    'https://us-ga-jiedai.orionstar.com/capi/v1/chatmax/query_text_chat',
    'https://us-ga-jiedai.orionstar.com/capi/v1/chatmax/query_text_chat',
    'https://us-ga-jiedai.orionstar.com/capi/v1/chatmax/query_text_chat',
    'https://us-ga-jiedai.orionstar.com/capi/v1/chatmax/query_text_chat',
    'https://us-ga-jiedai.orionstar.com/capi/v1/chatmax/query_text_chat',
    'https://us-ga-jiedai.orionstar.com/capi/v1/chatmax/query_text_chat',
    'https://us-ga-jiedai.orionstar.com/capi/v1/chatmax/query_text_chat',
    'https://us-ga-jiedai.orionstar.com/capi/v1/chatmax/query_text_chat',
    'https://us-ga-jiedai.orionstar.com/capi/v1/chatmax/query_text_chat',
    'https://us-ga-jiedai.orionstar.com/capi/v1/chatmax/query_text_chat'
]

headers = {
    'authority': 'us-ga-jiedai.orionstar.com',
    'accept': 'text/event-stream',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    'content-type': 'application/json',
    'origin': 'https://chatmax.ai',
    'referer': 'https://chatmax.ai/',
    'sec-ch-ua': '"Microsoft Edge";v="113", "Chromium";v="113", "Not-A.Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'cross-site',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36 Edg/113.0.1774.57',
    'Connection': 'close'  # 添加Connection: close
}

data_list = [
    {
    "ctshared_id": "b24a146a6ae26a889a997f5d6d36dadd",
     "session_id": "sessionId1685955079363",
     "query_text": "苹果公司CEO Tim Cook讨论了什么？",
     "pad_debug_info": 0,
     "stream": 1,
     "shared_type": "link"
    },
    {
    "ctshared_id": "b24a146a6ae26a889a997f5d6d36dadd",
     "session_id": "sessionId1685955079363",
     "query_text": "苹果公司2023年第一季度收入是多少？",
     "pad_debug_info": 0,
     "stream": 1,
     "shared_type": "link"
    },
    {
    "ctshared_id": "b24a146a6ae26a889a997f5d6d36dadd",
     "session_id": "sessionId1685955079363",
     "query_text": " 苹果公司2023年第一季度收入是多少？",
     "pad_debug_info": 0,
     "stream": 1,
     "shared_type": "link"
    },
    {
    "ctshared_id": "b24a146a6ae26a889a997f5d6d36dadd",
     "session_id": "sessionId1685955079363",
     "query_text": " 为什么苹果公司2023年第一季度收入同比下降？",
     "pad_debug_info": 0,
     "stream": 1,
     "shared_type": "link"
     },
    {
        "ctshared_id": "b24a146a6ae26a889a997f5d6d36dadd",
        "session_id": "sessionId1685955079363",
        "query_text": "苹果公司财务健康吗？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },    {
        "ctshared_id": "b24a146a6ae26a889a997f5d6d36dadd",
        "session_id": "sessionId1685955079363",
        "query_text": "苹果公司都有谁参与了？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        "ctshared_id": "b24a146a6ae26a889a997f5d6d36dadd",
        "session_id": "sessionId1685955079363",
        "query_text": "苹果公司主营业务？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        "ctshared_id": "b24a146a6ae26a889a997f5d6d36dadd",
        "session_id": "sessionId1685955079363",
        "query_text": "苹果公司哪些业务挣钱了？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        "ctshared_id": "b24a146a6ae26a889a997f5d6d36dadd",
        "session_id": "sessionId1685955079363",
        "query_text": "苹果公司目标是什么？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        "ctshared_id": "b24a146a6ae26a889a997f5d6d36dadd",
        "session_id": "sessionId1685955079363",
        "query_text": "库克在苹果公司？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        "ctshared_id": "b24a146a6ae26a889a997f5d6d36dadd",
        "session_id": "sessionId1685955079363",
        "query_text": "苹果公司Tim Cook说了些什么？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    }
]

result_list = []

def send_request(url, headers, data):
    # 发送请求并获取响应
    response = requests.post(url, headers=headers, json=data, stream=True)

    # 遍历响应的每一行
    for i, line in enumerate(response.iter_lines()):
        # 只处理第二行，因为第一行是空行
        if i == 1:
            # 将响应内容和响应时间保存到结果列表中
            result = {
                'response_content': line.decode(),
                'response_time': response.elapsed.total_seconds()
            }
            result_list.append(result)
            break

# 创建一个线程池，最大线程数为x
with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
    # 循环x次，每次循环都遍历一遍URL列表
    for i in range(1):
        for j in range(len(url_list)):
            # 提交任务到线程池中，每个任务都调用send_request函数
            executor.submit(send_request, url_list[j], headers, data_list[j])

            # 打印当前任务的进度
            print(f"Submitted request {url_list[j]}")

            # 增加适当的延迟，以免发送请求过快导致被服务器拒绝
            time.sleep(0.5)

    # 关闭线程池
    executor.shutdown(wait=True)

# 定义目标文件夹路径
destination_folder = '/Users/<USER>/Downloads/nob/produce_service_first_pack_time'

# 获取当前时间的实际日期
current_date = datetime.datetime.now().strftime("%m月%d日")

# 将时间戳转换为字符串，并作为文件名的一部分
filename = f'chatmax_msg_produce_{current_date}.xlsx'

# 创建完整的目标文件路径
destination_path = os.path.join(destination_folder, filename)

# 将结果列表转换为DataFrame，并保存到Excel文件中
df = pd.DataFrame(result_list)
df.to_excel(destination_path, index=False)

# 打印执行完成的消息
print(f"Execution complete. Results saved to {destination_path}")


