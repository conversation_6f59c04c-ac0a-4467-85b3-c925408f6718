#chatmax测试环境首包耗时
#kzy
# _*_ coding:utf-8 _*_
import requests
import concurrent.futures
import pandas as pd

url_list = [
    'https://dev-jiedai.ainirobot.com/capi/v1/chatmax/query_text_chat',
    'https://dev-jiedai.ainirobot.com/capi/v1/chatmax/query_text_chat',
    'https://dev-jiedai.ainirobot.com/capi/v1/chatmax/query_text_chat',
    'https://dev-jiedai.ainirobot.com/capi/v1/chatmax/query_text_chat',
    'https://dev-jiedai.ainirobot.com/capi/v1/chatmax/query_text_chat',
    'https://dev-jiedai.ainirobot.com/capi/v1/chatmax/query_text_chat',
    'https://dev-jiedai.ainirobot.com/capi/v1/chatmax/query_text_chat',
    'https://dev-jiedai.ainirobot.com/capi/v1/chatmax/query_text_chat',
    'https://dev-jiedai.ainirobot.com/capi/v1/chatmax/query_text_chat',
    'https://dev-jiedai.ainirobot.com/capi/v1/chatmax/query_text_chat'
]

headers = {
    'authority': 'dev-jiedai.ainirobot.com',
    'accept': 'text/event-stream',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    'content-type': 'application/json',
    'origin': 'https://chatmax-official-git-dev-orionstargit-s-team.vercel.app',
    'referer': 'https://chatmax-official-git-dev-orionstargit-s-team.vercel.app/',
    'sec-ch-ua': '"Microsoft Edge";v="113", "Chromium";v="113", "Not-A.Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'cross-site',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36 Edg/113.0.1774.57',
    'Connection': 'close'  # 添加Connection: close
}

data_list = [
    {
        'ctshared_id': 'aaf0527516067172dae3ef81d1d91620',
        'session_id': 'sessionId1686988046176',
        'query_text': '本文介绍了哪些操作流程和规定？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'aaf0527516067172dae3ef81d1d91620',
        'session_id': 'sessionId1685684249186',
        'query_text': '猎豹移动是什么公司？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'aaf0527516067172dae3ef81d1d91620',
        'session_id': 'sessionId1686988046176',
        'query_text': '员工请病假需要提供什么证明？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'aaf0527516067172dae3ef81d1d91620',
        'session_id': 'sessionId1686988046176',
        'query_text': '如何解决海外网站无法打开的问题？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'aaf0527516067172dae3ef81d1d91620',
        'session_id': 'sessionId1686988046176',
        'query_text': '猎户星空的自研产品有哪些？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'aaf0527516067172dae3ef81d1d91620',
        'session_id': 'sessionId1686988046176',
        'query_text': '猎豹移动是什么公司？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'aaf0527516067172dae3ef81d1d91620',
        'session_id': 'sessionId1686988046176',
        'query_text': '如何在飞书中访问团队宣传栏？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'aaf0527516067172dae3ef81d1d91620',
        'session_id': 'sessionId1686988046176',
        'query_text': '北京有哪些A类医院？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'aaf0527516067172dae3ef81d1d91620',
        'session_id': 'sessionId1686988046176',
        'query_text': '伊莎打印机连接识别需要注意什么？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'aaf0527516067172dae3ef81d1d91620',
        'session_id': 'sessionId1686988046176',
        'query_text': '猎户星空荣获了哪些荣誉？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    }
]

result_list = []  #存储数据

def send_request(url, headers, data):
    # 发送HTTP POST请求并处理响应数据
    response = requests.post(url, headers=headers, json=data, stream=True)

    for i, line in enumerate(response.iter_lines()):  # 逐行处理响应内容
        if i == 1:  # 仅处理第二行的数据，忽略其他行
            result = {
                # 'url': url,
                'response_content': line.decode(),  # 获取第二行响应内容并解码为字符串
                'response_time': response.elapsed.total_seconds()  # 记录请求响应时间
            }
            result_list.append(result)  # 将结果添加到结果列表中
            break  # 由于只处理第二行数据，处理后结束循环

# 使用线程池执行并发请求
with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:  # 创建线程池执行器，最大线程数为1
    for i in range(2):  # 重复执行2次，模拟2个并发请求的情况
        for j in range(len(url_list)):  # 遍历url_list中的URL列表
            executor.submit(send_request, url_list[j], headers, data_list[j])

# 将结果列表转换为DataFrame，并保存为Excel文件
df = pd.DataFrame(result_list)
df.to_excel('/Users/<USER>/Downloads/msg_test.xlsx', index=False)
