import requests
import json
import re
import datetime


class FeishuTalk:
    # webhook URL
    webhook_url = 'https://open.feishu.cn/open-apis/bot/v2/hook/662572b9-85eb-4b48-8d62-9974081acb8c'

    def __init__(self):
        self.result_list = []
        self.response_times = []

    def execute_requests_and_send_message(self, url_list, headers, data_list, title, min_response_time=0,
                                          max_response_time=None):
        response_messages = []

        for idx, url in enumerate(url_list):
            response = requests.post(url=url, headers=headers, json=data_list[idx], stream=True)
            response_content = response.content.decode()
            response_time = response.elapsed.total_seconds()
            data_sid = self.extract_data_sid(response_content)

            # 构建单条响应消息
            response_message = f"Data.sid: {data_sid}" \
                               f"耗时: {round(response_time, 2)} seconds\n"

            if (min_response_time is None or response_time >= min_response_time) and \
                    (max_response_time is None or response_time <= max_response_time):
                response_messages.append(response_message)

        return response_messages

    def extract_data_sid(self, response_content):
        data_sid = None
        pattern = r'"sid":\s*"([^"]*)"'
        match = re.search(pattern, response_content)
        if match:
            data_sid = match.group(1)
        else:
            print(f"Failed to extract data.sid from response content: {response_content}")
        return data_sid

    def send_text_message(self, content):
        url = self.webhook_url
        headers = {
            "Content-Type": "application/json; charset=utf-8",
        }
        payload_message = {
            "msg_type": "post",
            "content": {
                "post": {
                    "zh_cn": {
                        "title": "首包耗时监控",
                        "content": [
                            [
                                {
                                    "tag": "text",
                                    "text": content
                                }
                            ]
                        ]
                    }
                }
            }
        }
        response = requests.post(url=url, data=json.dumps(payload_message), headers=headers)
        return response.json()


# 国际chatmax配置
international_url_list = [
    'https://api-ga.chatmax.ai/capi/v1/chatmax/query_text_chat',
    'https://api-ga.chatmax.ai/capi/v1/chatmax/query_text_chat',
    'https://api-ga.chatmax.ai/capi/v1/chatmax/query_text_chat',
    'https://api-ga.chatmax.ai/capi/v1/chatmax/query_text_chat',
    'https://api-ga.chatmax.ai/capi/v1/chatmax/query_text_chat',
    'https://api-ga.chatmax.ai/capi/v1/chatmax/query_text_chat',
    'https://api-ga.chatmax.ai/capi/v1/chatmax/query_text_chat',
    'https://api-ga.chatmax.ai/capi/v1/chatmax/query_text_chat',
    'https://api-ga.chatmax.ai/capi/v1/chatmax/query_text_chat',
    'https://api-ga.chatmax.ai/capi/v1/chatmax/query_text_chat',
]

international_headers = {
    'authority': 'api-ga.chatmax.ai',
    'accept': 'text/event-stream',
    'accept-language': 'zh-CN,zh;q=0.9',
    'content-type': 'application/json',
    'origin': 'https://chatmax.ai',
    'referer': 'https://chatmax.ai/',
    'sec-ch-ua': '"Chromium";v="116", "Not)A;Brand";v="24", "Google Chrome";v="116"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36'
}

international_data_list = [
    {
        'ctshared_id': '94f01100466f618ac3723f040a4ff1b9',
        'session_id': 'sessionId1694147428885',
        "query_text": "访客Wifi是哪个？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        'ctshared_id': '94f01100466f618ac3723f040a4ff1b9',
        'session_id': 'sessionId1694147428885',
        "query_text": "豹小秘的RAM？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        'ctshared_id': '94f01100466f618ac3723f040a4ff1b9',
        'session_id': 'sessionId1694147428885',
        "query_text": " 民事法律行为的效力有哪些附条件和附期限？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        'ctshared_id': '94f01100466f618ac3723f040a4ff1b9',
        'session_id': 'sessionId1694147428885',
        "query_text": " 监护人被指定后是否允许擅自变更？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        'ctshared_id': '94f01100466f618ac3723f040a4ff1b9',
        'session_id': 'sessionId1694147428885',
        "query_text": "未成年人的监护人是谁？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        'ctshared_id': '94f01100466f618ac3723f040a4ff1b9',
        'session_id': 'sessionId1694147428885',
        "query_text": "自然人的民事行为能力评估与年龄的关系是怎样的？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        'ctshared_id': '94f01100466f618ac3723f040a4ff1b9',
        'session_id': 'sessionId1694147428885',
        "query_text": "保管期内，无偿保管人由于个人原因造成保管物毁损的需要承担赔偿责任？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        'ctshared_id': '94f01100466f618ac3723f040a4ff1b9',
        'session_id': 'sessionId1694147428885',
        "query_text": "江东林为什么要学英语？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        'ctshared_id': '94f01100466f618ac3723f040a4ff1b9',
        'session_id': 'sessionId1694147428885',
        "query_text": "江碧峰是“木偶之父”吗？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        'ctshared_id': '94f01100466f618ac3723f040a4ff1b9',
        'session_id': 'sessionId1694147428885',
        "query_text": "社保卡挂失后如何恢复卡的正常使用？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        'ctshared_id': '94f01100466f618ac3723f040a4ff1b9',
        'session_id': 'sessionId1694147428885',
        "query_text": "陈醋的加工工艺是什么？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    }
]

# 国内chatmax快速模式
domestic_url_list = [
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
]

domestic_headers = {
    'authority': 'api.chatmax.net',
    'accept': 'text/event-stream',
    'accept-language': 'zh-CN,zh;q=0.9',
    'content-type': 'application/json',
    'origin': 'https://chatmax.net',
    'referer': 'https://chatmax.net/',
    'sec-ch-ua': '"Not/A)Brand";v="99", "Google Chrome";v="115", "Chromium";v="115"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': 'macOS',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
}

domestic_data_list = [
    {
        "ctshared_id": "4f96098918bf6903e35aedd78dba266b",
        "session_id": "orinoutsid0ecd034498ad7fab71188da64d576fc2",
        "query_text": "省社保卡系统如何核查申请人的基本信息？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        "ctshared_id": "4f96098918bf6903e35aedd78dba266b",
        "session_id": "orinoutsid0ecd034498ad7fab71188da64d576fc2",
        "query_text": "这份文件包含了哪些方面的内容？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        "ctshared_id": "4f96098918bf6903e35aedd78dba266b",
        "session_id": "orinoutsid0ecd034498ad7fab71188da64d576fc2",
        "query_text": "登记机构应当履行哪些职责？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        "ctshared_id": "4f96098918bf6903e35aedd78dba266b",
        "session_id": "orinoutsid0ecd034498ad7fab71188da64d576fc2",
        "query_text": "不动产登记需要哪些必要材料？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        "ctshared_id": "4f96098918bf6903e35aedd78dba266b",
        "session_id": "orinoutsid0ecd034498ad7fab71188da64d576fc2",
        "query_text": "游客可以祭拜哪些窑神？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        "ctshared_id": "4f96098918bf6903e35aedd78dba266b",
        "session_id": "orinoutsid0ecd034498ad7fab71188da64d576fc2",
        "query_text": "豹小秘的屏幕尺寸是多少？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        "ctshared_id": "4f96098918bf6903e35aedd78dba266b",
        "session_id": "orinoutsid0ecd034498ad7fab71188da64d576fc2",
        "query_text": "预制卡的制作流程包括哪些步骤？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        "ctshared_id": "4f96098918bf6903e35aedd78dba266b",
        "session_id": "orinoutsid0ecd034498ad7fab71188da64d576fc2",
        "query_text": "在职工作3年，年假有多少天？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        "ctshared_id": "4f96098918bf6903e35aedd78dba266b",
        "session_id": "orinoutsid0ecd034498ad7fab71188da64d576fc2",
        "query_text": "EO是什么？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        "ctshared_id": "4f96098918bf6903e35aedd78dba266b",
        "session_id": "orinoutsid0ecd034498ad7fab71188da64d576fc2",
        "query_text": "豹小秘的摄像头规格是什么？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    },
    {
        "ctshared_id": "4f96098918bf6903e35aedd78dba266b",
        "session_id": "orinoutsid0ecd034498ad7fab71188da64d576fc2",
        "query_text": "你知道傅盛吗？",
        "pad_debug_info": 0,
        "stream": 1,
        "shared_type": "link"
    }
]

# 国内chatmax精准模式
accurate_url_list = [
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat',
    'https://api.chatmax.net/capi/v1/chatmax/query_text_chat'
]

accurate_headers = {
    'authority': 'api.chatmax.net',
    'accept': 'text/event-stream',
    'accept-language': 'zh-CN,zh;q=0.9',
    'content-type': 'application/json',
    'origin': 'https://chatmax.net',
    'referer': 'https://chatmax.net/',
    'sec-ch-ua': '"Not/A)Brand";v="99", "Google Chrome";v="115", "Chromium";v="115"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': 'macOS',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-site',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
}

accurate_data_list = [
    {
        'ctshared_id': 'd07482862a11b74b02488604de73797f',
        'session_id': 'orinoutsidfb0a46a1ec56fb2a64da740e691a16b8',
        'query_text': '喇叭不响有哪些原因？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'd07482862a11b74b02488604de73797f',
        'session_id': 'orinoutsidfb0a46a1ec56fb2a64da740e691a16b8',
        'query_text': '泄露电流的限值是多少？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'd07482862a11b74b02488604de73797f',
        'session_id': 'orinoutsidfb0a46a1ec56fb2a64da740e691a16b8',
        'query_text': '如何进行粒子操作？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'd07482862a11b74b02488604de73797f',
        'session_id': 'orinoutsidfb0a46a1ec56fb2a64da740e691a16b8',
        'query_text': '推荐一下手工制瓷技艺的老艺人',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'd07482862a11b74b02488604de73797f',
        'session_id': 'orinoutsidfb0a46a1ec56fb2a64da740e691a16b8',
        'query_text': '介绍一下手工制瓷作坊',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'd07482862a11b74b02488604de73797f',
        'session_id': 'orinoutsidfb0a46a1ec56fb2a64da740e691a16b8',
        'query_text': '推荐一个可以现场体验的项目',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'd07482862a11b74b02488604de73797f',
        'session_id': 'orinoutsidfb0a46a1ec56fb2a64da740e691a16b8',
        'query_text': '山西老陈醋的原料有哪些？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'd07482862a11b74b02488604de73797f',
        'session_id': 'orinoutsidfb0a46a1ec56fb2a64da740e691a16b8',
        'query_text': '社保卡支持异地挂失吗？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'd07482862a11b74b02488604de73797f',
        'session_id': 'orinoutsidfb0a46a1ec56fb2a64da740e691a16b8',
        'query_text': '什么是木偶戏？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'd07482862a11b74b02488604de73797f',
        'session_id': 'orinoutsidfb0a46a1ec56fb2a64da740e691a16b8',
        'query_text': '如何管理失踪人的财产？',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    }
]


def main():
    INTERNATIONAL_MIN_RESPONSE_TIME = 3
    DOMESTIC_FAST_MIN_RESPONSE_TIME = 2
    DOMESTIC_ACCURATE_MIN_RESPONSE_TIME = 10

    feishu_talk_international = FeishuTalk()
    feishu_talk_domestic_fast = FeishuTalk()
    feishu_talk_domestic_accurate = FeishuTalk()

    international_response_messages = feishu_talk_international.execute_requests_and_send_message(
        international_url_list, international_headers, international_data_list,
        "国际chatmax（请求10次，每次首包大于3S的发送出来）",
        min_response_time=INTERNATIONAL_MIN_RESPONSE_TIME, max_response_time=None)

    domestic_fast_response_messages = feishu_talk_domestic_fast.execute_requests_and_send_message(
        domestic_url_list, domestic_headers, domestic_data_list,
        "国内chatmax快速模式（请求10次，每次首包大于2S的发送出来）",
        min_response_time=DOMESTIC_FAST_MIN_RESPONSE_TIME, max_response_time=None)

    domestic_accurate_response_messages = feishu_talk_domestic_accurate.execute_requests_and_send_message(
        accurate_url_list, accurate_headers, accurate_data_list,
        "国内chatmax精准模式（请求10次，每次首包大于10S的发送出来）",
        min_response_time=DOMESTIC_ACCURATE_MIN_RESPONSE_TIME, max_response_time=None)

    international_exceptions = sum(1 for msg in international_response_messages if
                                   float(re.search(r'耗时: ([\d.]+) seconds', msg).group(1)) > INTERNATIONAL_MIN_RESPONSE_TIME)

    domestic_fast_exceptions = sum(1 for msg in domestic_fast_response_messages if
                                   float(re.search(r'耗时: ([\d.]+) seconds', msg).group(1)) > DOMESTIC_FAST_MIN_RESPONSE_TIME)

    domestic_accurate_exceptions = sum(1 for msg in domestic_accurate_response_messages if
                                       float(re.search(r'耗时: ([\d.]+) seconds', msg).group(1)) > DOMESTIC_ACCURATE_MIN_RESPONSE_TIME)

    exception_count = international_exceptions + domestic_fast_exceptions + domestic_accurate_exceptions

    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    exception_details = f"""本次监控出现{exception_count}条异常，当前时间：{current_time}，异常详情如下："""

    consolidated_message_parts = []

    if international_exceptions > 0:
        consolidated_message_parts.append(
            "国际版：\n" + ("\n".join(international_response_messages)))

    if domestic_fast_exceptions > 0:
        consolidated_message_parts.append(
            "国内版快速模式：\n" + ("\n".join(domestic_fast_response_messages)))

    if domestic_accurate_exceptions > 0:
        consolidated_message_parts.append(
            "国内版精准模式：\n" + ("\n".join(domestic_accurate_response_messages)))

    consolidated_message = "\n\n".join(consolidated_message_parts)

    full_message = f"{exception_details}\n\n{consolidated_message}\n\n原理：首包耗时监控对线上各环境进行问答请求，每次串行发送10条，检查每条请求流式返回首包的耗时情况，如有超出标准视为异常\n标准：国际版不大于3秒，国内版快速模式不大于2秒，国内版精准模式不大于10秒"

    if exception_count == 0:
        success_message = f"太棒啦，本次首包耗时都符合预期啦！\n当前时间：{current_time}"
        feishu_talk_international.send_text_message(success_message)
    else:
        feishu_talk_international.send_text_message(full_message)


if __name__ == "__main__":
    main()
