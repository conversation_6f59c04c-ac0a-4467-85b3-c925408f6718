#德生私有化首包耗时
#kzy
# _*_ coding:utf-8 _*_
import requests
import concurrent.futures
import pandas as pd
import datetime
import concurrent.futures
import time


url_list = [
    'http://api.chatmax-self-hosting.ai/capi/v1/chatmax/query_text_chat',
    'http://api.chatmax-self-hosting.ai/capi/v1/chatmax/query_text_chat',
    'http://api.chatmax-self-hosting.ai/capi/v1/chatmax/query_text_chat',
    'http://api.chatmax-self-hosting.ai/capi/v1/chatmax/query_text_chat',
    'http://api.chatmax-self-hosting.ai/capi/v1/chatmax/query_text_chat',
    'http://api.chatmax-self-hosting.ai/capi/v1/chatmax/query_text_chat',
    'http://api.chatmax-self-hosting.ai/capi/v1/chatmax/query_text_chat',
    'http://api.chatmax-self-hosting.ai/capi/v1/chatmax/query_text_chat',
    'http://api.chatmax-self-hosting.ai/capi/v1/chatmax/query_text_chat',
    'http://api.chatmax-self-hosting.ai/capi/v1/chatmax/query_text_chat'
]

headers = {
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Connection': 'keep-alive',
    'Content-Type': 'application/json',
    'Origin': 'http://*************:3003',
    'Referer': 'http://*************:3003/',
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'accept': 'text/event-stream',
    'Connection': 'close'  # 添加Connection: close
}

data_list = [
    {
        'ctshared_id': 'ab669b4e843dc67d082aaf581549a856',
        'session_id': 'sessionId1687873094376',
        'query_text': '大清覆亡，失去江山，成为必然',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'ab669b4e843dc67d082aaf581549a856',
        'session_id': 'sessionId1687873094376',
        'query_text': '总的来说，清朝覆亡的原因复杂多样?',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'ab669b4e843dc67d082aaf581549a856',
        'session_id': 'sessionId1687873094376',
        'query_text': '政治腐败和道德沦丧等问题是导致清朝覆亡的主要原因?',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'ab669b4e843dc67d082aaf581549a856',
        'session_id': 'sessionId1687873094376',
        'query_text': '首先，清朝的统治集团内部存在严重的分歧和矛盾?',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'ab669b4e843dc67d082aaf581549a856',
        'session_id': 'sessionId1687873094376',
        'query_text': '有一个重要的转折点，那就是宣统帝的逊位?',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'ab669b4e843dc67d082aaf581549a856',
        'session_id': 'sessionId1687873094376',
        'query_text': '逊位后，清朝的统治权被冯玉祥等人夺取?',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'ab669b4e843dc67d082aaf581549a856',
        'session_id': 'sessionId1687873094376',
        'query_text': '溥仪出宫，回到醇亲王北府今宋庆龄故居?',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'ab669b4e843dc67d082aaf581549a856',
        'session_id': 'sessionId1687873094376',
        'query_text': '严重的政治腐败和道德沦丧问题，导致社会风气日趋败坏?',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'ab669b4e843dc67d082aaf581549a856',
        'session_id': 'sessionId1687873094376',
        'query_text': '清朝的统治权被冯玉祥等人夺取，溥仪出宫?',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    },
    {
        'ctshared_id': 'ab669b4e843dc67d082aaf581549a856',
        'session_id': 'sessionId1687873094376',
        'query_text': '清朝覆亡的原因，可以从多个方面来分析?',
        'pad_debug_info': 0,
        'stream': 1,
        'shared_type': 'link'
    }
]

result_list = []

# 定义发送请求的函数
def send_request(url, headers, data):
    # 发送请求并获取响应
    response = requests.post(url, headers=headers, json=data, stream=True)

    # 遍历响应的每一行
    for i, line in enumerate(response.iter_lines()):
        # 只处理第二行，因为第一行是空行
        if i == 1:
            # 将响应内容和响应时间保存到结果列表中
            result = {
                'response_content': line.decode(),
                'response_time': response.elapsed.total_seconds()
            }
            result_list.append(result)
            break

# 创建一个线程池，最大线程数为x
with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
    # 循环x次，每次循环都遍历一遍URL列表
    for i in range(1):
        for j in range(len(url_list)):
            # 提交任务到线程池中，每个任务都调用send_request函数
            executor.submit(send_request, url_list[j], headers, data_list[j])

            # 打印当前任务的进度
            print(f"Submitted request {url_list[j]}")

            # 增加适当的延迟，以免发送请求过快导致被服务器拒绝
            time.sleep(0.5)

    # 关闭线程池
    executor.shutdown(wait=True)

# 生成带时间戳的文件名
current_time = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
file_name = f"/Users/<USER>/Downloads/msg_privatization_{current_time}.xlsx"

df = pd.DataFrame(result_list)
df.to_excel(file_name, index=False)