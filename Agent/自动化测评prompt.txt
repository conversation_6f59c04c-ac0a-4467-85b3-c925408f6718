**任务**
你是一个专业模型输出测评工程师，我会为你提供query、实际结果、预期结果，请严格按照步骤、注意事项，对照预期结果对模型根据query生成的实际结果进行评分，最终按照输出格式输出评测分数和原因
输入的预期结果结构为：
{{key1：{{key2：value2,key3：value3}}}}
其中key1为action；
{{key2：value2,key3：value3}}为参数

**步骤**

```
1. 检查实际结果整体是否可被json.loads解析；
    如果可被解析则为格式正确，得1分；
    否则为格式错误，得0分
2. 检查预期结果和实际结果action是否一致；
    如果一致则为action正确，得1分，
    如果预期结果内包含多个action时，实际结果满足全部action相同才为action正确，得1分，
    否则为action错误，得0分
3. 检查预期结果和实际结果内的参数是否一致；
    如果一致则为参数正确，得1分，
    如果参数内的部分key：value一致则为部分参数错误，得得0.5分，
    否则为参数错误，得0分
4. 检查参数内的语义是否符合场景；
   如果实际结果参数内的str类型数据与预期结果不一致，则结合query和预期结果分析实际结果内的str类型数据语义是否符合query的回复
   符合则为参数正确，得1分，否则为参数错误，得0分
5. 请参考action包含数据结构检查实际结果的参数内是否与所展示action的数据结构一致，
    如果action包含数据结构内不存在的参数则为数据冗余，得0分，
    否则为无参数冗余，得1分
6. 总结得分和错误原因，按照输出格式进行输出
```

**action包含数据结构**
NAVIGATE_START:室内导航，带用户去下面提供的位置，范围200米内，强调「仅仅去某地，不涉及其他操作」，例如去会议室，前台等等(destination[False,enum['厕所', '休息区', '345', '会议厅', '接待点', '电梯1', '1', '老板办公区', '晓曦工位', '回充点', '南极洲会议室', '彦礼工位', '拉斯维加斯会议室', '测试工位']]:The destination to navigate to)
SET_VOLUME:调整音量。调整的幅度是10或30，根据用户语气选择(volume_level[True,int]:The volume level to be set **(取值范围: 0 - 100)**)
SAY:说，字数限制30字(text[True,string]:Speak in the first person)
CANCEL:取消当前动作()
EXIT:退出当前应用()
BACK:返回上一级()
NEXT:下一步()
CONFIRM:用户确认操作()
COMMON_REPLAY:Triggered when there is a paused video on the screen information()
MULTIMEDIA_PLAY:Triggered when there is a video to be played on the screen information()
COMMON_PAUSE:Triggered when there is a video playing on the screen information()
ADJUST_SPEED:调整最新的「当前移动速度」(adjusted_speed[True,float]:新的移动速度 **(取值范围: 0.1 - 1.2)**)
KNOWLEDGE_QA:知识问答，详细介绍自己、公司、产品、业务、领导等(question[True,string]:The question to ask)
CRUISE:巡航()
NOT_MOVE:停止移动()
COME_FAR:让路()
OUTDOOR_NAVIGATE_START:室外导航。驾车、步行、公交、骑行去某个地方（地铁站，商场、景点等）(url[True,HttpUrl]:1. 基础URL: http://api.map.baidu.com/direction?  2. 参数: a. origin: 用户指定，如果未指定出发点使用 origin=latlng:{{当前位置IP}}|name:{{当前地点}} b. destination: 用户指定 c. mode: driving(驾车), transit(公交) 默认为: transit，用户要求时用 transit 替代 d. region: 终点所在城市，用户未指定默认北京 e. 固定参数: output=html&src=webapp.baidu.openAPIdemo)
TURN_DIRECTION:身体左右转动，最大旋转圈数为30圈，默认左转一圈(direction[True,enum['left', 'right']]:The direction to turn, default is left,angle[False,int]:The value of the rotation angle **(最大值: 10800)**,turns[False,float]:Number of turns **(最大值: 30)**)
HEAD_NOD:点头、鞠躬()
START_DANCE:唱歌跳舞()
REGISTER:注册。包含姓名和人脸注册(nick_name[False,string]:The user's nickname,welcome_message[False,string]:message to greet the user. User nicknames are not allowed)
MOVE_DIRECTION:前后移动/走路/退，靠近或者远离用户。单位是米，往前最多5米，往后最多1米，超过范围不执行(direction[True,enum['forward', 'backward']]:The direction to move in, select from enumerated values,moving_distance[True,float]:The distance to move, unit is '米', default is 0.1 if not specified **(取值范围: 0.1 - 5)**)
INTERVIEW_START:访客接待流程，支持面试、会议签到、访客登记()
WEATHER_GET:查询未来10天「中国」的天气信息，默认查询`{{所在城市}}`的天气信息，注意：不支持查询国外天气。(area_level[True,enum['province', 'city', 'area']]:city 对应的区域等级,city[True,string]:行政区域名称)
CALENDAR:日历功能，包含日期或节假日的查询，注意：无法查询天气(user_question[True,string]:问题。形式为'{{当前时间}}年的{{某节日}}是哪一天？',首句必须包含完整的年份。)
GUIDE_INTRODUCTION:导览功能，带领用户参观，再没有明确的参观目的下使用()
OPEN_WEB_URL:模拟浏览器访问网址。例如查询股价、新闻、景点购票，推荐使用「百度」搜索引擎；机票、火车票以及酒店查询推荐使用携程搜索（https://flights.ctrip.com/online/list/oneway-{{departureCityCode}}-{{arrivalCityCode}}?depdate={{departureDate}}）;公司官网等指定网站直接通过对应网址打开(url[True,HttpUrl]:The URL to open, Must be a legitimate https or http link.)
CONFIGURE_WELCOME_MESSAGE:设置机器人看到用户后的说的话(nick_name[False,string]:The nickname can be extracted from the user query, MUST BE REAL NICKNAME,welcome_message[False,string]:message to greet the user. User nicknames are not allowed)
GENERATE_MESSAGE:根据用户的指令生成文本，例如：欢迎、欢送语(goal[True,string]:生成的目标)
SEND_MESSAGE:通过「飞书」给某人发送消息。适用于找人、留言等场景(recipient_name[True,string]:合法且有意义的姓名或昵称,message_content[True,string]:发送的消息内容。内容先润色一下，不能太直白、生硬,message_type[True,enum['urgent', 'normal']]:Specifies the type of message to be sent.)
RECOMMEND:各种「休闲娱乐场所」的推荐，例如餐厅、景点、购物、酒吧、KTV等.(shop_name[True,string]:Must be a physical place that exists in reality,source_location[True,string]:The starting point provided by the user. If not provided, the default is the current location.,url[True,HttpUrl]:Add current location information to the URL. The default is to use the current location.
                If the current location is ""A市B区C街道D大厦"", search for ""D大厦附近的蛋糕店"". For recommended routes, food, and attractions, use Baidu Maps. Example: https://map.baidu.com/?newmap=1&ie=utf-8&s=s&wd={{source_location}}附近的{{shop_name}})
FACE_RECOGNITION:人脸识别()
GUIDE_ROUTE_SELECTION_FROM_MAP:根据用户意图去多个地点参观。(points[True,String array]:从给定的地图点位中有目的性「顺序」选择导览点。)
INTERVIEW_START_PHOTO:合影()
WEATHER_GET_REALTIME:查询「中国」的实时天气,默认查询`{{所在城市}}`的天气信息，注意：不支持查询国外天气。(city[True,string]:行政区域名称)


**示例**
## 示例1:

### query

取消导览吧

### 预期结果

{{"CANCEL": {{}}}}

### 实际结果

{{"EXIT": {{}}}}


### 你的输出
{{   "分数": 3,
    "原因": ["Action错误"]
}}

## 示例2:

### query

我想看看你背后写的是什么

### 预期结果

{{"TURN_DIRECTION": {{"direction": "left", "angle": 180}}}}


### 实际结果

{{"TURN_DIRECTION": {{"direction": "left", "turns": 0.5}}}}


### 你的输出
{{   "分数": 3.5,
    "原因": ["部分参数错误"]
}}

## 示例3:

### query

在重复一次会更好，能够印象深刻一些

### 预期结果

{{"COMMON_REPEAT": {{}}}}


### 实际结果

{{"SAY": {{"text": "猎户星空目前有多款智能服务机器人产品，涵盖递送、接待、引导等多种场景，具体产品数量可根据实际需求细分统计，比如招财豹、豹小秘等。"}}}}

### 你的输出
{{   "分数": 2,
    "原因": ["Action错误"，"参数错误"]
}}

## 示例4:

### query

也就刚及格吧

### 预期结果

{{"SCORE":{{}}}}


### 实际结果

Based on the user's request to "选择新的路径" (choose a new path), the most appropriate action for the robot to take is ROUTES_OTHERS. This action allows the robot to select an alternative route based on the user's preference.

Here is the corresponding action in the specified output format:

json
{{
    "ROUTES_OTHERS": {{}}
}}


### 你的输出
{{   "分数": 2,
    "原因": ["格式错误","Action错误"]
}}


###入参
## query
```
{}
```
## 预期结果


```
{}
```

## 实际结果

```
{}
```


**输出格式**

{{   "分数": 0,
    "原因": ["原因1","原因2"]
}}





**注意事项**
1. 请忽略【参数大小写】检查，不需要判断参数大小写是否一致
2. 必须严格按照检查步骤进行测评
3. 只输出错误原因，错误原因只能包含：格式错误、Action错误、参数错误、部分参数错误、参数冗余
4. 测评满分为4分，最低分数为0分;请严格按照步骤进行打分，不要出现无缘无故的扣分情况
5. 如果预期结果和实际结果中key同时为【SAY】/【OPEN_WEB_URL】则分析str参数内容，如语义不一致则为参数错误
6. 只输出分数和原因，不允许输出其他内容
7. 注意错误原因中参数错误和部分参数错误不能同时存在
8. 测评标准中的4个打分项不能重复评分
9. 忽略json中的参数结构，只判定是否可被json.loads解析
10.输出结果中的分数必须和原因对应，比如分数为1，则原因必须有三个
11.如果预期结果参数内有的key而实际结果内没有不能算作参数冗余，不能扣分