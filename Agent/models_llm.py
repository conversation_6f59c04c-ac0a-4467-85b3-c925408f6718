import json
import time
import requests

from openai import OpenAI
import vertexai
from vertexai.generative_models import GenerativeModel
import os
import boto3


def get_model_response(model_name, system_prompt, user_prompt1, user_prompt2):
    """
    通用模型调用方法
    :param model_name: 模型名称，对应models_config.json中的key
    :param system_prompt: 系统prompt
    :param user_prompt1: 用户prompt1
    :param user_prompt2: 用户prompt2
    :return: 模型返回的结果
    """
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config_path = os.path.join(script_dir, 'models_config.json')

    try:
        # 加载模型配置
        with open(config_path, 'r', encoding='utf-8') as f:
            models_config = json.load(f)

        # 检查模型是否存在
        if model_name not in models_config:
            raise ValueError(f"模型 {model_name} 未在配置文件中找到")

        model_config = models_config[model_name]

        # 根据调用类型选择不同的处理方式
        if model_config["call_type"] == "openai":
            client = OpenAI(
                api_key=model_config["api_key"],
                base_url=model_config["base_url"]
            )
            response = client.chat.completions.create(
                model=model_config["model"],
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt1},
                    {"role": "user", "content": user_prompt2}
                ],
                temperature=0.0
            )
            # print(response)
            return response.choices[0].message.content

        elif model_config["call_type"] == "api":
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {model_config['api_key']}"
            }
            data = {
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt1},
                    {"role": "user", "content": user_prompt2}
                ],
                "temperature": 0.0,
                "model": model_config["model"]
            }

            response = requests.post(model_config["base_url"], headers=headers, json=data)
            # print(response.json())
            return response.json()["choices"][0]["message"]["content"]

        elif model_config["call_type"] == "vertexai":
            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = model_config["credentials"]
            vertexai.init(project=model_config["project"], location=model_config["location"])
            model = GenerativeModel(model_config["model"])
            chat_session = model.start_chat()
            prompt_massage = [f"""{{"role": "system", "content": {system_prompt}}}""",
                              f""""{{role": "user", "content": {user_prompt1}}}""",
                              f"""{{"role": "user", "content": {user_prompt2}}}"""
                              ]
            response = chat_session.send_message(prompt_massage, stream=False)
            # print(response)
            return response.candidates[0].content.parts[0].text

        elif model_config["call_type"] == "bedrock":
            client = boto3.client(
                "bedrock-runtime",
                region_name=model_config["region"],
                aws_access_key_id=model_config["aws_access_key_id"],
                aws_secret_access_key=model_config["aws_secret_access_key"]
            )
            messages = [
                {"role": "user", "content": [{"text": system_prompt}]},
                {"role": "user", "content": [{"text": user_prompt1}]},
                {"role": "user", "content": [{"text": user_prompt2}]}
            ]
            model_response = client.converse(
                modelId=model_config["model"],
                messages=messages,
                inferenceConfig={"maxTokens": 300, "temperature": 0.0},
                additionalModelRequestFields={"inferenceConfig": {"topK": 20}}
            )
            # print(model_response)
            return model_response["output"]["message"]["content"][0]["text"]

        else:
            raise ValueError(f"不支持的调用类型: {model_config['call_type']}")

    except Exception as e:
        print(f"调用模型 {model_name} 时出错: {e}")
        return None

if __name__ == '__main__':
    model_name = "gpt_4o"
    system_prompt = """你的名字是`小小豹机器人`，你是`猎户星空创造的机器人`，所在公司介绍 ：`ORIONSTSAR`。专业化。
"""
    user_prompt1 = """# ROBOT BASIC INFORMATION
{'基本能力': '具备出色的任务规划与执行能力、丰富的语言表达能力以及强大的记忆力', '当前位置经纬度': '39.911329,116.566428', '所在国家': '中国', '所在省份': '北京市', '所在城市': '北京市', '所在地区': '朝阳区', '所在街道': '建国路', '所在地点': '在万东科技文化创意产业园附近'}

# ROBOT'S ACTIONS 
Format: action:usage_conditions(param1[is_required,type[enum]]:desc[**usage_restrictions**],...)

SET_VOLUME:调整音量。调整的幅度是10或30，根据用户语气选择(volume_level[True,int]:The volume level to be set **(取值范围: 0 - 100)**)
SAY:说话。例如回答机器人状态信息。(text[True,string]:Speak in the first person, words limit 30)
CANCEL:取消当前动作。例如跳舞、点头、导航等()
EXIT:退出当前应用()
BACK:返回上一级()
NEXT:下一步()
CONFIRM:用户确认操作()
COMMON_REPLAY:Triggered when there is a paused video on the screen information.()
MULTIMEDIA_PLAY:Triggered when there is a video to be played on the screen information.()
COMMON_PAUSE:暂停导览讲解过程；媒体资源播放时，暂停播放。()
ADJUST_SPEED:调整<移动速度>。(adjusted_speed[True,float]:新的移动速度。 **(取值范围: 0.1 - 1.2)**)
KNOWLEDGE_QA:查询知识回答用户的问题，包含「百科知识」、「公司产品及人员信息」等(question[True,string]:The question to ask.)
NAVIGATE_REC_START:室内点位导航意图。只能带用户去下面提供的位置，不支持去室外位置。如果用户没有明确指定目的地，则返回最多4个可用的导航点位列表，并按照相似性排序，最靠近的点位在前面(destinations[False,String array['晓曦工位', '南极洲会议室', '彦礼工位', '老板办公区', '厕所', '测试点', '休息区', '拉斯维加斯会议室', '接待点', '回充点', '电梯1', '会议室', '测试部']]:The user wants to navigate to a ranked list of recommended destinations (maximum 4 destinations),guide_text[True,string]:A natural guiding response that varies based on the number of destinations:
    - For single destination: Direct guidance to that location
    - For multiple destinations: Present options and ask for user's choice
    - For no destinations: Provide a friendly introduction to all available locations with a phrase like 'Here are some places you might be interested in')

CRUISE:巡视，巡逻。()
NOT_MOVE:停止走路。()
COME_FAR:让路()
OUTDOOR_NAVIGATE_START:室外路线导航。(origin[True,string]:The starting point provided by the user. If not provided, the default is the `<current location of the robot>`.,destination[True,string]:The destination to navigate to,mode[True,enum['driving', 'transit']]:The mode to navigate, select from enumerated values,region[True,string]:The region to navigate, default is `<current city of the robot>`)
TURN_DIRECTION:身体左右转动，最多只能转10圈，如果超过10圈，使用`SAY`拒绝用户，然后根据用户的要求选择是圈数还是角度。(direction[True,enum['left', 'right']]:The direction to turn, default is left,angle[False,int]:The value of the rotation angle **(最大值: 3600)**,turns[False,float]:Number of turns **(最大值: 10)**)
HEAD_NOD:点头、鞠躬()
START_DANCE:跳舞、播放音乐。()
REGISTER:注册。包含姓名和人脸注册(nick_name[False,string]:The nickname can be extracted from the user query, MUST BE REAL NICKNAME,welcome_message[False,string]:message to greet the user. MUST NOT CONTAIN USER'S NICKNAME)
MOVE_DIRECTION:前后移动，靠近或者远离用户。超过限制的运动距离可以使用`SAY`拒绝用户。(direction[True,enum['forward', 'backward']]:The direction to move in, select from enumerated values,moving_distance[True,float]:单位米，默认走0.1米，**往前最多5米，往后最多1米**，决不能超过此范围。 **(取值范围: 0.1 - 5)**)
INTERVIEW_START:访客接待流程，支持面试、会议签到、访客登记。()
WEATHER_GET:查询「明天及未来10天」的「中国及港澳台」地区的天气（包含风力、温度、湿度、空气质量等），不支持查询国外天气！！！(area_level[True,enum['province', 'city', 'area']]:city 对应的区域等级,city[True,string]:行政区域名称，默认查询`<所在城市>`。)
CALENDAR:日历功能，包含日期或节假日的查询，注意：无法查询天气(user_question[True,string]:question.Format: When is <holidays or other times> in <current time>.The first sentence must contain the complete year.)
GUIDE_INTRODUCTION:导览功能，带领用户参观，在没有明确的参观目的下使用。()
OPEN_WEB_URL:模拟浏览器访问网址，例如股票、门票、新闻等。搜索引擎推荐使用「百度」搜索引擎；公司官网等指定网站直接通过对应网址打开。(url[True,HttpUrl]:google or a specified URL. Must be a legitimate https or http link.)
FLIGHT_TICKET_QUERY:飞机票查询(departure_city_code[True,string]:The IATA code of the departure city,arrival_city_code[True,string]:The IATA code of the arrival city,departure_date[True,string]:The departure date, for example: 2024-01-01)
TRAIN_TICKET_QUERY:火车票查询(departure_city[True,string]:城市名称，例如`北京`不要带`市`字,arrival_city[True,string]:城市名称，例如`北京`不要带`市`字,departure_date[True,string]:出发日期，例如`2024-01-01`)
CONFIGURE_WELCOME_MESSAGE:设置机器人看到用户后的说的话(nick_name[False,string]:The nickname can be extracted from the user query, MUST BE REAL NICKNAME,welcome_message[False,string]:message to greet the user. MUST NOT CONTAIN USER'S NICKNAME)
GENERATE_MESSAGE:文本生成。只能生成「欢迎语」、「欢送语」、「诗词」、「故事」、「笑话」。无法生成「建议」、「推荐」、「介绍」等(goal[True,string]:user instructions)
SEND_MESSAGE:通过「飞书」给某人发送消息。适用于找人、留言等场景(recipient_name[True,string]:Absolutely real names, not personal pronouns,message_content[True,string]:Send the message content. The content should be polished first, not too direct or stiff,message_type[True,enum['urgent', 'normal']]:Specifies the type of message to be sent.)
RECOMMEND:搜索推荐酒店、路线（到地铁站、景点等）、景点、购物等。(place[True,string]:Name of the place of actual meaning,near_by[True,string]:The starting point provided by the user. If not provided, the default is the current location.)
FACE_RECOGNITION:人脸识别。()
GUIDE_ROUTE_SELECTION_FROM_MAP:用户参观游览。带领用户去下面提供的多个地点。(points[True,String array]:顺序选择地点，决不能捏造不存在的点位。)
INTERVIEW_START_PHOTO:合影()
WEATHER_GET_REALTIME:查询「当天」的「中国及港澳台」地区的天气（包含风力、温度、湿度、空气质量等），不支持查询国外天气！！！(city[True,string]:行政区域名称，默认查询`<所在城市>`。)
ANSWER_QUESTION_FROM_VISION:通过机器人的摄像头观察并回答问题，包括外貌识别、周边环境描述、物体识别等(question[True,string]:根据图片要回答的问题，必须总结为第一人称的问题，生成的问题要简单)
OPEN_WEB_URL_DEFINED:模拟浏览器访问网址，例如股票、门票、新闻等。搜索引擎推荐使用「百度」搜索引擎；公司官网等指定网站直接通过对应网址打开。*只要*预定义问题列表中存在与用户当前问题*语义相同*的问题，则选择此action，因为可以找到用户设置好的URL直接打开。(predefined_question[True,enum['我要听歌', '来首音乐', '打开音乐', '放首歌', '给我放首音乐', '播放流行歌曲', '播报音乐', '打开网易云音乐', '我要听音乐', '我看看建图', '打开地图工具', '我要查快递', '寄快递', '我的快递到哪了', '我要邮寄快递', '查快递', '帮我查下快递', '打开设置功能', '打开饿了吗', '外卖', '打开猎豹官网', '打开猎豹移动官网', '介绍一下猎豹移动网站', '今天有哪些热门微博', '打开微博', '我要看微博热搜', '打开微博热搜', '我要看热门微博', '播放电影', '播放电视剧', '近期有啥热门电视剧', '我要看电视剧', '播放综艺节目', '播放动画片', '我要看综艺', '我要看电影', '我要看动画片', '给我播个电视剧', '打开腾讯视频', '我要看电视', '最近有什么电视剧']]:list of predefined question.)

"""
    user_prompt2 = """# ROBOT REAL-TIME INFORMATION
{'当前音量': 0, '电池电量': 71, '当前时间': '2025年03月18日 19点09分27秒', '当前移动速度': 0.29640928}

# EXAMPLES
Score: 0.657, Input: <Robot> SAY '你确认要xxx吗？' <User> SAY '确定'
{"CONFIRM": {}}
Score: 0.654, Input: <User> SAY '帮我打把伞'。<Robot> SAY '好的'。<User> SAY '我刚才说了什么？'
{"SAY": {"text": "你上次说 “帮我带把伞”"}}

# CHAT CONVERSATION
<User> JUST SAID '救命我憋不住了快点快点'

# OUTPUT FORMAT
```json
{"<action>": {"<param1>": <value1>}}
```

Speech-to-text content from 「<User> JUST SAID」 contain homophonic errors, near-sound errors and misrecognitions. You MUST correct the key words first. First. Traverse the usage conditions of each action. If you are still unsure of the answer, you can also refer to the provided **EXAMPLES**. Finally, ONLY OUTPUT THE MOST SUITABLE SINGLE ACTION, If the Action's parameters have enumerated values, the parameters must be chosen from the given enumerated values. with the default robot language being Chinese.

"""
    print(get_model_response(model_name, system_prompt, user_prompt1, user_prompt2))