import json
import os
from datetime import datetime
from openai import OpenAI
import pandas as pd


def is_valid_json(string):
    try:
        json.loads(string)
        return True
    except json.JSONDecodeError:
        return False


def get_response_gpt4o(user_prompt1):
    try:
        client = OpenAI(
            api_key='***************************************************',
            base_url='https://api.openai.com/v1')
        response = client.chat.completions.create(
            model="gpt-4o-2024-11-20",
            messages=[

                {"role": "user", "content": user_prompt1}

            ],
            temperature=0.0,
            # stream=True  # 启用流式输出
        )
        return response.choices[0].message.content
    except Exception as e:
        print(f"Error: {e}")
        return None


def calculate_score(path):
    pf = pd.read_excel(path)
    # 筛选出“模型评分”不为空的行
    now_pf = pf[pf["模型评分"].notna()]

    # 计算总分和精度
    total_score = len(now_pf) * 4
    score_p = round(now_pf["评分"].sum() / total_score * 100)
    score_m = round(now_pf["模型评分"].sum() / total_score * 100)
    total_precision = round((score_m / score_p) * 100)

    # print(score_p, score_m, total_precision)

    # 筛选出“模型评分”不为空且“模型评分理由”不等于“工程对比，完全一致”的行
    now_pf_filtered = now_pf[now_pf["模型评分理由"] != "工程对比，完全一致"]

    # 计算新的总分和精度
    total_score_filtered = len(now_pf_filtered) * 4
    score_p_filtered = round(now_pf_filtered["评分"].sum() / total_score_filtered * 100)
    score_m_filtered = round(now_pf_filtered["模型评分"].sum() / total_score_filtered * 100)
    total_precision_filtered = round((score_m_filtered / score_p_filtered) * 100)

    # print(score_p_filtered, score_m_filtered, total_precision_filtered)
    # 创建 DataFrame 保存结果
    results = pd.DataFrame({
        '人工准确率': [score_p],
        '自动化测评准确率': [score_m],
        '总准确率': [f'{total_precision}%'],
        '模型处理样本数': [len(now_pf_filtered)],
        '人工准确率（模型处理）': [score_p_filtered],
        '自动化准确率（模型处理）': [score_m_filtered],
        '模型准确率': [f'{total_precision_filtered}%']
    }, index=['1'])

    # 写入 Excel 的 Sheet2
    with pd.ExcelWriter(path, mode='a',
                        engine='openpyxl', if_sheet_exists='replace') as writer:
        results.to_excel(writer, sheet_name='Sheet2')

    print("分析结果保存到Sheet2")


def resp_answers_gpt(path):
    cwd_path = os.getcwd()
    fail_path = os.path.join(cwd_path, "自动化测评prompt.txt")
    p_paht = os.path.join(cwd_path, path)
    # print(fail_path)
    with open(fail_path, 'r', encoding='utf-8') as file:
        prompt = file.read()
        # print(prompt)
    pf = pd.read_excel(p_paht)
    # print(pf)
    score = []
    desc = []
    prompt1 = []
    text = None
    for index, row in pf.iterrows():

        try:
            print(f"第{index + 1}条数据")
            query = row["query"]
            compare_answer = row["Expected Answer"]
            compare_answer=json.loads(compare_answer)
            print(compare_answer)
            answer = row["Actual Answer"]
            if is_valid_json(answer) and json.loads(answer) == compare_answer:
                score.append(4)
                desc.append("工程对比，完全一致")
                prompt1.append("")
            else:
                p_prompt = prompt.format(query, compare_answer, answer)
                # print("prompt是：")
                prompt1.append(p_prompt)
                text = get_response_gpt4o(p_prompt)
                # print(text)
                cleaned_string = text.replace('```json', '').replace('```', '').strip()
                res_json = json.loads(cleaned_string)
                score.append(res_json["分数"])
                desc.append(res_json["原因"])

                score.append("")
                desc.append("")
                prompt1.append("")
        except Exception as e:
            score.append("")
            desc.append(text)
            print(f"第{index + 1}条报错了 : {e}")
            prompt1.append("")
            print(text)
    print(len(prompt1))
    print(len(score))
    print(len(desc))
    pf["prompt"] = prompt1
    pf["模型评分"] = score
    pf["模型评分理由"] = desc
    now = datetime.now()
    current_dir = os.getcwd()
    raw_path = os.path.join(current_dir, 'data', '评测结果')
    output_dir = os.path.join(raw_path, now.strftime('%m-%d'))
    os.makedirs(output_dir, exist_ok=True)  # 确保目录存在
    output_file_path = os.path.join(output_dir, f'Action抽取_测评结果{now.strftime("%H_%M")}.xlsx')
    pf.to_excel(output_file_path, index=False)
    print(f"结果已保存到 '{output_file_path}'")
    # calculate_score(output_file_path)
    return
    # pf.to_excel(path, index=False)
    # print("数据写入完成")


if __name__ == '__main__':

    path = r"C:\Users\<USER>\Downloads\普通测试集_V1.0.0.250321.O_0327_英文_V1_0327_GPT-4o (1).xlsx"
    resp_answers_gpt(path)

    # 读取 Excel 文件
