import json
import requests
import pandas as pd
from datetime import datetime
import re


def get_prompt(environment, sessionid, startTime, endTime):
    if environment == "test":
        base_url = 'https://test-backoffice.ainirobot.com/'
    elif environment == "pre":
        base_url = "https://backoffice.ainirobot.com/"
    else:
        raise ValueError("Invalid environment. Choose either 'test' or 'pre'.")
    url = f"{base_url}realtime/ajax_list/"

    token = get_token(base_url)
    headers = {
        'accept': 'application/json, text/javascript, */*; q=0.01',
        'accept-language': 'zh-CN,zh;q=0.9',
        'content-type': 'application/json',
        'cookie': f"PHPSESSID={token}; token={token}",
        'origin': 'https://test-backoffice.ainirobot.com',
        'priority': 'u=1, i',
        'referer': 'https://test-backoffice.ainirobot.com/realtime/index',
        'sec-ch-ua': '"Chromium";v="128", "Not;A=Brand";v="24", "Google Chrome";v="128"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        'x-requested-with': 'XMLHttpRequest'
    }

    data = {
        "page": 1,
        "limit": 50,
        "client_id": "",
        "corp_scen": "",
        "model": "",
        "enterprise_id": None,
        "device_id": "",
        "session_id": sessionid,
        "startTime": startTime,
        "endTime": endTime,
        "query": "",
        "query_precise": 0,
        "answer": "",
        "answer_precise": 0,
        "exclude_writing": 1,
        "exclude_test": 1,
        "domain": "",
        "intent": None,
        "domain_c": "",
        "intent_c": "",
        "face_info_exists": "",
        "opk_id": None,
        "opk_path": None
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        # print(response.json())
        response.raise_for_status()
        response_data = response.json()

        # 检查响应数据是否为空
        if not response_data.get("data") or len(response_data["data"]) == 0:
            print(f"会话 {sessionid} 没有找到数据")
            return None, None, "", "", "", ""

        # 检查debug_info是否存在
        if response_data["data"][0].get("plan_info") == "":

            debug_info = response_data["data"][1].get("debug_info")
        else:
            debug_info = response_data["data"][0].get("debug_info")
        if not debug_info:
            print(f"会话 {sessionid} 没有找到 debug_info")
            return None, None, "", "", "", ""

        info = json.loads(debug_info)
        # print(info["app_id"])
        # print(info["package_name"])

        # # 检查必要的字段是否存在
        # if not info.get("agent_debug") or not info["agent_debug"].get("agent_messages"):
        #     print(f"Missing required fields in debug_info for session {sessionid}")
        #     return None, None, "", "", "", ""

        messages = info["agent_debug"]["agent_messages"]
        if len(messages) < 4:
            raw = ""
            print(f"会话 {sessionid} 信息不足,开始拼接实际结果")
            plan_info = response_data["data"][0].get("plan_info")
            match = re.search(r'name="orion\.agent\.action\.(\w+)"', plan_info)
            if match:
                action_name = match.group(1)
                # print(action_name)
                if action_name == "SET_VOLUME":
                    match = re.search(r'volume_level="(\d+)"', plan_info)
                    if match:
                        volume_level = match.group(1)
                        raw = {f"{action_name}": {"volume_level": int(volume_level)}}
                else:
                    raw = {f"{action_name}": {}}
                raw = json.dumps(raw)
                print("拼接的实际结果:", "\n", raw)
                return info, None, "", "", "", raw
            return info, None, "", "", "", ""

        prompt = messages[0]["content"]
        userprompt1 = messages[1]["content"]
        userprompt2 = messages[2]["content"]
        row = messages[3]["content"]
        return info, messages, prompt, userprompt1, userprompt2, row
    except requests.exceptions.RequestException as req_err:
        print(f"请求错误: {req_err}")
    except KeyError as key_err:
        print(f"键错误: {key_err}")
    except json.JSONDecodeError as json_err:
        print(f"JSON 解码错误: {json_err}")
    except Exception as other_error:
        print(f"意外错误: {other_error}")

    return None, None, "", "", "", ""


def get_token(url):
    path = "login/dologin"
    base_url = url + path
    headers = {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9",
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
        "cookie": "PHPSESSID=28905202911d4b3a7a8655a29a0fc85a; token=28905202911d4b3a7a8655a29a0fc85a",
        "origin": "https://test-backoffice.ainirobot.com",
        "priority": "u=1, i",
        "referer": "https://test-backoffice.ainirobot.com/login/index",
        "sec-ch-ua": '"Chromium";v="128", "Not;A=Brand";v="24", "Google Chrome";v="128"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-requested-with": "XMLHttpRequest"
    }

    data = {
        "user": "wangxuejian",
        "pass": "xjwang1_12"
    }

    response = requests.post(base_url, headers=headers, data=data)
    return response.json()["token"]


def expand_json(row):
    try:
        # 检查debug字段是否为空
        if not row.get('elapse_info'):
            print(f"调试值为空")
            return row

        if isinstance(row['elapse_info'], dict):
            json_data = row['elapse_info']
        else:
            # 确保debug值是有效的字符串
            debug_str = str(row['elapse_info']).strip()
            if not debug_str:
                print(f"调试字符串为空")
                return row

            json_data = json.loads(debug_str.replace("'", "\""))

        for key, value in json_data.items():
            row[key] = value
    except Exception as err:

        print(f"处理行时出错: {err}")
        print(f"调试值: {row['elapse_info']}")
    return row


def process_json_string(json_string):
    # 检查并去掉前缀
    if json_string.startswith("```json"):
        json_string = json_string[7:].strip()
    if json_string.endswith("```"):
        json_string = json_string[:-3].strip()

    return json_string


def extract_prompt(messages):
    System_Prompt = messages[0]["content"]

    # ROBOT_BASIC_INFORMATION
    match = re.search(r'ROBOT BASIC INFORMATION(.*?)\n\n', messages[1]["content"], re.DOTALL)
    ROBOT_BASIC_INFORMATION = match.group(1) if match else ""

    # ROBOTS_ACTIONS
    match = re.search(r"# ROBOT\'S ACTIONS(.*)", messages[1]["content"], re.DOTALL)
    ROBOTS_ACTIONS = match.group(1) if match else ""

    # INFORMATION
    match = re.search(r"# ROBOT REAL-TIME INFORMATION(.*?)\n\n", messages[2]["content"], re.DOTALL)
    INFORMATION = match.group(1) if match else ""

    # screen_info
    match = re.search(r"# Screen Info(.*)# EXAMPLES", messages[2]["content"], re.DOTALL)
    screen_info = match.group(1) if match else ""

    # CHAT_CONVERSATION
    match = re.search(r"# CHAT CONVERSATION(.*?)\n\n", messages[2]["content"], re.DOTALL)
    CHAT_CONVERSATION = match.group(1) if match else ""

    # Relevant_information
    match = re.search(r"# User's Relevant information(.*?)\n\n", messages[2]["content"], re.DOTALL)
    Relevant_information = match.group(1) if match else ""

    # examples
    match = re.search(r"# EXAMPLES(.*?)\n\n", messages[2]["content"], re.DOTALL)
    examples = match.group(1) if match else ""

    return System_Prompt, ROBOT_BASIC_INFORMATION, ROBOTS_ACTIONS, INFORMATION, screen_info, examples, CHAT_CONVERSATION, Relevant_information


def main(path, environment, startTime, endTime):
    try:
        df = pd.read_excel(
            path)
        df = df.sort_values(by="caseID")
        elapse_info = []
        system_prompts = []
        user_prompt1s = []
        user_prompt2s = []
        messagess = []
        rows = []
        now_rows = []
        app_ids = []
        package_names = []
        prompt_tokens = []
        total_tokens = []
        cached_tokens = []
        completion_tokens = []
        debug_infos = []
        token_detail = []
        System_Prompts, ROBOT_BASIC_INFORMATIONs, ROBOTS_ACTIONSs, INFORMATIONs, Screen_Infos, EXAMPLESs, CHAT_CONVERSATIONs, Relevant_informations = [], [], [], [], [], [], [], []

        for index, session_id in enumerate(df["SID"]):
            print(f"正在处理第{index + 1}条数据")
            try:
                if session_id is not None:
                    debug, messages, system_prompt, user_prompt1, user_prompt2, row = get_prompt(environment,
                                                                                                 session_id,
                                                                                                 startTime, endTime)
                    # print(debug)
                    debug_infos.append(debug)
                    elapse_info.append(debug["elapse_info"] if debug else "")
                    messagess.append(messages if messages else "")
                    system_prompts.append(system_prompt)
                    user_prompt1s.append(user_prompt1)
                    user_prompt2s.append(user_prompt2)
                    rows.append(row)
                    now_rows.append(process_json_string(row))
                    try:
                        # print(debug["agent_debug"].keys())
                        token_detail.append(debug["agent_debug"]["agent_token_cost"])
                        prompt_tokens.append(debug["agent_debug"]["agent_token_cost"]["prompt_tokens"])
                        total_tokens.append(debug["agent_debug"]["agent_token_cost"]["total_tokens"])
                        cached_tokens.append(
                            debug["agent_debug"]["agent_token_cost"]["prompt_tokens_details"]["cached_tokens"])
                        completion_tokens.append(debug["agent_debug"]["agent_token_cost"]["completion_tokens"])
                    except Exception as e:
                        print(e)
                        token_detail.append("")
                        prompt_tokens.append("")
                        total_tokens.append("")
                        cached_tokens.append("")
                        completion_tokens.append("")
                    if system_prompt == "":

                        System_Prompt, ROBOT_BASIC_INFORMATION, ROBOTS_ACTIONS, INFORMATION, Screen_Info, EXAMPLES, CHAT_CONVERSATION, Relevant_information = "", "", "", "", "", "", "", ""
                    else:
                        System_Prompt, ROBOT_BASIC_INFORMATION, ROBOTS_ACTIONS, INFORMATION, Screen_Info, EXAMPLES, CHAT_CONVERSATION, Relevant_information = extract_prompt(
                            messages)
                    System_Prompts.append(System_Prompt)
                    ROBOT_BASIC_INFORMATIONs.append(ROBOT_BASIC_INFORMATION)
                    ROBOTS_ACTIONSs.append(ROBOTS_ACTIONS)
                    INFORMATIONs.append(INFORMATION)
                    Screen_Infos.append(Screen_Info)
                    EXAMPLESs.append(EXAMPLES)
                    CHAT_CONVERSATIONs.append(CHAT_CONVERSATION)
                    Relevant_informations.append(Relevant_information)
                    app_ids.append(debug["app_id"] if debug else "")
                    package_names.append(debug["package_name"] if debug else "")
                else:
                    debug_infos.append("")
                    elapse_info.append("")
                    messagess.append("")
                    system_prompts.append("")
                    user_prompt1s.append("")
                    user_prompt2s.append("")
                    rows.append("")
                    now_rows.append("")
                    System_Prompts.append("")
                    ROBOT_BASIC_INFORMATIONs.append("")
                    ROBOTS_ACTIONSs.append("")
                    INFORMATIONs.append("")
                    Screen_Infos.append("")
                    EXAMPLESs.append("")
                    CHAT_CONVERSATIONs.append("")
                    Relevant_informations.append("")
                    app_ids.append("")
                    package_names.append("")
                    token_detail.append("")
                    prompt_tokens.append("")
                    total_tokens.append("")
                    cached_tokens.append("")
                    completion_tokens.append("")
            except Exception as e:
                # print(1)
                print(f"处理会话 {index + 1} 时出错: {e}")
                debug_infos.append("")
                elapse_info.append("")
                messagess.append("")
                system_prompts.append("")
                user_prompt1s.append("")
                user_prompt2s.append("")
                rows.append("")
                now_rows.append("")
                System_Prompts.append("")
                ROBOT_BASIC_INFORMATIONs.append("")
                ROBOTS_ACTIONSs.append("")
                INFORMATIONs.append("")
                Screen_Infos.append("")
                EXAMPLESs.append("")
                CHAT_CONVERSATIONs.append("")
                Relevant_informations.append("")
                app_ids.append("")
                package_names.append("")
                prompt_tokens.append("")
                total_tokens.append("")
                cached_tokens.append("")
                completion_tokens.append("")
                token_detail.append("")

        df_new = pd.DataFrame({
            "query": df["query"],
            "Action": df["Action"],
            "Action含义": df["Action含义"],
            "caseID": df["caseID"],
            "SID": df["SID"],
            "System Prompt": system_prompts,
            # "User Prompt1": user_prompt1s,
            # "User Prompt2": user_prompt2s,
            "Original Model Output": rows,
            "Actual Answer": now_rows,
            "Messages": messagess,
            "# ROBOT BASIC INFORMATION": ROBOT_BASIC_INFORMATIONs,
            "# ROBOT'S ACTIONS": ROBOTS_ACTIONSs,
            "# ROBOT REAL-TIME INFORMATION": INFORMATIONs,
            "# Screen Info": Screen_Infos,
            "# EXAMPLES": EXAMPLESs,
            "# CHAT CONVERSATION": CHAT_CONVERSATIONs,
            "# User's Relevant information": Relevant_informations,
            "Expected Answer": df["Expected Answer"],
            "elapse_info": elapse_info,
            "Token Detail": token_detail,
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "total_tokens": total_tokens,
            "cached_tokens": cached_tokens,
            "app_id": app_ids,
            "package_name": package_names,
            "Debug_Info": debug_infos,
        })

        df_new = df_new.apply(expand_json, axis=1)

        # 添加新列：计算 plan_end-vad_end
        try:
            df_new['plan_end-vad_end'] = (
                    (pd.to_datetime(df_new['plan_end_timestamp'], format='ISO8601') -
                     pd.to_datetime(df_new['vad_end_timestamp'], format='ISO8601')).dt.total_seconds() * 1000 +
                    (df_new['agent_core_total_time'] - df_new['plan_total_cost_time'])
            )
        except Exception as e:
            print(f"计算 plan_end-vad_end 时出错: {e}")
            df_new['plan_end-vad_end'] = None
        desired_order = [
            "query", "Action", "Action含义", "caseID", "SID",
            "System Prompt", "Debug_Info", "Messages",
            "# ROBOT BASIC INFORMATION", "# ROBOT'S ACTIONS", "# ROBOT REAL-TIME INFORMATION", "# Screen Info",
            "# EXAMPLES", "# CHAT CONVERSATION", "# User's Relevant information",
            "Expected Answer", "Original Model Output", "Actual Answer",
            "elapse_info", "plan_end-vad_end", "vad_start_timestamp", "vad_end_timestamp", "asr_end_timestamp",
            "asr_cost_time", "wakeup_cost_time", "answer_start_timestamp", "plan_start_timestamp",
            "agent_core_load_context_time", "agent_core_load_action_time", "agent_core_embedded_time",
            "agent_core_call_summary_llm_time", "agent_core_select_few_shot_time",
            "agent_core_call_select_action_llm_time", "agent_core_load_user_profile_time", "agent_core_total_time",
            "agent_core_end_timestamp", "agent_core_start_timestamp", "agent_core_llm_retry_count",
            "plan_end_timestamp", "plan_total_cost_time", "Token Detail",
            "prompt_tokens", "completion_tokens", "total_tokens", "cached_tokens",
            "app_id", "package_name"
        ]

        # 只保留存在的列（避免KeyError）
        valid_columns = [col for col in desired_order if col in df_new.columns]
        df_new = df_new[valid_columns + [col for col in df_new.columns if col not in valid_columns]]
        # ------------------------------------------------------------

        # 获取当前时间并格式化
        current_time = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_filename = f"plan_prompt_result_{current_time}.xlsx"

        # 构建测评子表
        evaluation_data = {
            "caseID": df_new["caseID"],
            "query": df_new["query"],
            "SID": df_new["SID"],
            "messages": df_new["Messages"],
            "Expected_Answer": df_new["Expected Answer"],
            "original_model_output": df_new["Original Model Output"],
            "Actual_Answer": df_new["Actual Answer"],
        }
        df_evaluation = pd.DataFrame(evaluation_data)
        # 保存测评子表到新的Excel文件

        # 构建prompt拆分数据
        prompt_data = {
            "caseID": df_new["caseID"],
            "query": df_new["query"],
            "SID": df_new["SID"],
            "System Prompt": df_new["System Prompt"],
            "# ROBOT BASIC INFORMATION": df_new["# ROBOT BASIC INFORMATION"],
            "# ROBOT'S ACTIONS": df_new["# ROBOT'S ACTIONS"],
            "# ROBOT REAL-TIME INFORMATION": df_new["# ROBOT REAL-TIME INFORMATION"],
            "# Screen Info": df_new["# Screen Info"],
            "# EXAMPLES": df_new["# EXAMPLES"],
            "# CHAT CONVERSATION": df_new["# CHAT CONVERSATION"],
            "# User's Relevant information": df_new["# User's Relevant information"],

        }
        df_prompt = pd.DataFrame(prompt_data)
        # 保存prompt拆分数据到新的Excel文件

        with pd.ExcelWriter(output_filename) as writer:
            df_new.to_excel(writer, sheet_name="原始数据", index=False)
            df_evaluation.to_excel(writer, sheet_name="测评", index=False)
            df_prompt.to_excel(writer, sheet_name="prompt拆分", index=False)

        print(f"处理完成，结果已保存到 {output_filename}")

        # df.to_excel(output_filename, index=False)
    except Exception as e:
        print(f"读取或写入 Excel 文件时出错: {e}")


if __name__ == '__main__':
    # 生产对应pre、测试对应test
    environment = "test"
    path = r"C:\Users\<USER>\Downloads\普通测试集_V1.0.0.250321.O_0327_英文_V1_0327_GPT-4o (2).xlsx"
    starttime = "2025-03-17 00:00:00"
    endtime = "2025-03-27 23:59:59"
    main(path, environment, starttime, endtime)
