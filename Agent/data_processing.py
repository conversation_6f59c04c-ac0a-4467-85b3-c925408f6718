import pandas as pd


def process_excel(file_path):
    # 读取Excel文件
    df = pd.read_excel(file_path, engine='openpyxl')

    # 遍历User Prompt字段
    def clean_prompt(prompt):
        if pd.isna(prompt):
            return prompt
        # 找到# Similar Answers和# User's Relevant information的位置
        start_index = prompt.find('# Similar Answers')
        end_index = prompt.find('# User\'s Relevant information')

        if start_index != -1 and end_index != -1:
            # 去除从# Similar Answers开始到# User's Relevant information之间的字符串
            cleaned_prompt = prompt[:start_index] + prompt[end_index:]
            return cleaned_prompt
        return prompt

    # 处理User Prompt字段，并写入now User Prompt字段
    df['now User Prompt'] = df['User Prompt'].apply(clean_prompt)

    # 将处理后的数据写回Excel文件
    df.to_excel(file_path, index=False, engine='openpyxl')


# 示例调用
file_path = r'F:\code\jytest\Agent\效果测试集_测试集_plan.xlsx'
process_excel(file_path)
