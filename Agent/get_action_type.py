import pandas as pd
import os
import time
import re
from models_llm import get_model_response


def process_excel(model_name, file_path):
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误：文件 {file_path} 不存在")
            return

        # 检查文件是否可读
        if not os.access(file_path, os.R_OK):
            print(f"错误：没有权限读取文件 {file_path}")
            return

        # 读取Excel文件
        try:
            df = pd.read_excel(file_path)
        except PermissionError:
            print(f"错误：文件 {file_path} 可能正在被其他程序使用，请关闭后重试")
            return
        except Exception as e:
            print(f"读取文件时发生错误：{str(e)}")
            return

        # 检查并创建必要的列
        if 'Response' not in df.columns:
            df['Response'] = ''  # 文本列使用空字符串
        if '实际结果' not in df.columns:
            df['实际结果'] = ''  # 文本列使用空字符串
        if 'Total Time' not in df.columns:
            df['Total Time'] = 0.0  # 数值列使用0.0初始化

        # 设置数据类型
        df['Response'] = df['Response'].astype('object')
        df['实际结果'] = df['实际结果'].astype('object')
        df['Total Time'] = df['Total Time'].fillna(0.0).astype('float64')

        # 创建带日期和模型名称的结果目录
        now = time.strftime("%Y%m%d")
        stats_dir = os.path.join(
            os.path.dirname(file_path),
            f"模型预测结果_{now}"
        )
        os.makedirs(stats_dir, exist_ok=True)

        # 遍历每一行
        for index, row in df.iterrows():
            try:
                print(f"处理第 {index + 1} 条...")
                system_prompt = row['System Prompt']
                user_prompt1 = row['User Prompt1']
                user_prompt2 = row['User Prompt2']

                # 记录开始时间
                start_time = time.time()

                # 动态调用模型
                response = get_model_response(model_name, system_prompt, user_prompt1, user_prompt2)

                # 提取实际结果，确保去除多余的空白字符
                now_resp = re.sub(r'^```json\s*|\s*```$', '', response, flags=re.DOTALL).strip()

                # 记录结束时间和计算耗时
                end_time = time.time()
                total_time = float(end_time - start_time)

                # 更新数据
                df.loc[index, ['Response', '实际结果', 'Total Time']] = [response, now_resp, total_time]
                print(f"实际结果：{now_resp}")
                print(f"耗时：{total_time} 秒")
                print(f"第 {index + 1} 条处理完成")

            except Exception as e:
                print(f"处理第 {index + 1} 条时出错: {str(e)}")
                continue

        # 构建输出文件名
        now = time.strftime("%Y%m%d_%H%M%S")
        input_filename = os.path.basename(file_path)
        output_file_path = os.path.join(
            stats_dir,
            f"{model_name}_{input_filename}_{now}.xlsx"
        )

        # 保存文件
        try:
            df.to_excel(output_file_path, index=False)
            print(f"处理完成，结果已保存至：{output_file_path}")
        except PermissionError:
            print(f"错误：无法保存到 {output_file_path}，文件可能正在被使用或没有写入权限")
            return
        except Exception as e:
            print(f"保存文件时发生错误：{str(e)}")
            return

    except Exception as e:
        print(f"处理过程中发生错误：{str(e)}")


if __name__ == '__main__':
    # 调用示例
    process_excel(
        model_name="gpt_4o",  # 模型名称
        file_path=r"F:\code\jytest\Agent\评测结果_普通测试集_V1.0.0.250321_V4_0317_Qwen-Max.xlsx"
    )
