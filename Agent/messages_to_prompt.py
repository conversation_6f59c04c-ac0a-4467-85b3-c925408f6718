import re

import pandas as pd


def extract_json(text):
    match = re.search(
        r"'role\': \'system\', \'content\': \'(.*?)'}, {'role': 'user', 'content': \"# ROBOT BASIC INFORMATION", text,
        re.DOTALL)
    if match:
        system_prompt = match.group(1)
    else:
        system_prompt = ""
        # print(system_prompt)
    match = re.search(r" {\'role\': \'user\', \'content\': \"(.*?)\"}, {'role': 'user', 'content': ", text, re.DOTALL)
    if match:
        user_prompt1 = match.group(1)
    else:
        user_prompt1 = ""
        # print(result)
    # print(text)
    pattern = r"""
    \{'role':\s*'user',\s*'content':\s*'  # 匹配 user 角色开头部分
    (.*?)                                 # 非贪婪捕获content内容
    '\}                                   # 捕获到右单引号和右花括号
    (?=,\s*\{|\])                        # 确保后面是逗号+新字典 或 列表结尾
    """

    match = re.search(pattern, text, re.DOTALL | re.VERBOSE)
    if match:
        user_prompt2 = match.group(1)
    else:
        user_prompt2 = ""
    # print(user_prompt2)
    return system_prompt, user_prompt1, user_prompt2


if __name__ == '__main__':
    panth = r"C:\Users\<USER>\Downloads\plan_prompt_result_qwen-max_20250319_192214.xlsx"
    pf = pd.read_excel(panth)
    for index, raw in pf.iterrows():
        msg = raw["Messages"]
        system_prompt, user_prompt1, user_prompt2 = extract_json(msg)
        corrected_text = system_prompt.replace("/n", "\n")
        pf[["system Prompt", "User Prompt1", "User Prompt2"]] = pf[
            ["system Prompt", "User Prompt1", "User Prompt2"]].astype('object')
        pf.loc[index, ["system Prompt", "User Prompt1", "User Prompt2"]] = [system_prompt, user_prompt1, user_prompt2]
    pf.to_excel(panth, index=False)
    print("done")
