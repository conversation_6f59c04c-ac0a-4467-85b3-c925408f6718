import pandas as pd
import openpyxl


def convert_to_milliseconds(time_str):
    """
    将"分:秒:帧"格式的时间转换为毫秒
    帧率为30fps，即一帧约等于33.333毫秒
    """
    try:
        # 分割时间字符串
        minutes, seconds, frames = map(int, time_str.split(':'))

        # 计算总毫秒数
        total_ms = (minutes * 60 * 1000) + (seconds * 1000) + (frames * 1000 // 60)
        return total_ms
    except Exception as e:
        raise ValueError(f"时间格式错误，请使用'分:秒:帧'格式。错误信息：{str(e)}")


def calculate_time_difference(time1, time2):
    """
    计算两个时间点之间的差值（毫秒）
    """
    try:
        ms1 = convert_to_milliseconds(time1)
        ms2 = convert_to_milliseconds(time2)

        # 计算时间差
        difference_ms = abs(ms2 - ms1)
        return difference_ms
    except Exception as e:
        print(f"计算失败：{str(e)}")
        return None


def format_milliseconds(ms):
    """
    将毫秒格式化为可读的时间格式
    """
    minutes = ms // (60 * 1000)
    ms = ms % (60 * 1000)
    seconds = ms // 1000
    remaining_ms = ms % 1000

    return f"{minutes}分{seconds}秒{remaining_ms}毫秒"


def format_seconds(ms):
    """
    将毫秒转换为秒（保留3位小数）
    """
    return f"{ms / 1000:.3f}"


def process_excel_times(input_file, output_file):
    """
    批量处理Excel文件中的时间数据
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(input_file, engine='openpyxl')

        # 检查必要的列是否存在
        required_columns = ['T1(人结束说话)', 'T2（VadEnd）', 'T3（PlanEnd）']
        if not all(col in df.columns for col in required_columns):
            print("错误：输入文件中必须包含 'T1(人结束说话)', 'T2（VadEnd）' 和 'T3（PlanEnd）' 列")
            return

        # 初始化结果列表
        diff1 = []  # T2 - T1
        diff1_formatted = []
        diff1_seconds = []

        diff2 = []  # T3 - T2
        diff2_formatted = []
        diff2_seconds = []

        for index, row in df.iterrows():
            try:
                # 计算 T2 - T1
                d1 = calculate_time_difference(str(row['T1(人结束说话)']), str(row['T2（VadEnd）']))
                if d1 is not None:
                    diff1.append(d1)
                    diff1_formatted.append(format_milliseconds(d1))
                    diff1_seconds.append(format_seconds(d1))
                else:
                    diff1.append(0)
                    diff1_formatted.append(None)
                    diff1_seconds.append(None)

                # 计算 T3 - T2
                d2 = calculate_time_difference(str(row['T2（VadEnd）']), str(row['T3（PlanEnd）']))
                if d2 is not None:
                    diff2.append(d2)
                    diff2_formatted.append(format_milliseconds(d2))
                    diff2_seconds.append(format_seconds(d2))
                else:
                    diff2.append(0)
                    diff2_formatted.append(None)
                    diff2_seconds.append(None)

            except Exception as e:
                print(f"处理第 {index + 1} 行时发生错误：{str(e)}")
                diff1.append(0)
                diff1_formatted.append(None)
                diff1_seconds.append(None)
                diff2.append(0)
                diff2_formatted.append(None)
                diff2_seconds.append(None)
        df['T2T1时间差(毫秒)'] = diff1
        # df['T2-T1(秒)'] = diff1_seconds
        # df['T2-T1(格式化)'] = diff1_formatted
        df['T3T2时间差(毫秒)'] = diff2
        # df['T3-T2(秒)'] = diff2_seconds
        # df['T3-T2(格式化)'] = diff2_formatted

        # 保存结果
        df.to_excel(output_file, index=False, engine='openpyxl')
        print(f"处理完成，结果已保存到：{output_file}")

    except Exception as e:
        print(f"处理Excel文件时发生错误：{str(e)}")


def analysis_time_cost(file_path: str):
    # 读取Excel文件
    df = pd.read_excel(file_path)
    df = df[(df['T2T1时间差(毫秒)'] != 0) & (df['T3T2时间差(毫秒)'] != 0)]
    # 需要转换为毫秒的列
    ms_convert_cols = [
        "asr_cost_time",
        "wakeup_cost_time",
        "agent_core_total_time",
        "plan_total_cost_time",
        "agent_core_load_context_time",
        "agent_core_load_action_time",
        "agent_core_embedded_time",
        "agent_core_call_summary_llm_time",
        "agent_core_select_few_shot_time",
        "agent_core_call_select_action_llm_time",
        "agent_core_load_user_profile_time",
    ]

    # 将这些列的值乘以1000（转换为毫秒）
    for col in ms_convert_cols:
        df[col] = df[col] * 1000

    # 过滤掉重试的记录
    df = df[df["agent_core_llm_retry_count"] == 0]
    sample_count = len(df)

    # 准备结果数据
    results = []

    # 核心处理时间分析
    core_time_cols = [
        "T2T1时间差(毫秒)",
        "T3T2时间差(毫秒)",
        "plan_end_timestamp-vad_end_timestamp（毫秒）",
        "asr_cost_time",
        "wakeup_cost_time",
        "agent_core_total_time",
        "plan_total_cost_time",
    ]

    for col in core_time_cols:
        # 过滤0值
        valid_data = df[df[col] > 0][col]
        valid_count = len(valid_data)

        results.append(
            {
                "指标": col,
                "类别": "核心耗时",
                "样本数": valid_count,
                "平均时间(ms)": valid_data.mean(),
                "最大时间(ms)": valid_data.max(),
                "最小时间(ms)": valid_data.min(),
                "P70时间(ms)": valid_data.quantile(0.7),
                "P90时间(ms)": valid_data.quantile(0.9),
                "P95时间(ms)": valid_data.quantile(0.95),
                "标准差(ms)": valid_data.std(),
            }
        )

    # Agent Core 细分时间分析
    agent_detail_cols = [
        "agent_core_load_context_time",
        "agent_core_load_action_time",
        "agent_core_embedded_time",
        "agent_core_call_summary_llm_time",
        "agent_core_select_few_shot_time",
        "agent_core_call_select_action_llm_time",
        "agent_core_load_user_profile_time",
    ]

    for col in agent_detail_cols:
        # 过滤0值
        valid_data = df[df[col] > 0][col]
        valid_count = len(valid_data)

        results.append(
            {
                "指标": col,
                "类别": "Agent Core细分",
                "样本数": valid_count,
                "平均时间(ms)": valid_data.mean(),
                "最大时间(ms)": valid_data.max(),
                "最小时间(ms)": valid_data.min(),
                "P70时间(ms)": valid_data.quantile(0.7),
                "P90时间(ms)": valid_data.quantile(0.9),
                "P95时间(ms)": valid_data.quantile(0.95),
                "标准差(ms)": valid_data.std(),
            }
        )

    # 转换为DataFrame
    results_df = pd.DataFrame(results)

    # # 任务类型分组分析
    # task_group = (
    #     df.groupby("基础/高级任务规划")["plan_total_cost_time"]
    #     .agg(["mean", "count"])
    #     .reset_index()
    # )
    # task_group.columns = ["任务类型", "平均耗时(ms)", "样本数"]

    # 保存到原始Excel文件的新sheet中
    with pd.ExcelWriter(file_path, engine="openpyxl", mode="a", if_sheet_exists="replace") as writer:
        # 写入时间分析结果
        results_df.to_excel(writer, sheet_name="时间分析", index=False)

        # 写入任务类型分析
        # task_group.to_excel(writer, sheet_name="任务类型分析", index=False)

        # 写入LLM重试统计
        pd.DataFrame(
            [
                {
                    "平均重试次数": df["agent_core_llm_retry_count"].mean(),
                    "最大重试次数": df["agent_core_llm_retry_count"].max(),
                }
            ]
        ).to_excel(writer, sheet_name="LLM重试统计", index=False)

    print(f"分析结果已保存到原始文件的新sheet中: {file_path}")


if __name__ == '__main__':

    # 修改主程序入口
    try:
        input_file = r'F:\code\jytest\Agent\耗时测试_V1.0.0.250321.O_0324_英文_V1_0324 (5).xlsx'  # 输入文件名
        output_file = r'F:\code\jytest\Agent\result.xlsx'  # 输出文件名
        process_excel_times(input_file, output_file)
        analysis_time_cost(output_file)
    except Exception as e:
        print(f"程序执行失败：{e}")
