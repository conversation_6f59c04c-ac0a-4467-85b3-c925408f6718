# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : file_load.py
<AUTHOR> <EMAIL>
@Time   : 2023-11-08 19:39:13
@Version: v1.0
"""
import os

from flask import send_from_directory
from tool.gen_jsonl import ConvertData
from services.sensitive.check_sensitive_for_gen import upload_sensitive_check_task
from test_conifg.path_config import UPLOAD_PATH, DOWNLOAD_PATH
from tool.util import *
from concurrent.futures.thread import ThreadPoolExecutor
from tool.shumei import upload_sensitive_shumei

ex = ThreadPoolExecutor(max_workers=1)


def upload_file(file, task) -> tuple:
    file_name = file.filename
    if not os.path.exists(UPLOAD_PATH):
        os.mkdir(UPLOAD_PATH)
    try:
        file_path = "{file_path}{sep}{file_name}".format(file_path=UPLOAD_PATH, file_name=file_name, sep=os.sep)
        file.save(file_path)
        if task == "gen":
            out_f = "sensitive_gen_answer_{T}.xlsx".format(T=get_time())
            f_type = ""
            if str(file_name).__contains__("xls"):
                fs = open_xlsx_for_path(file_path)
                f_type = "xls"
            elif str(file_name).__contains__("json"):
                fs = open(file_path, 'r', encoding='utf-8')
                f_type = "json"
            ex.submit(upload_sensitive_check_task, fs, f_type, out_f)
            return True, "上传成功", out_f
        elif task == "convert_json":
            convert = ConvertData(file_path)
            ex.submit(convert.gen_data)
            return True, "上传成功", "data.zip"
        else:
            return False, "没有识别到任务", 0

    except Exception:
        return False, traceback.format_exc(), -1


def download_file(file_name="sensitive.jsonl") -> tuple:
    try:
        file = send_from_directory(DOWNLOAD_PATH, file_name, as_attachment=True)
        return True, file
    except Exception:
        return False, traceback.format_exc()


def upload_shumei(file) -> tuple:
    file_name = file.filename
    if not os.path.exists(UPLOAD_PATH):
        os.mkdir(UPLOAD_PATH)
    try:
        file_path = "{file_path}{sep}{file_name}".format(file_path=UPLOAD_PATH, file_name=file_name, sep=os.sep)
        file.save(file_path)

        out_f = "shumei_illegal_{T}.xlsx".format(T=get_time())
        f_type = ""
        if str(file_name).__contains__("xls"):
            fs = open_xlsx_for_path(file_path)
            f_type = "xls"
        elif str(file_name).__contains__("json"):
            fs = open(file_path, 'r', encoding='utf-8')
            f_type = "json"
        ex.submit(upload_sensitive_shumei, fs, f_type, out_f)
        return True, "上传成功", out_f

    except Exception:
        return False, traceback.format_exc(), -1

#
# if __name__ == '__main__':
#     file_path="/Users/<USER>/workspace/liangwei/sensitive/测试的数据/wangxinban_merge.xlsx"
#     out_f = "wangxinban_merge_{T}.xlsx".format(T=get_time())
#
#     fs = open_xlsx_for_path(file_path)
#     f_type = "xls"
#
#     upload_sensitive_check_task(fs, f_type, out_f)