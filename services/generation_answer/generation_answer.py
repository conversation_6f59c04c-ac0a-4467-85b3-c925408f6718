# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : generation_answer.py
<AUTHOR> <EMAIL>
@Time   : 2023-11-01 18:30:45
@Version: v1.0
"""
import datetime
import requests
import json
from tool.util import get_time, flush_key
from tool.logger import log
from test_conifg import config as conf
from tool.common import load_yml
from test_conifg.path_config import REQUEST_DATA

req_data = load_yml(REQUEST_DATA)


def request(question: str, key: str, gen=True, env="test", **kwargs) -> dict:
    """
    :param question:
    :param gen:  False 调用chat对话，True 调用创作中心
    :param env:
    :param key:
    :return:
    """
    header = req_data["gen_answer"]["headers"]
    header["Orionstar-Api-Key"] = flush_key(env) if not key else key
    payload = req_data["gen_answer"][env]["payload"]
    payload["query_text"] = question
    payload["session_id"] = "test{}".format(get_time())
    if gen:
        # 创作中心标识，添加创作中心key
        payload["category"] = "tpl_chat"
        dict(payload).__delitem__("character_id")
    url = req_data["gen_answer"][env]["url"]
    response_data = requests.post(url, headers=header, data=json.dumps(payload))
    if response_data.status_code == 200:
        res = response_data.json()
        if kwargs:
            res["extra"] = kwargs
        return res
    else:
        log.info("接口返回非200，接口调用失败：{}".format(response_data.text))
        return {"msg": "接口调用返回非200", "ret": response_data.status_code}


def stream(question: str, gen=None, env="test") -> dict:
    """

    :param question:
    :param gen:  False 调用chat对话，True 调用创作中心
    :param env:
    :return:
    """
    header = req_data["gen_answer"]["headers"]
    header["Orionstar-Api-Key"] = conf.api_key
    payload = req_data["gen_answer"][env]["payload"]
    payload["query_text"] = question
    payload["session_id"] = "test{}".format(get_time())
    payload["stream"] = 1
    if gen:
        #   判断有创作中心标识，添加创作中心key
        payload["category"] = "tpl_chat"
        dict(payload).__delitem__("character_id")
    url = req_data["gen_answer"][env]["url"]
    response_data = requests.post(url, headers=header, data=json.dumps(payload), stream=True)
    if response_data.status_code == 200:
        for raw_item in response_data.iter_lines(chunk_size=512):
            print(f"\n============================")
            print("{}-->{}".format(datetime.datetime.now(), bytes(raw_item).decode("utf-8")))

            # item = json.loads(raw_item)
            #   TODO 处理流返回的整体请求
            # return item
    else:
        log.info("接口返回非200，接口调用失败：{}".format(response_data.text))
        return {"msg": "接口调用返回非200", "ret": response_data.status_code}
