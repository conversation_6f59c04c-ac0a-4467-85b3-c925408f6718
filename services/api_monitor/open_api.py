# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : open_api.py
<AUTHOR> <EMAIL>
@Time   : 2024-01-26 10:35:51
@Version: v1.0
"""
import random, os
import requests, json, itertools
import pandas as pd
from tool.util import get_time
from tool.common import load_yml
from test_conifg.path_config import REQUEST_DATA, MODEL_OUTPUT_PATH

req_data = load_yml(REQUEST_DATA)["open_api_monitor"]
service = "open_api"
output_path = "{prefix_path}{sep}{service}{sep}{T}".format(prefix_path=MODEL_OUTPUT_PATH, sep=os.sep,
                                                           T=get_time(format="%Y%m%d"), service=service)

if not os.path.exists(output_path):
    os.makedirs(output_path)


def check_normal(param=None, env="test") -> dict:
    url = req_data[env]["url"]
    headers = req_data[env]["headers"]
    # print(f"{url}\n{headers}\n{json.dumps(param,ensure_ascii=False)}")
    response_data = requests.post(url, headers=headers, data=json.dumps(param))
    if response_data.status_code == 200:
        data_dict = response_data.json()
        return_type = data_dict.get("data").get("extend_info").get("ialgo_extend_info").get("return_type", "")
        answer = data_dict.get("data").get("answer_text")
        success = 1 if answer and return_type else 0
        return {"return_type": return_type, "param": param, "answer": answer, "is_success": success}
    else:
        print(f"非流式请求返回HTTPCODE非200{response_data}")
        return {"return_type": "", "answer": "", "param": param}


def check_stream(param=None, env="test") -> dict:
    url = req_data[env]["url"]
    headers = req_data[env]["headers"]
    result_dic = {}
    response_data = requests.post(url, headers=headers, data=json.dumps(param), stream=True)
    # print(f"query{param}\nanswer:{response_data}")
    i = 0
    if response_data.status_code == 200:
        answer = ""
        first_package = True
        for raw_item in response_data.iter_lines(chunk_size=512):
            #   只测试首包打开判断
            # if i>1:break
            if i == 0:
                i += 1
                continue
            # print(f"\n============================")
            raw_item = raw_item[6:]
            res = bytes(raw_item).decode("utf-8")
            try:
                data_dict = json.loads(res)
            except BaseException:
                continue
            if first_package:
                first_package = False
                return_type = data_dict.get("data").get("extend_info").get("ialgo_extend_info").get("return_type",
                                                                                                    "")
                result_dic["return_type"] = return_type
                if not return_type:
                    print(f"return_type 空,包数据为{res}")
            answer_text = data_dict.get("data").get("answer_text")
            answer += answer_text
            i += 1
        result_dic["param"] = param
        result_dic["answer"] = answer
        result_dic["is_success"] = 1 if answer and return_type else 0
        return result_dic


def get_param(param):
    param["role"] = {"type": "universal"}
    param["query_text"] = "你好"
    param["session_id"] = "test_id{}".format(get_time())
    param["nlu_switch"] = {"enable_get_contact_info": 1}
    param["talkingstyle_id"] = "rigorous"
    param["max_history_turns"] = random.choice([1, 2, 3, 4])
    param["temperature"] = random.choice([0, 1, 2, 3, 4, 5, 6, 7, 8, 9])
    param["elaborate_switch"] = random.choice([0, 1])
    param["citations_switch"] = random.choice([0, 1])
    param["citations_switch"] = random.choice([0, 1])
    return param


def run_stream(param_list, env="test"):
    result_list = []
    x = 0
    for accurate_switch in [0, 1]:
        for v in ["v1", "v2"]:
            for role in ["creator", "writer", "universal", "legal_consultant"]:
                for param in param_list:
                    if x > 1: break
                    param = get_param(param)
                    param["stream"] = 1
                    param["ctprofile_id"] = role
                    param["accurate_switch"] = accurate_switch
                    param["retrieval_strategy"] = v
                    result_dict = check_stream(param=param, env="test")
                    x += 1
                    print(f"执行stream{x}次")
                    result_list.append(result_dict)
    df = pd.DataFrame(result_list)
    file_path = "{output_path}{sep}stream_monitor_result{T}.xlsx".format(output_path=output_path, sep=os.sep,
                                                                         T=get_time())
    df.to_excel(file_path, index=False)
    print(f"save to excel success!{file_path}")


def run_normal(param_list, env="test"):
    result_list = []
    i = 0
    for accurate_switch in [0, 1]:
        for v in ["v1", "v2"]:
            for role in ["creator", "writer", "universal", "legal_consultant"]:
                for param in param_list:
                    if i > 1: break
                    param = get_param(param)
                    param["stream"] = 0
                    param["ctprofile_id"] = role
                    param["accurate_switch"] = accurate_switch
                    param["retrieval_strategy"] = v
                    result_dict = check_normal(param=param, env=env)
                    i += 1
                    print(f"执行normal{i}次")
                    result_list.append(result_dict)
    df = pd.DataFrame(result_list)
    file_path = "{output_path}{sep}normal_monitor_result{T}.xlsx".format(output_path=output_path, sep=os.sep,
                                                                         T=get_time())

    df.to_excel(file_path, index=False)
    print(f"save to excel success!{file_path}")


if __name__ == '__main__':
    env = "test"
    param_list_produce = list(itertools.product([0, 1], repeat=2))
    param_list = [{"follow_up_switch": combo[0], "trapped_switch": combo[1]} for combo in param_list_produce]
    run_normal(param_list, env="test")
    run_stream(param_list, env="test")
