# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : wangxinban_api_monitor.py
<AUTHOR> <EMAIL>
@Time   : 2024-02-04 17:01:44
@Version: v1.0
"""
import os, sys

BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
print(BASE_DIR)
sys.path.append(BASE_DIR)
from test_conifg.path_config import DOWNLOAD_PATH
from test_conifg.config import ip, port
from services.monitor.api_data_analysis import repeat_convert_xlsx
import redis
import json
from tool.util import get_time, send_card_message, send_message

"""
网信办接口测试数据
"""
#   查询大于query_date的数据
query_date = get_time(time_res="T", format="%Y-%m-%d")
#   输出文件
output_file = f"wangxinban_dialogues_{query_date}.json"
API_KEYS = [
    "Bearer umZYy6sWEAeyH89xPY2pQSu1lr8kUg",
    "Bearer KJ5TYFNfag8v5xEuWKfPy9KfB2nwe0",
    "Bearer 8e4WPFr4HM3lJtn87NXtUaFokGOJzG",
    "Bearer cv3jZeVzRiavD4CvxpW3s2Kh1npd17",
    "Bearer jxahxzCYgDPWpvco6hV7c1SPCpLnbt",
    "Bearer Na7fPpufzBWYIPmObjr2CJEBdHaPtW",
    "Bearer zI6kjO4yQkyAfsphWWwWqAWd0kMmHw",
    "Bearer yPIoo0SHUgtgD5pevbzXWMvXdtrrgd",
    "Bearer lflLasfdlZ1AMi5xcmkOhpp7ZLmtrY",
    "Bearer LTkAPwglQ11CAo6bJoc0991wDJmrWU",
    # "Bearer pw9SwJLvLLeuarN1W3u9X8fu3oZZvG",

]

PREFIX = "wangxinban:qa_history:"

redis = redis.Redis(
    host="juyan-autolm-product.b6uduc.ng.0001.cnw1.cache.amazonaws.com.cn",
    port=6379,
    db=2,
    decode_responses=True,
)

dialogues = {}
for api_key in API_KEYS:
    dialogue_key = PREFIX + api_key
    results = redis.lrange(dialogue_key, 0, -1)
    filtered_results = []
    for ret in results:
        ret = json.loads(ret)
        date_time = ret["date_time"]
        if date_time < query_date:
            continue
        filtered_results.append(ret)
    dialogues[api_key] = filtered_results

with open(output_file, "w") as f:
    json.dump(dialogues, f, indent=4, ensure_ascii=False)
print(f"数据已保存到{output_file}")
send_flag = False
real_data = [x for dialogues in dialogues.values() for x in dialogues if len(x) > 0]

send_url = "https://open.feishu.cn/open-apis/bot/v2/hook/0299ae09-dd47-4920-845f-bf13171c3401"

if real_data.__len__() > 0:
    down_load_file = repeat_convert_xlsx(output_file)
    path = "/api/download/{}".format(down_load_file)
    title = "网信办抽查数据通知!"
    down_url = "http://{ip}:{port}{path}".format(ip=ip, port=port, path=path)

    send_card_message(send_url=send_url,title=title,
                      msg_data=f"{query_date}发现网信办api测试数据{len(real_data)}条\n", down_url=down_url)
else:
    send_message(send_url=send_url, msg=f"{query_date}未发现网信办api测试数据!")
