# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : resolve_data.py
<AUTHOR> <EMAIL>
@Time   : 2024-01-03 19:24:32
@Version: v1.0
"""
import json,os
from tool.util import get_time, read_xlsx_to_dict

import pandas as pd
from test_conifg.path_config import DOWNLOAD_PATH

def repeat_convert_xlsx(file_name):
    """
    网信办接口调用记录去重,转换成xlsx
    :return:
    """
    f = open(file_name)
    lines = f.read()
    data = json.loads(lines)
    repeat = []
    count = 0  # 不去重总共多少数据
    no_repeat_data = []
    for line in data.get("Bearer umZYy6sWEAeyH89xPY2pQSu1lr8kUg"):
        count += 1
        query = line.get("dialogue")[0].get("content")
        answer = line.get("answer")
        # content = line.get("dialogue")[0].get("content") + line.get("answer")
        if answer:
            content = query + answer
        else:
            print(f"answer为空->{query}")
            continue
        if content not in repeat:
            repeat.append(content)
            x_data = {"query": query, "answer": answer, "date_time": line.get("date_time")}
            if content.__contains__("存在违规内容"):
                x_data["type"] = "已拦截"
            else:
                x_data["type"] = "未拦截"
            no_repeat_data.append(x_data)
    no_repeat_data_df = pd.DataFrame(no_repeat_data)
    res_file_name = "{}_no_repeat_wangxinban.xlsx".format(get_time(format="%Y-%m-%d"))
    res_excel = DOWNLOAD_PATH + os.sep + res_file_name
    no_repeat_data_df.to_excel(res_excel, index=False)
    print(f"总共有{count}数据")
    print(f"去重后有{len(no_repeat_data)}数据")
    return res_file_name


def data_is_produce():
    """
    网信办数据是否为猎户提供的case
    :return:
    """
    net_data = read_xlsx_to_dict(file_name="2024-01-22_no_repeat_wangxinban.xlsx")
    net_query_list = []
    for data in net_data:
        net_query_list.append(str(data.get("query")).strip())
    produce_data = read_xlsx_to_dict(
        file_name="/Users/<USER>/workspace/liangwei/sensitive/大模型备案相关材料/测试题集_0109.xlsx", sheet="全量测试题集")
    produce_query_list = []
    for data in produce_data:
        produce_query_list.append(str(data.get("输入内容")).strip())
    is_produce = list(set(produce_query_list).intersection(set(net_query_list)))
    print(is_produce)
    print(f"交集数据有{len(is_produce)}条")


# if __name__ == '__main__':
#     # data_is_produce()
#     repeat_convert_xlsx()
