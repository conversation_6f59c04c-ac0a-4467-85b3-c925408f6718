# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : wangxinban_monitor.py
<AUTHOR> <EMAIL>
@Time   : 2024-01-20 11:51:20
@Version: v1.0
"""

"""
网信办聚言平台测试数据监控
"""

import os, sys
#   优化代码
try:
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    print(BASE_DIR)
    sys.path.append(BASE_DIR)
except Exception:
    pass
from tool.common import get_sql, load_yml
from tool.DB import DB
from test_conifg.path_config import SQL_PATH, DOWNLOAD_PATH
from tool.util import get_time, send_message, send_card_message
import pandas as pd
from test_conifg.config import ip, port


sql_data = load_yml(SQL_PATH)

#   每天晚上22点执行脚本
wxb_query = get_sql(sql_data, "query_wxb_log", start=get_time(time_res="-10h", format="ms") * 1000,
                    end=get_time(format="ms") * 1000)

db_res = DB.query_all(wxb_query, env="wxb")
send_url = "https://open.feishu.cn/open-apis/bot/v2/hook/0299ae09-dd47-4920-845f-bf13171c3401"
if db_res:
    df = pd.DataFrame(db_res)
    file_name = "{t}_wangxinban_test_data.xlsx".format(t=get_time(format="%Y-%m-%d"))
    out_file = "{outpath}{sep}{file}".format(outpath=DOWNLOAD_PATH, sep=os.sep, file=file_name)
    df.to_excel(out_file)
    print(f"save data to {out_file}")
    title = "{}发现网信办web测试数据{}条".format(get_time(format="%Y-%m-%d"), len(db_res))
    path = "/api/download/{file}".format(file=file_name)
    url = "http://{ip}:{port}{path}".format(ip=ip, port=port, path=path)
    send_card_message(send_url=send_url, title=title, down_url=url)
else:
    send_message(send_url=send_url, msg="{}未发现网信办web测试数据!".format(get_time(time_res="T", format="%Y-%m-%d")))
