# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : runCase.py
<AUTHOR> <EMAIL>
@Time   : 2024-04-19 16:18:31
@Version: v1.0
"""


import pandas as pd
from datetime import datetime
# import time
from jsonpath_ng import parse
import openpyxl
import requests
import traceback
import json
import os
import sys

BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
print(BASE_DIR)
sys.path.append(BASE_DIR)


def get_today():
    return datetime.now().strftime("%Y%m%d%H%M%S")


book = openpyxl.Workbook()
sheet = book.active
sheet.append(
    ["desc", "url", "method", "data", "response", "stream", "verify", "verify_method", "status", "answer",
     "note", "case_type", "expect_res"])

report_structure = {"request_id": "", "event": "", "code": 0, "msg": "", "outputs": {"answer": ""}}
report_structure_no_stream = {"request_id": "", "code": 0, "msg": "", "outputs": {"answer": ""}}

simulate_chat_structure = {"code": 0, "msg": "", "data": {"event": "", "answer_text": "", "end": 0}, "request_id": ""}
teach_course_qa = {"code": "0", "msg": "", "data": {"event": "", "answer_text": ""}, "request_id": ""}
recommend_structure = {"msg": "success", "code": 0,
                       "data": {"return_type": "recommend", "return_data": {"recommend": []}, "is_last_event": True},
                       "request_id": ""}
teach_pic_check_structure = {"code": 0, "msg": "", "request_id": "xxxx", "data": {"check_result": 2,
                                                                                  "check_msg": "未能满足所有要求:单个人物侧面照片、单个人物背面照片"}}
chat_evaluate_structure = {"code": 0, "msg": "",
                           "data": {"score": 85, "review": "考生整体符合规范。需要注意结束服务后应该礼貌道别。"},
                           "request_id": "xxxx"}
qa_evaluate_structure = {"code": 0, "msg": "", "data": {"score": 85,
                                                        "review": "整体要点回答到位，但遗漏了一个点，需要询问客人是否有邮寄服务商的特别需求。"},
                         "request_id": "xxxxxx"}


def case():
    """
    执行case，结果文件在当前目录，只需要修改case_path路径
    case url:https://orionstar.feishu.cn/sheets/RduHsoPXAhbGSEtXtUjcak8wnih
    :return:
    """
    case_path = "data.xlsx"
    # 定义新文件夹的名称
    folder_name = "api_result"

    # 检查文件夹是否已存在，如果不存在则创建新文件夹
    if not os.path.exists(folder_name):
        os.makedirs(folder_name)
    res_file = f"{folder_name}/{datetime.now()}_result.xlsx"
    index = 1
    #   配置host体验demo
    boss_host = "http://self-hosting.chatmax.net:100"
    #   配置host体验demo
    teach_host = "http://self-hosting.chatmax.net:100"
    xls = pd.read_excel(case_path, sheet_name="Sheet1", keep_default_na=False)
    index = 1
    for rows in xls.iterrows():
        #   从低71条开始执行
        # if index < 71:
        #     index += 1
        #     continue
        headers = {}
        answer = ""
        note = ""
        desc = rows[1].get("desc")
        m = rows[1].get("method")
        payload = rows[1].get("data")
        service = rows[1].get("service")
        case_type = rows[1].get("type")
        expect_res = rows[1].get("expect_res")
        print(f"{datetime.now()}执行第{index}条:->{desc}")
        if service == "boss":
            headers = {"content-Type": "application/json",
                       "orionstar-api-key": "bz0740a2a2323068f6f628d9854445bf24z59e837ecbf5b57bb524b9af538cf483f"}
            url = boss_host + rows[1].get("url")
        else:
            headers["content-Type"] = "application/json"
            headers[
                "Referer"] = "https://test-teaching-demo.orionstar.com/examination/1/a4c90d95-4696-47fa-9b47-76dc1af3b589"
            #   配置host体验demo的数据
            headers[
                "Referer"] = "http://self-hosting.chatmax.net:3001/examination/T2-010/6c3443fd-ca38-4874-8ea1-6dfa267b7741"
            headers["orionstar-api-key"] = "bz0740a2a2323068f6f628d9854445bf24z59e837ecbf5b57bb524b9af538cf483f"

            url = teach_host + rows[1].get("url")
        verify = rows[1].get("verify")
        stream = rows[1].get("stream")
        business_type = rows[1].get("business")
        #   校验方法
        verify_method = rows[1].get("verify_method")
        if not stream:
            status, results = request_verify(
                url=url,
                method=m,
                payload=payload,
                verify=verify,
                headers=headers,
                verify_type=verify_method,
                business_type=business_type,
                except_res=expect_res,
                case_type=case_type
            )
        else:
            results = []
            try:
                results = request_stream(url=url, method=m, payload=payload, headers=headers)
            except BaseException:
                print(f"获取流式请求接口失败\n{traceback.format_exc()}")
                note = "调用流式请求失败"
                sheet.append([index, desc, url, m, payload, json.dumps(results, ensure_ascii=False), stream, verify,
                              verify_method, "fail", answer, note, case_type, expect_res])
                continue
            if business_type == "report":
                if verify:
                    status = "fail"
                    answer = results
                    res = my_verify(results, verify, verify_method)
                    if res:
                        status = "success"
                else:
                    #   校验数据格式
                    status_structure = my_verify(results, report_structure, "structure")
                    #   校验event
                    status_event, answer, note = check_event("report", results)
                    status = "success" if status_structure and status_event and not note else "fail"
                    if not status_structure:
                        note = note + "数据结构校验失败\n"
                    if not status_event:
                        note = note + ""
                    if len(answer) < 1:
                        note = note + "没有对应的洞察结果\n"
                # sheet.append([index, desc, url, m, payload, json.dumps(results, ensure_ascii=False), stream, verify,verify_method, status,answer,note])
            elif business_type == "chat":
                if verify:
                    status = "fail"
                    answer = results
                    res = my_verify(results, verify, verify_method)
                    if res:
                        status = "success"
                else:
                    status_event, answer, note = check_event("chat", results)
                    status = "fail"
                    if not note and status_event:
                        status = "success"
            elif business_type == "simulate_chat":
                if verify:
                    status = "fail"
                    answer = results
                    res = my_verify(results, verify, verify_method)
                    if res:
                        status = "success"
                else:
                    #   校验数据格式
                    status_structure = my_verify(results, simulate_chat_structure, "structure")
                    status_event, answer, note = check_event("simulate_chat", results)
                    if not status_structure:
                        note = note + "数据结构校验失败\n"
                    status = "success" if status_structure and status_event and answer else "fail"

            elif business_type == "teach_course_qa":
                if verify:
                    status = "fail"
                    answer = results
                    res = my_verify(results, verify, verify_method)
                    if res:
                        status = "success"
                else:
                    #   校验数据格式
                    status_structure = my_verify(results, teach_course_qa, "structure")
                    status_event, answer, note = check_event("simulate_chat", results)
                    if not status_structure:
                        note = note + "数据结构校验失败\n"
                    status = "success" if status_structure and status_event and answer else "fail"
        if not isinstance(answer, str):
            answer = json.dumps(answer, ensure_ascii=False)
        sheet.append(
            [desc, url, m, payload, json.dumps(results, ensure_ascii=False), stream, verify, verify_method,
             status, answer, note, case_type, expect_res])
        index += 1
    book.save(res_file)
    print(f"case结果保存在:{res_file}")


def check_event(event_type: str, res: list) -> tuple:
    #   判断res是list类型才继续执行
    if not isinstance(res, list):
        return False, res, "res不是list类型"
    if event_type == "report":
        note = ""
        answer = ""
        stop_flag = False
        for r in res:
            answer += r.get("outputs", {}).get("answer", "")
            if r.get("code") != 0:
                note += r.get("msg", "")
            if r.get("event") == "stop":
                stop_flag = True

        if not stop_flag:
            note += "event没有stop标记"
        if not note:
            return True, answer, note
        else:
            return False, answer, note

    if event_type == "chat":
        intent_list = []
        chat_node = {
            "progress": False,
            "insight": True,
            "recommend": False,
            "end": False,
            "chart": False,
            "rag": True,
            "report": True,
            "reply": True,
            "tips": True
        }
        #   记录问答结果
        answer = ""
        note = ""
        flag = True
        for r in res:
            try:
                return_type = r.get("data", {}).get("return_type", "")
            except BaseException:
                print(f"chat获取data异常{r}")
                answer = r
                note = "数据结构异常"
                return False, answer, note
            #   判断data中return_type指定的数据在return_data中有对应的key数据
            return_data = r.get("data", {}).get("return_data", {}).get(return_type, {})
            is_last_event = r.get("data", {}).get("is_last_event", "")
            if return_type != "end" and return_type in chat_node and return_data and not is_last_event:
                #   校验推荐问题有3个
                if return_type == "recommend":
                    if len(return_data) != 3:
                        note += f"{return_type}推荐问题数量不是3个\n"
                        flag = False
                #   校验洞察有数据
                elif return_type == "insight":
                    if return_data.get("text"):
                        answer += return_data.get("text")
                    else:
                        flag = False
                        note += f"{return_type}洞察数据空\n"
                elif return_type == "reply":
                    if return_data.get("text"):
                        answer += return_data.get("text")
                    else:
                        flag = False
                        note += f"{return_type}反问内容空\n"
                elif return_type == "report":
                    if return_data.get("text"):
                        answer += return_data.get("text")
                    else:
                        flag = False
                        note += f"{return_type}内容空\n"

                elif return_type == "tips":
                    if return_data:
                        answer += return_data
                    else:
                        flag = False
                        note += f"{return_type}兜底内容空\n"

                #   校验chart success阶段有数据
                elif return_type == "chart":
                    if return_data.get("status") == "success":
                        echart_data = return_data.get("echart_data")
                        if echart_data:
                            data_type = echart_data[0].get("data_type")
                            echart_data = echart_data[0].get("data")
                            if data_type == "bar" or data_type == "line":
                                if echart_data:
                                    try:
                                        if not echart_data.get("categories") or not echart_data.get("series"):
                                            note += f"{return_type}柱状图or折线图数据错误\n"
                                            flag = False
                                    except BaseException:
                                        flag = False
                                        note = "echart 数据包错误"
                                        print("echart 数据包错误")
                            elif data_type == "pie":
                                if echart_data:
                                    for data_dict in echart_data:
                                        if not data_dict.get("value"):
                                            flag = False
                                            note += f"{return_type}饼图 数据错误\n"

                elif return_type == "rag":
                    answer_text = return_data.get("answer_text", "default")
                    if answer_text or answer_text == "default":
                        answer += return_data.get("answer_text")
                    else:
                        flag = False
                        note += f"{return_type}获取数据失败\n"
                elif return_type == "progress":
                    intent_list.append(return_data)
                else:
                    return False, r, "不存在的node"
        return flag, answer, note

    #   模拟对话
    if event_type == "simulate_chat":
        note = ""
        answer = ""
        for index, r in enumerate(res):
            answer += r.get("data", {}).get("answer_text", "")
            #   判断最后一个包
            if index == len(res) - 1:
                if "stop" != r.get("data", {}).get("event"):
                    note += "event没有stop，对话结束"
                # if not r.get("data", {}).get("end"):
                #     note += "最后一个包，end不是1"
            # elif r.get("data", {}).get("end", "无") != "无":
            #     note += "非尾包存在end字段"
            if not r.get("code"):
                note += r.get("msg", "")
        if answer:
            return True, answer, note
        else:
            return False, answer, note


def request_verify(url, method, payload, headers, verify, verify_type, business_type, except_res, case_type):
    try:
        res = requests.request(url=url, method=method, json=json.loads(payload), headers=headers).json()
        verify_res = my_verify(res, verify, verify_type)
        structure_res = True
        if business_type == "report" and except_res == "success" and case_type != "反向":
            structure_res = my_verify(res, report_structure_no_stream, "structure")
        elif business_type == "recommend" and except_res == "success" and case_type != "反向":
            structure_res = my_verify(res, recommend_structure, "structure")
        elif business_type == "teach_pic_check" and except_res == "success" and case_type != "反向":
            structure_res = my_verify(res, teach_pic_check_structure, "structure")
        elif business_type == "chat_evaluate" and except_res == "success" and case_type != "反向":
            structure_res = my_verify(res, chat_evaluate_structure, "structure")
        elif business_type == "qa_evaluate" and except_res == "success" and case_type != "反向":
            structure_res = my_verify(res, qa_evaluate_structure, "structure")
        if verify_res and structure_res:
            return "success", res
        else:
            return "fail", res
    except BaseException:
        print(f"请求异常:{traceback.format_exc()}")
        return "fail", "接口调用异常"


def my_verify(res, expect_data, v_type):
    if isinstance(res, list):
        res = res[0]
    if isinstance(expect_data, str):
        expect_data = json.loads(expect_data)
    if v_type == "eq":
        return res == expect_data
    elif v_type == "json_path_eq":
        flag = True
        for expect in expect_data:
            key = expect.get("key")
            value = expect.get("value")
            jsonpath_expr = parse(key)
            matches = [match.value for match in jsonpath_expr.find(res)]
            if not matches or not matches[0] == value:
                return False
        return flag
    elif v_type == "json_path_has":
        flag = True
        for expect in expect_data:
            key = expect.get("key")
            jsonpath_expr = parse(key)
            matches = [match.value for match in jsonpath_expr.find(res)]
            if not matches:
                return False
        return flag
    elif v_type == "json_path_no":
        flag = True
        for expect in expect_data:
            key = expect.get("key")
            jsonpath_expr = parse(key)
            matches = [match.value for match in jsonpath_expr.find(res)]
            if matches:
                return False
        return flag
    elif v_type == "json_path_len":
        flag = True
        for expect in expect_data:
            key = expect.get("key")
            value = expect.get("value")
            jsonpath_expr = parse(key)
            matches = [match.value for match in jsonpath_expr.find(res)]
            matches_len = len(matches[0])
            if not matches or matches_len != value:
                return False
            return flag
    elif v_type == "structure":
        if isinstance(res, list):
            for r in res:
                if not my_verify(res=r, expect_data=expect_data, v_type=v_type):
                    return False
            return True
        return compare_dicts_structure(dict1=expect_data, dict2=res)


def compare_dicts_structure(dict1, dict2):
    if dict2.get("event") == "stop":
        return True
    if len(dict1) != len(dict2):
        return False

    for key in dict1:
        if key not in dict2:
            return False

        value1 = dict1[key]
        value2 = dict2[key]

        if isinstance(value1, dict) and isinstance(value2, dict):
            # 递归比较嵌套字典
            if not compare_dicts_structure(value1, value2):
                return False
        elif type(value1) != type(value2):
            return False

    return True


def request_stream(url, method, payload, headers):
    package_info = []
    # headers["Referer"]="https://test-boss-demo.orionstar.com/chat/45e272691827224360806f6baf5b887b"

    response = requests.request(url=url, method=method, json=json.loads(payload), headers=headers, stream=True)

    if response.headers.get("content-type") == "application/json":
        return response.json()
    for line in response.iter_lines():
        response_line = line.decode()
        # 处理响应数据
        if not line.startswith(b"data: "):
            error_answer = response_line
        else:
            json_data = line[6:]
            try:
                data_dict = json.loads(json_data)
                package_info.append(data_dict)
            except json.JSONDecodeError:
                data = line.decode("utf-8")
                if data.__contains__("DONE"):
                    continue
                else:
                    print("json loads error:->{}".format(line))
                    continue
    # 等待整个请求完全返回
    response.close()
    if len(json.dumps(package_info, ensure_ascii=False)) > 32762:
        print(f"返回数据超长{package_info}")
    return package_info


if __name__ == '__main__':
    case()
