# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : load_test.py
<AUTHOR> <EMAIL>
@Time   : 2023-12-06 10:29:16
@Version: v1.0
"""
import os, sys

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print(BASE_DIR)
sys.path.append(BASE_DIR)
import requests
import json
import time
import concurrent.futures
from datetime import datetime
import pandas as pd
import uuid
import traceback
import copy
from test_data import course as teach_course, chat_dai, chat_rag, chat_v2, simulate_data, loop_1, loop_3, loop_2, \
    loop_1_thread, loop_2_thread, loop_3_thread

"""

华住接口测试

"""
all_res = []
#   模拟对话
simulate_chat = {
    "info": {
        "responsibility":
            "前台员工：作为酒店前线的代表，该角色承担着满足客人需求、解决客户问题的重任，同时必须维护酒店的服务质量标准。此外，该员工还负责准确、高效地进行系统数据录入，确保酒店运营的顺畅。",
        "description":
            "现在我们进行外部来电接听和转接的考核。我将扮演来电者，你需要按照服务规范模拟305房间没有人接通的场景。\n现在是一个非常宁静的午后，你正在酒店前台值班，突然前台电话响了，你会…",
        "male_background":
            "张先生是一位工作认真负责的职场人士，与王小姐是同事关系。他对工作非常敬业，积极主动地帮助同事解决问题。",
        "male_motivation":
            "张先生想联系305房间的王小姐，可能是因为有重要事情需要与她商讨或交流。",
        "female_background":
            "张女士是一位工作认真负责的职场人士，与王小姐是同事关系。她对工作非常敬业，积极主动地帮助同事解决问题。",
        "female_motivation":
            "张小姐想联系305房间的王小姐，可能是因为有重要事情需要与她商讨或交流。",
        "assessment_points":
            "考核要点主要包括五个方面：电话问候、聆听与记录、核实信息、无人应答处理以及礼貌道别",
        "scoring_criteria": [{
            "content": "使用标准的电话问候语",
            "score": 20,
            "expected_behavior": True
        }, {
            "content": "前台人员有确认房客信息",
            "score": 20,
            "expected_behavior": True
        }, {
            "content": "核实来电者提供房间号",
            "score": 10,
            "expected_behavior": True
        }, {
            "content": "核实来电者提供房客姓名",
            "score": 10,
            "expected_behavior": True
        }, {
            "content": "礼貌地处理无人应答情况并询问是否留言",
            "score": 20,
            "expected_behavior": True
        }, {
            "content": "使用礼貌的道别语结束通话，出现了感谢和再见的话术",
            "score": 20,
            "expected_behavior": True
        }],
        "dialogue_example":
            "前台员工: 您好，这里是XX酒店，有什么能够为您服务的？\n来电者: 你好，我想跟住在305房间的王xx小姐联系。\n前台员工：跟您确认一下，您说的是305房间的王xx小姐是吗？\n来电者:是的。\n前台员工: 好的，非常感谢您的来电，我是前台的李经理，请问您是...？\n来电者: 我是她的同事，张先生。\n前台员工: 好的，张先生，请稍等，我查询一下。\n来电者：好的，我等一下。\n前台员工：很抱歉，没有查到王小姐的入住信息。请问还有什么能够为您服务的吗？\n来电者: 没有了。\n前台员工: 好的，张先生，感谢您的来电，祝您有美好的一天，再见。",
        "end_condition":
            "前台回答内容包括：\"好的\"，感谢您的来电，再见。同时匹配到\"好的\"再见语义则触发结束。"
    },
    "dialog": "前台员工: 你好",
    "examiner_role_name": "来电者",
    "examinee_role_name": "前台员工",
    "gender": "female",
    "topic_id": "1",
    "request_id": "7edbfe51-c988-48f7-9bc8-3ee8846101a1",
    "topic": "外部呼入电话的接待和转接场景",
    "description": "现在是一个非常宁静的午后，你正在酒店前台值班，突然前台电话响了，你会"
}

#   线程数和循环次数

task_list = [
    {
        "thread": loop_1_thread,
        "loop": loop_1,
        "service": "simulate_chat",
        "data": simulate_chat,
        "path": "/v1/ctai/teach_simulate_chat"
    },
    {
        "thread": loop_2_thread,
        "loop": loop_2,
        "service": "simulate_chat",
        "data": simulate_chat,
        "path": "/v1/ctai/teach_simulate_chat"
    },
    {
        "thread": loop_3_thread,
        "loop": loop_3,
        "service": "simulate_chat",
        "data": simulate_chat,
        "path": "/v1/ctai/teach_simulate_chat"
    },
]
task_list_test = [
    {
        "thread": 1,
        "loop": 1,
        "service": "simulate_chat",
        "data": simulate_chat,
        "path": "/v1/ctai/teach_simulate_chat"
    },
]


def get_now(format=None):
    if format:
        return datetime.now().strftime("%Y%m%d")
    else:
        return datetime.now().strftime("%Y%m%d%H%M%S")


def send_stream(service, data, path):
    """
    流式请求
    :param qa_prompt:
    :param key:
    :param env:
    :return:
    """

    url = "http://self-hosting.chatmax.net:100" + path
    headers = {"content-Type": "application/json"}
    # 记录请求发送的时间（精确到毫秒）
    request_send_time = time.time()
    response = requests.post(url, headers=headers, json=data, stream=True)
    first_packet_time = None
    first_packet_time_2 = None
    total_character_count = 0
    answer = ""
    # 处理响应数据
    error = 0
    msg = ""
    if service == "simulate_chat" or service == "course_qa":
        package_list = []
        for line in response.iter_lines():
            package_list.append(line)
            if not first_packet_time:
                # 记录第一个数据包返回的时间（精确到毫秒）
                first_packet_time = time.time()
            # 处理响应数据
            if line.startswith(b"data: "):
                json_data = line[6:]
                try:
                    data_dict = json.loads(json_data)
                except json.JSONDecodeError:
                    print("json loads error:->{}".format(line))
                    continue
                content = ""
                try:
                    content = data_dict.get("data", {}).get("answer_text", "")
                except BaseException:
                    print(f"{service}:获取data报错")
                    print(f"{data_dict}")
                    print(traceback.format_exc())
                if content and not first_packet_time_2:
                    first_packet_time_2 = time.time()
                    # print(content)
                content_length = len(content)
                total_character_count += content_length
                answer += content
        #     else:
        #         print(f"{get_time()}数据不是以data开头{line}")
        if not answer:
            if not first_packet_time_2:
                first_packet_time_2 = time.time()
            msg = [msg] + package_list
            print(f"{get_now()},{service}没有获取到数据\n{package_list}")

    # 等待整个请求完全返回
    response.close()
    # 记录请求完成的时间（精确到毫秒）
    request_complete_time = time.time()
    # 转换为易读的时分秒格式（精确到毫秒）并保留毫秒的前3位
    request_send_time_str = datetime.fromtimestamp(request_send_time).strftime(
        '%Y-%m-%d %H:%M:%S.%f')[:23]
    first_packet_time_str = datetime.fromtimestamp(first_packet_time).strftime(
        '%Y-%m-%d %H:%M:%S.%f')[:23]
    request_complete_time_str = datetime.fromtimestamp(
        request_complete_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    if first_packet_time_2:
        first_packet_time_str_2 = datetime.fromtimestamp(
            first_packet_time_2).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
        first_packet_duration_2 = (first_packet_time_2 - request_send_time)
        tokens_time_2 = (request_complete_time - first_packet_time_2)
        performance_metric_2 = (total_character_count) / tokens_time_2
    else:
        performance_metric_2 = 0
        first_packet_duration_2 = 0
        first_packet_time_str_2 = ""

    # 计算并打印首包耗时和总耗时
    first_packet_duration = (first_packet_time - request_send_time)

    total_duration = request_complete_time - request_send_time
    tokens_time = (request_complete_time - first_packet_time)
    # 计算性能指标
    performance_metric = (total_character_count) / tokens_time

    # print("性能指标: {:.3f} 字/秒".format(performance_metric))
    # print("=" * 50)
    result = {
        "请求发送的时间": request_send_time_str,
        "第一个数据包返回的时间": first_packet_time_str,
        "第一个有效数据包返回的时间": first_packet_time_str_2,
        "请求完成的时间": request_complete_time_str,
        "首包耗时(s)": first_packet_duration,
        "有效数据_首包耗时(s)": first_packet_duration_2,
        "总耗时(s)": total_duration,
        # "输入": len(qa_prompt),
        "输出字数": total_character_count,
        "性能指标(字/秒)": performance_metric,
        "有效数字_性能指标(字/秒)": performance_metric_2,
        "answer": answer,
        "query": data.get("dialog", ""),
        "request_id": data["request_id"],
        "msg": msg,
        "progress": "",
        "session_id": data.get("session_id", "")
    }
    return result


def save_print_to_excel(thread, loop, service, path, data, run_number):
    results = []
    output_path = "{prefix_path}{sep}{run_number}{sep}{type_file}{sep}{service}".format(
        prefix_path=get_now("day"),
        sep=os.sep,
        service=service,
        type_file="Mixed_scenes",
        run_number=run_number
    )
    output_file = f"{thread}-{loop}-{get_now()}-{service}.xlsx"

    excel_path = f"{output_path}{os.sep}{output_file}"
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    with concurrent.futures.ThreadPoolExecutor(max_workers=thread) as executor:
        futures = []
        for index in range(loop):
            orion_data = simulate_data[index % len(simulate_data)]
            data = copy.deepcopy(orion_data)
            data["request_id"] = str(uuid.uuid4())
            futures.append(executor.submit(send_stream, service, data, path))
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as exc:
                print(f"Error: {exc}")
    df = pd.DataFrame(results)
    df.to_excel(excel_path, index=False)
    return excel_path


def run_1(run_number):
    thread = task_list[0].get("thread")
    loop = task_list[0].get("loop")
    service = task_list[0].get("service")
    data = task_list[0].get("data")
    path = task_list[0].get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    return save_print_to_excel(thread, loop, service, path, data, run_number)


def run_5(run_number):
    thread = task_list[1].get("thread")
    loop = task_list[1].get("loop")
    service = task_list[1].get("service")
    data = task_list[1].get("data")
    path = task_list[1].get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    return save_print_to_excel(thread, loop, service, path, data, run_number)


def run_10(run_number):
    thread = task_list[2].get("thread")
    loop = task_list[2].get("loop")
    service = task_list[2].get("service")
    data = task_list[2].get("data")
    path = task_list[2].get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    return save_print_to_excel(thread, loop, service, path, data, run_number)


def run_test(run_number):
    thread = task_list_test[0].get("thread")
    loop = task_list_test[0].get("loop")
    service = task_list_test[0].get("service")
    data = task_list_test[0].get("data")
    path = task_list_test[0].get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    return save_print_to_excel(thread, loop, service, path, data, run_number)


def save(run_number):
    df = pd.DataFrame(all_res)
    df.to_excel(f"./{get_now('day')}/{run_number}/Mixed_scenes/{get_now()}_simulate_all_res.xlsx", index=False)

# run_test(run_number=1)
