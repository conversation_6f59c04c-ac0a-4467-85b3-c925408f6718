# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : load_test.py
<AUTHOR> <EMAIL>
@Time   : 2023-12-06 10:29:16
@Version: v1.0
"""
import os, sys

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print(BASE_DIR)
sys.path.append(BASE_DIR)
import requests
import json
import time
import concurrent.futures
from datetime import datetime
import pandas as pd
import uuid
"""

华住接口测试

"""
all_res = []
#   周报数据
data_week = {
    "hotel_id":
    "9003051",
    "begin_date":
    "2023-04-23",
    "end_date":
    "2023-04-29",
    "type":
    1,
    "week_in_year":
    "2023-16",
    "context":
    "lw" + str(uuid.uuid4()).replace("-", ""),
    "request_id":
    "lw" + str(uuid.uuid4()).replace("-", ""),
    "stream":
    1,
    "first_level_data":
    "[{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"crevenuetotal\":14378.69,\"cbudgetrevenuetotal\":14188.43,\"crevenuetotalcompleterate\":1.01341,\"crevenueroom\":12133.66,\"crevenueroomnohourrent\":11553.90885,\"crevenueroomhourrent\":579.5064,\"crevenuenoroom\":2245.03,\"crevenuemeetingroom\":0.0,\"crevenuedinner\":718.725,\"crevenuemembercard\":1063.66,\"crevenueother\":462.645,\"croomcount\":348,\"croomday\":167.5,\"croomdaynohourrent\":159.5,\"croomdayhourrent\":8.0,\"revpar\":41.3181,\"occ\":0.48132,\"occnohourrent\":0.45833,\"adr\":72.4383,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"crevenuetotal\":26608.97,\"cbudgetrevenuetotal\":25782.18,\"crevenuetotalcompleterate\":1.032068,\"crevenueroom\":21765.24,\"crevenueroomnohourrent\":21256.47225,\"crevenueroomhourrent\":508.7019,\"crevenuenoroom\":4843.72,\"crevenuemeetingroom\":0.0,\"crevenuedinner\":1487.42,\"crevenuemembercard\":2316.06,\"crevenueother\":1040.24,\"croomcount\":609,\"croomday\":299.5,\"croomdaynohourrent\":292.5,\"croomdayhourrent\":7.0,\"revpar\":43.6929,\"occ\":0.49179,\"occnohourrent\":0.4803,\"adr\":72.6717,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"crevenuetotal\":24928.4,\"cbudgetrevenuetotal\":24355.03,\"crevenuetotalcompleterate\":1.023542,\"crevenueroom\":21379.15,\"crevenueroomnohourrent\":20760.7035,\"crevenueroomhourrent\":618.0945,\"crevenuenoroom\":3549.26,\"crevenuemeetingroom\":0.0,\"crevenuedinner\":1162.03,\"crevenuemembercard\":1671.11,\"crevenueother\":716.12,\"croomcount\":609,\"croomday\":294.0,\"croomdaynohourrent\":285.5,\"croomdayhourrent\":8.5,\"revpar\":40.9333,\"occ\":0.48276,\"occnohourrent\":0.4688,\"adr\":72.717,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"}]",
    "second_level_data":
    "[{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"个人会员\",\"crevenuetotal\":6874.647003,\"crevenueroom\":5564.551322,\"croomday\":75.5,\"adr\":73.70266652,\"crevenueroomavg\":794.9359031,\"croomdayavg\":10.78571429,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.45075,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"中介\",\"crevenuetotal\":4206.663254,\"crevenueroom\":3538.331705,\"croomday\":44.5,\"adr\":79.51307202,\"crevenueroomavg\":505.4759579,\"croomdayavg\":6.357142857,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.26567,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"企业会员\",\"crevenuetotal\":489.0190494,\"crevenueroom\":399.1091089,\"croomday\":5.5,\"adr\":72.56529253,\"crevenueroomavg\":57.01558699,\"croomdayavg\":0.785714286,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.03284,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"其他\",\"crevenuetotal\":479.485557,\"crevenueroom\":399.1091089,\"croomday\":5.5,\"adr\":72.56529253,\"crevenueroomavg\":57.01558699,\"croomdayavg\":0.785714286,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.03284,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"团队\",\"crevenuetotal\":1979.692742,\"crevenueroom\":1664.557078,\"croomday\":27.0,\"adr\":61.65026215,\"crevenueroomavg\":237.7938683,\"croomdayavg\":3.857142857,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.16119,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"时租\",\"crevenuetotal\":522.1989087,\"crevenueroom\":432.1356723,\"croomday\":8.0,\"adr\":54.01695904,\"crevenueroomavg\":61.73366747,\"croomdayavg\":1.142857143,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.04776,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"非会员\",\"crevenuetotal\":489.0338039,\"crevenueroom\":399.1091089,\"croomday\":5.5,\"adr\":72.56529253,\"crevenueroomavg\":57.01558699,\"croomdayavg\":0.785714286,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.03284,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"其他\",\"crevenuetotal\":2318.83813,\"crevenueroom\":1790.969065,\"croomday\":24.0,\"adr\":74.62371104,\"crevenueroomavg\":255.8527236,\"croomdayavg\":3.428571429,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.14328,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"单床类\",\"crevenuetotal\":4128.356519,\"crevenueroom\":3457.759972,\"croomday\":46.5,\"adr\":74.36042951,\"crevenueroomavg\":493.9657103,\"croomdayavg\":6.642857143,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.27761,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"双床类\",\"crevenuetotal\":7931.490439,\"crevenueroom\":6884.921033,\"croomday\":97.0,\"adr\":70.97856734,\"crevenueroomavg\":983.5601476,\"croomdayavg\":13.85714286,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.5791,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"IDS\",\"crevenuetotal\":7901.395203,\"crevenueroom\":6696.322215,\"croomday\":89.5,\"adr\":74.81924261,\"crevenueroomavg\":956.6174593,\"croomdayavg\":12.78571429,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.53433,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"其他\",\"crevenuetotal\":660.1630963,\"crevenueroom\":541.7784341,\"croomday\":7.5,\"adr\":72.23712455,\"crevenueroomavg\":77.39691916,\"croomdayavg\":1.071428571,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.04478,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"分销\",\"crevenuetotal\":4236.170021,\"crevenueroom\":3577.750413,\"croomday\":47.5,\"adr\":75.32106133,\"crevenueroomavg\":511.1072019,\"croomdayavg\":6.785714286,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.28358,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"酒店直销\",\"crevenuetotal\":1825.641377,\"crevenueroom\":1487.467118,\"croomday\":25.5,\"adr\":58.33204384,\"crevenueroomavg\":212.4953026,\"croomdayavg\":3.642857143,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.15224,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"一般\",\"crevenuetotal\":1501.737194,\"crevenueroom\":1253.34248,\"croomday\":17.5,\"adr\":71.61957029,\"crevenueroomavg\":179.0489257,\"croomdayavg\":2.5,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.10448,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"新客\",\"crevenuetotal\":6960.019032,\"crevenueroom\":5960.30417,\"croomday\":79.0,\"adr\":75.44688823,\"crevenueroomavg\":851.4720243,\"croomdayavg\":11.28571429,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.47164,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"高价值\",\"crevenuetotal\":3218.015614,\"crevenueroom\":2735.559804,\"croomday\":42.5,\"adr\":64.36611301,\"crevenueroomavg\":390.7942577,\"croomdayavg\":6.071428571,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.25373,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2022-16\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"高潜力\",\"crevenuetotal\":2701.857811,\"crevenueroom\":2321.511362,\"croomday\":30.5,\"adr\":76.11512662,\"crevenueroomavg\":331.6444803,\"croomdayavg\":4.357142857,\"croomdaytotalavg\":23.93,\"croomdayratioavg\":0.18209,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"个人会员\",\"crevenuetotal\":14500.45082,\"crevenueroom\":12008.53538,\"croomday\":162.5,\"adr\":73.89867926,\"crevenueroomavg\":1715.505054,\"croomdayavg\":23.21428571,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.54257,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"中介\",\"crevenuetotal\":7214.277213,\"crevenueroom\":6067.167855,\"croomday\":78.0,\"adr\":77.78420326,\"crevenueroomavg\":866.738265,\"croomdayavg\":11.14285714,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.26043,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"企业会员\",\"crevenuetotal\":307.5906266,\"crevenueroom\":246.2574429,\"croomday\":4.0,\"adr\":61.56436073,\"crevenueroomavg\":35.1796347,\"croomdayavg\":0.571428571,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.01336,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"其他\",\"crevenuetotal\":236.4155933,\"crevenueroom\":220.8845065,\"croomday\":3.0,\"adr\":73.62816883,\"crevenueroomavg\":31.5549295,\"croomdayavg\":0.428571429,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.01002,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"团队\",\"crevenuetotal\":3121.543028,\"crevenueroom\":2629.940024,\"croomday\":41.5,\"adr\":63.37204877,\"crevenueroomavg\":375.7057177,\"croomdayavg\":5.928571429,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.13856,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"时租\",\"crevenuetotal\":364.9366627,\"crevenueroom\":316.1501936,\"croomday\":7.0,\"adr\":45.16431337,\"crevenueroomavg\":45.16431337,\"croomdayavg\":1.0,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.02337,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"非会员\",\"crevenuetotal\":285.549532,\"crevenueroom\":220.8845065,\"croomday\":3.0,\"adr\":73.62816883,\"crevenueroomavg\":31.5549295,\"croomdayavg\":0.428571429,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.01002,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"其他\",\"crevenuetotal\":5576.352312,\"crevenueroom\":4618.444697,\"croomday\":65.5,\"adr\":70.51060606,\"crevenueroomavg\":659.7778139,\"croomdayavg\":9.357142857,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.2187,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"单床类\",\"crevenuetotal\":3448.850289,\"crevenueroom\":2814.471999,\"croomday\":35.0,\"adr\":80.41348569,\"crevenueroomavg\":402.0674284,\"croomdayavg\":5.0,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.11686,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"双床类\",\"crevenuetotal\":17583.75834,\"crevenueroom\":14332.32871,\"croomday\":199.0,\"adr\":72.02175231,\"crevenueroomavg\":2047.47553,\"croomdayavg\":28.42857143,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.66444,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"IDS\",\"crevenuetotal\":11838.2143,\"crevenueroom\":9571.792733,\"croomday\":132.5,\"adr\":72.23994515,\"crevenueroomavg\":1367.398962,\"croomdayavg\":18.92857143,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.4424,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"其他\",\"crevenuetotal\":1772.96886,\"crevenueroom\":1478.980598,\"croomday\":21.0,\"adr\":70.42764752,\"crevenueroomavg\":211.2829426,\"croomdayavg\":3.0,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.07012,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"分销\",\"crevenuetotal\":8893.711866,\"crevenueroom\":7460.416137,\"croomday\":101.0,\"adr\":73.86550631,\"crevenueroomavg\":1065.773734,\"croomdayavg\":14.42857143,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.33723,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"酒店直销\",\"crevenuetotal\":3965.122401,\"crevenueroom\":3197.632213,\"croomday\":44.5,\"adr\":71.85690366,\"crevenueroomavg\":456.8046019,\"croomdayavg\":6.357142857,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.14858,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"一般\",\"crevenuetotal\":5313.315576,\"crevenueroom\":4270.251051,\"croomday\":58.5,\"adr\":72.99574446,\"crevenueroomavg\":610.0358644,\"croomdayavg\":8.357142857,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.19533,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"新客\",\"crevenuetotal\":13490.43641,\"crevenueroom\":11362.11196,\"croomday\":149.5,\"adr\":76.0007489,\"crevenueroomavg\":1623.158851,\"croomdayavg\":21.35714286,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.49917,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"高价值\",\"crevenuetotal\":4540.423105,\"crevenueroom\":3453.993065,\"croomday\":54.5,\"adr\":63.37601954,\"crevenueroomavg\":493.4275807,\"croomdayavg\":7.785714286,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.18197,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-15\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"高潜力\",\"crevenuetotal\":3074.911747,\"crevenueroom\":2590.459244,\"croomday\":36.0,\"adr\":71.95720122,\"crevenueroomavg\":370.0656063,\"croomdayavg\":5.142857143,\"croomdaytotalavg\":42.79,\"croomdayratioavg\":0.1202,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"个人会员\",\"crevenuetotal\":12584.70253,\"crevenueroom\":10105.13897,\"croomday\":137.0,\"adr\":73.76013847,\"crevenueroomavg\":1443.591281,\"croomdayavg\":19.57142857,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.46599,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"中介\",\"crevenuetotal\":8721.50492,\"crevenueroom\":7431.519204,\"croomday\":95.5,\"adr\":77.81695502,\"crevenueroomavg\":1061.645601,\"croomdayavg\":13.64285714,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.32483,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"企业会员\",\"crevenuetotal\":469.2586586,\"crevenueroom\":404.0210457,\"croomday\":5.5,\"adr\":73.45837195,\"crevenueroomavg\":57.71729224,\"croomdayavg\":0.785714286,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.01871,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"其他\",\"crevenuetotal\":499.2359995,\"crevenueroom\":404.0210457,\"croomday\":5.5,\"adr\":73.45837195,\"crevenueroomavg\":57.71729224,\"croomdayavg\":0.785714286,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.01871,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"团队\",\"crevenuetotal\":3305.509274,\"crevenueroom\":2667.030874,\"croomday\":43.5,\"adr\":61.31105457,\"crevenueroomavg\":381.0044106,\"croomdayavg\":6.214285714,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.14796,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"时租\",\"crevenuetotal\":539.2758538,\"crevenueroom\":459.0704173,\"croomday\":8.5,\"adr\":54.00828438,\"crevenueroomavg\":65.58148819,\"croomdayavg\":1.214285714,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.02891,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"非会员\",\"crevenuetotal\":530.219575,\"crevenueroom\":404.0210457,\"croomday\":5.5,\"adr\":73.45837195,\"crevenueroomavg\":57.71729224,\"croomdayavg\":0.785714286,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.01871,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"其他\",\"crevenuetotal\":7692.736119,\"crevenueroom\":6341.510122,\"croomday\":87.0,\"adr\":72.89092094,\"crevenueroomavg\":905.9300174,\"croomdayavg\":12.42857143,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.29592,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"单床类\",\"crevenuetotal\":6308.133398,\"crevenueroom\":5634.977481,\"croomday\":74.5,\"adr\":75.63728161,\"crevenueroomavg\":804.996783,\"croomdayavg\":10.64285714,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.2534,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"双床类\",\"crevenuetotal\":10927.54017,\"crevenueroom\":9402.651998,\"croomday\":132.5,\"adr\":70.96341131,\"crevenueroomavg\":1343.236,\"croomdayavg\":18.92857143,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.45068,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"IDS\",\"crevenuetotal\":14056.35107,\"crevenueroom\":11888.11823,\"croomday\":161.0,\"adr\":73.83924366,\"crevenueroomavg\":1698.302604,\"croomdayavg\":23.0,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.54762,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"其他\",\"crevenuetotal\":899.0438813,\"crevenueroom\":751.8993127,\"croomday\":10.5,\"adr\":71.60945835,\"crevenueroomavg\":107.4141875,\"croomdayavg\":1.5,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.03571,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"分销\",\"crevenuetotal\":7127.757619,\"crevenueroom\":5738.839326,\"croomday\":75.5,\"adr\":76.0111169,\"crevenueroomavg\":819.8341894,\"croomdayavg\":10.78571429,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.2568,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"酒店直销\",\"crevenuetotal\":3995.930557,\"crevenueroom\":3271.676645,\"croomday\":51.0,\"adr\":64.15052245,\"crevenueroomavg\":467.3823779,\"croomdayavg\":7.285714286,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.17347,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"一般\",\"crevenuetotal\":3107.859044,\"crevenueroom\":2481.891837,\"croomday\":35.5,\"adr\":69.91244611,\"crevenueroomavg\":354.5559767,\"croomdayavg\":5.071428571,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.12075,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"新客\",\"crevenuetotal\":14347.50023,\"crevenueroom\":11488.52049,\"croomday\":149.5,\"adr\":76.8462909,\"crevenueroomavg\":1641.217213,\"croomdayavg\":21.35714286,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.5085,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"高价值\",\"crevenuetotal\":3269.144743,\"crevenueroom\":2777.652976,\"croomday\":46.0,\"adr\":60.38376035,\"crevenueroomavg\":396.807568,\"croomdayavg\":6.571428571,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.15646,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"},{\"week_key2\":\"2023-16\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"高潜力\",\"crevenuetotal\":5789.188446,\"crevenueroom\":4865.280297,\"croomday\":66.5,\"adr\":73.16210973,\"crevenueroomavg\":695.0400424,\"croomdayavg\":9.5,\"croomdaytotalavg\":42.0,\"croomdayratioavg\":0.22619,\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createyw\":\"2024-04\"}]"
}
#   日报数据
data_day = {
    "hotel_id":
    "9003051",
    "begin_date":
    "2023-07-15",
    "end_date":
    "2023-07-15",
    "type":
    2,
    "week_in_year":
    "2023-16",
    "context":
    "lw" + str(uuid.uuid4()).replace("-", ""),
    "request_id":
    "lw" + str(uuid.uuid4()).replace("-", ""),
    "stream":
    1,
    "first_level_data":
    "[{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"crevenuetotal\":3820.01,\"cbudgetrevenuetotal\":3677.48,\"crevenuetotalcompleterate\":1.038758,\"crevenueroom\":3053.52,\"crevenueroomnohourrent\":3017.05,\"crevenueroomhourrent\":36.35,\"crevenuenoroom\":766.49,\"crevenuemeetingroom\":0.0,\"crevenuedinner\":231.095,\"crevenuemembercard\":367.99,\"crevenueother\":167.405,\"croomcount\":87,\"croomday\":42.0,\"croomdaynohourrent\":41.5,\"croomdayhourrent\":0.5,\"revpar\":43.91,\"occ\":0.4828,\"occnohourrent\":0.477,\"adr\":72.7,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"crevenuetotal\":3406.42,\"cbudgetrevenuetotal\":3348.37,\"crevenuetotalcompleterate\":1.017337,\"crevenueroom\":2832.99,\"crevenueroomnohourrent\":2546.06,\"crevenueroomhourrent\":286.88,\"crevenuenoroom\":573.43,\"crevenuemeetingroom\":0.0,\"crevenuedinner\":181.58,\"crevenuemembercard\":271.41,\"crevenueother\":120.44,\"croomcount\":87,\"croomday\":39.5,\"croomdaynohourrent\":35.5,\"croomdayhourrent\":4.0,\"revpar\":39.15,\"occ\":0.454,\"occnohourrent\":0.408,\"adr\":71.72,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"crevenuetotal\":4460.94,\"cbudgetrevenuetotal\":4244.21,\"crevenuetotalcompleterate\":1.051065,\"crevenueroom\":3531.07,\"crevenueroomnohourrent\":3420.54,\"crevenueroomhourrent\":110.34,\"crevenuenoroom\":929.86,\"crevenuemeetingroom\":0.0,\"crevenuedinner\":275.56,\"crevenuemembercard\":448.37,\"crevenueother\":205.93,\"croomcount\":87,\"croomday\":48.0,\"croomdaynohourrent\":46.5,\"croomdayhourrent\":1.5,\"revpar\":51.28,\"occ\":0.5517,\"occnohourrent\":0.5345,\"adr\":73.56,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"}]",
    "second_level_data":
    "[{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"个人会员\",\"croomday\":34.5,\"adr\":75.06968562,\"crevenuetotal\":3081.985943,\"crevenueroom\":2589.904154,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"中介\",\"croomday\":0.5,\"adr\":75.99068562,\"crevenuetotal\":44.94849055,\"crevenueroom\":37.99534281,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"企业会员\",\"croomday\":0.5,\"adr\":70.63893562,\"crevenuetotal\":42.63059765,\"crevenueroom\":35.31946781,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"其他\",\"croomday\":0.5,\"adr\":70.63893562,\"crevenuetotal\":46.48041964,\"crevenueroom\":35.31946781,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"团队\",\"croomday\":7.5,\"adr\":61.18958562,\"crevenuetotal\":508.0265346,\"crevenueroom\":458.9218922,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"时租\",\"croomday\":0.5,\"adr\":55.48738562,\"crevenuetotal\":33.95828,\"crevenueroom\":27.74369281,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"非会员\",\"croomday\":0.5,\"adr\":70.63893562,\"crevenuetotal\":45.84466922,\"crevenueroom\":35.31946781,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"其他\",\"croomday\":18.5,\"adr\":75.08888406,\"crevenuetotal\":1736.430444,\"crevenueroom\":1389.144355,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"单床类\",\"croomday\":14.5,\"adr\":68.20458406,\"crevenuetotal\":1205.550125,\"crevenueroom\":988.9664688,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"双床类\",\"croomday\":9.0,\"adr\":75.04498406,\"crevenuetotal\":878.0263135,\"crevenueroom\":675.4048565,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"IDS\",\"croomday\":6.0,\"adr\":75.03252992,\"crevenuetotal\":450.1951795,\"crevenueroom\":450.1951795,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"其他\",\"croomday\":5.5,\"adr\":70.99047992,\"crevenuetotal\":495.087607,\"crevenueroom\":390.4476396,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"分销\",\"croomday\":19.5,\"adr\":78.63002992,\"crevenuetotal\":1740.279137,\"crevenueroom\":1533.285583,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"酒店直销\",\"croomday\":10.5,\"adr\":61.24932992,\"crevenuetotal\":654.0509696,\"crevenueroom\":643.1179642,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"一般\",\"croomday\":0.5,\"adr\":62.0023004,\"crevenuetotal\":38.7824389,\"crevenueroom\":31.0011502,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"新客\",\"croomday\":32.0,\"adr\":74.7519004,\"crevenuetotal\":3198.185307,\"crevenueroom\":2392.060813,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"高价值\",\"croomday\":3.0,\"adr\":62.5346004,\"crevenuetotal\":222.4981082,\"crevenueroom\":187.6038012,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2022-07-14\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"高潜力\",\"croomday\":6.0,\"adr\":67.7301004,\"crevenuetotal\":526.2628801,\"crevenueroom\":406.3806024,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"个人会员\",\"croomday\":12.0,\"adr\":80.70101464,\"crevenuetotal\":1180.494442,\"crevenueroom\":968.4121757,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"中介\",\"croomday\":3.5,\"adr\":77.28451464,\"crevenuetotal\":368.1447855,\"crevenueroom\":270.4958012,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"企业会员\",\"croomday\":3.0,\"adr\":74.86377618,\"crevenuetotal\":246.1520961,\"crevenueroom\":224.5913285,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"其他\",\"croomday\":3.0,\"adr\":74.86377618,\"crevenuetotal\":289.2736312,\"crevenueroom\":224.5913285,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"团队\",\"croomday\":10.0,\"adr\":64.09611464,\"crevenuetotal\":772.3581814,\"crevenueroom\":640.9611464,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"时租\",\"croomday\":4.0,\"adr\":52.23311464,\"crevenuetotal\":265.1352899,\"crevenueroom\":208.9324586,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"非会员\",\"croomday\":3.0,\"adr\":74.86377618,\"crevenuetotal\":259.6275758,\"crevenueroom\":224.5913285,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"其他\",\"croomday\":10.5,\"adr\":62.8047806,\"crevenuetotal\":861.9014065,\"crevenueroom\":659.4501963,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"单床类\",\"croomday\":11.5,\"adr\":74.5972806,\"crevenuetotal\":1055.178534,\"crevenueroom\":857.8687269,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"双床类\",\"croomday\":17.5,\"adr\":75.1811806,\"crevenuetotal\":1489.339188,\"crevenueroom\":1315.67066,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"IDS\",\"croomday\":14.5,\"adr\":71.62725106,\"crevenuetotal\":1245.275573,\"crevenueroom\":1038.59514,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"其他\",\"croomday\":3.5,\"adr\":72.9359126,\"crevenuetotal\":300.2042163,\"crevenueroom\":255.2756941,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"分销\",\"croomday\":17.5,\"adr\":71.38895106,\"crevenuetotal\":1560.383998,\"crevenueroom\":1249.306644,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"酒店直销\",\"croomday\":3.5,\"adr\":72.9359126,\"crevenuetotal\":276.208301,\"crevenueroom\":255.2756941,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"一般\",\"croomday\":3.0,\"adr\":68.1465537,\"crevenuetotal\":244.7142743,\"crevenueroom\":204.4396611,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"新客\",\"croomday\":31.0,\"adr\":72.8005537,\"crevenuetotal\":2798.453284,\"crevenueroom\":2256.817165,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"高价值\",\"croomday\":4.0,\"adr\":65.0956537,\"crevenuetotal\":313.2402856,\"crevenueroom\":260.3826148,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-13\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"高潜力\",\"croomday\":0.5,\"adr\":81.8722537,\"crevenuetotal\":54.64972934,\"crevenueroom\":40.93612685,\"weeklabel\":\"周中\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"个人会员\",\"croomday\":16.5,\"adr\":72.49411488,\"crevenuetotal\":1461.698838,\"crevenueroom\":1196.152896,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"中介\",\"croomday\":15.5,\"adr\":76.35431488,\"crevenuetotal\":1455.695013,\"crevenueroom\":1183.491881,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"企业会员\",\"croomday\":2.0,\"adr\":93.27071488,\"crevenuetotal\":231.3113729,\"crevenueroom\":186.5414298,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"其他\",\"croomday\":1.5,\"adr\":77.59193027,\"crevenuetotal\":118.3664896,\"crevenueroom\":116.3878954,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"团队\",\"croomday\":9.5,\"adr\":64.33201488,\"crevenuetotal\":715.0503454,\"crevenueroom\":611.1541414,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"时租\",\"croomday\":1.5,\"adr\":77.59193027,\"crevenuetotal\":122.440066,\"crevenueroom\":116.3878954,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"客源\",\"factor\":\"非会员\",\"croomday\":1.5,\"adr\":77.59193027,\"crevenuetotal\":154.5631251,\"crevenueroom\":116.3878954,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"其他\",\"croomday\":5.5,\"adr\":71.6226069,\"crevenuetotal\":470.3456595,\"crevenueroom\":393.924338,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"单床类\",\"croomday\":6.0,\"adr\":86.13339152,\"crevenuetotal\":610.3412123,\"crevenueroom\":516.8003491,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"房类\",\"factor\":\"双床类\",\"croomday\":36.5,\"adr\":71.79039152,\"crevenuetotal\":3380.250585,\"crevenueroom\":2620.34929,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"IDS\",\"croomday\":39.0,\"adr\":74.0137179,\"crevenuetotal\":3585.076468,\"crevenueroom\":2886.534998,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"其他\",\"croomday\":0.5,\"adr\":70.24423329,\"crevenuetotal\":38.17774079,\"crevenueroom\":35.12211664,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"分销\",\"croomday\":7.0,\"adr\":72.1714179,\"crevenuetotal\":602.7035109,\"crevenueroom\":505.1999253,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"渠道\",\"factor\":\"酒店直销\",\"croomday\":2.0,\"adr\":65.1056179,\"crevenuetotal\":151.8263009,\"crevenueroom\":130.2112358,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"一般\",\"croomday\":3.0,\"adr\":71.0854757,\"crevenuetotal\":218.8010942,\"crevenueroom\":213.2564271,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"新客\",\"croomday\":29.0,\"adr\":76.5718757,\"crevenuetotal\":2511.480951,\"crevenueroom\":2220.584395,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"高价值\",\"croomday\":6.0,\"adr\":65.1995757,\"crevenuetotal\":462.3953909,\"crevenueroom\":391.1974542,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"},{\"create_day\":\"2023-07-14\",\"hotel_id\":9003051,\"dimension\":\"客户价值\",\"factor\":\"高潜力\",\"croomday\":10.0,\"adr\":70.1465757,\"crevenuetotal\":834.0427851,\"crevenueroom\":701.465757,\"weeklabel\":\"周末\",\"etl_ins_tm\":\"2024-04-23 15:08:49\",\"createym\":\"2024-04\"}]"
}
#   聊天接口——DAI，supperboss_qa 出租率
chat_DAI_data = {
    "session_id":
    "85bc7c07-055b-4ef3-be99-cae2fa9b7b1c",
    "recommend":
    0,
    "query":
    "出租率",
    "sence":
    "superboss_qa",
    "request_id":
    "55e92073-e959-4b9c-983c-d2395117ebc8",
    "store_id":
    "9007534",
    "dataaccess_token":
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoic3lzdGVtIiwiaG90ZWxfaWQiOiI5MDA3NTM0IiwiY3JlYXRlX3RpbWUiOiIyMDI0LTA1LTEzIiwiZXhwIjoxNzUxNjE5NTE1fQ.OfBbSuOBO1wRkkoOQiAA3njX49egLJVz8-DfYinxPfI"
}
#   聊天接口——周报supperboss_qa
chat_DAI_week_report = {
    "session_id":
    "4a3c0d6d-ca16-4b43-b254-776deb8ba66d",
    "recommend":
    0,
    "query":
    "查看2024年第20周的周报",
    "sence":
    "superboss_qa",
    "request_id":
    "64444b10-5b6c-49d8-b2e3-4db5431e0bd9",
    "store_id":
    "9007534",
    "dataaccess_token":
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoic3lzdGVtIiwiaG90ZWxfaWQiOiI5MDA3NTM0IiwiY3JlYXRlX3RpbWUiOiIyMDI0LTA1LTEzIiwiZXhwIjoxNzUxNjE5NTE1fQ.OfBbSuOBO1wRkkoOQiAA3njX49egLJVz8-DfYinxPfI"
}
#   聊天接口——QAsupperboss_qa
chat_rag = {
    "session_id":
    "85bc7c07-055b-4ef3-be99-cae2fa9b7b1c",
    "recommend":
    0,
    "query":
    "PMS系统如何提高酒店的运营效率？",
    "sence":
    "superboss_qa",
    "request_id":
    "c0a6c4b2-302e-4a20-bcd1-415c08c0283c",
    "store_id":
    "9007534",
    "dataaccess_token":
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoic3lzdGVtIiwiaG90ZWxfaWQiOiI5MDA3NTM0IiwiY3JlYXRlX3RpbWUiOiIyMDI0LTA1LTEzIiwiZXhwIjoxNzUxNjE5NTE1fQ.OfBbSuOBO1wRkkoOQiAA3njX49egLJVz8-DfYinxPfI"
}
#   模拟对话
simulate_chat = {
    "info": {
        "responsibility":
        "前台员工：作为酒店前线的代表，该角色承担着满足客人需求、解决客户问题的重任，同时必须维护酒店的服务质量标准。此外，该员工还负责准确、高效地进行系统数据录入，确保酒店运营的顺畅。",
        "description":
        "现在我们进行外部来电接听和转接的考核。我将扮演来电者，你需要按照服务规范模拟305房间没有人接通的场景。\n现在是一个非常宁静的午后，你正在酒店前台值班，突然前台电话响了，你会…",
        "male_background":
        "张先生是一位工作认真负责的职场人士，与王小姐是同事关系。他对工作非常敬业，积极主动地帮助同事解决问题。",
        "male_motivation":
        "张先生想联系305房间的王小姐，可能是因为有重要事情需要与她商讨或交流。",
        "female_background":
        "张女士是一位工作认真负责的职场人士，与王小姐是同事关系。她对工作非常敬业，积极主动地帮助同事解决问题。",
        "female_motivation":
        "张小姐想联系305房间的王小姐，可能是因为有重要事情需要与她商讨或交流。",
        "assessment_points":
        "考核要点主要包括五个方面：电话问候、聆听与记录、核实信息、无人应答处理以及礼貌道别",
        "scoring_criteria": [{
            "content": "使用标准的电话问候语",
            "score": 20,
            "expected_behavior": True
        }, {
            "content": "前台人员有确认房客信息",
            "score": 20,
            "expected_behavior": True
        }, {
            "content": "核实来电者提供房间号",
            "score": 10,
            "expected_behavior": True
        }, {
            "content": "核实来电者提供房客姓名",
            "score": 10,
            "expected_behavior": True
        }, {
            "content": "礼貌地处理无人应答情况并询问是否留言",
            "score": 20,
            "expected_behavior": True
        }, {
            "content": "使用礼貌的道别语结束通话，出现了感谢和再见的话术",
            "score": 20,
            "expected_behavior": True
        }],
        "dialogue_example":
        "前台员工: 您好，这里是XX酒店，有什么能够为您服务的？\n来电者: 你好，我想跟住在305房间的王xx小姐联系。\n前台员工：跟您确认一下，您说的是305房间的王xx小姐是吗？\n来电者:是的。\n前台员工: 好的，非常感谢您的来电，我是前台的李经理，请问您是...？\n来电者: 我是她的同事，张先生。\n前台员工: 好的，张先生，请稍等，我查询一下。\n来电者：好的，我等一下。\n前台员工：很抱歉，没有查到王小姐的入住信息。请问还有什么能够为您服务的吗？\n来电者: 没有了。\n前台员工: 好的，张先生，感谢您的来电，祝您有美好的一天，再见。",
        "end_condition":
        "前台回答内容包括：\"好的\"，感谢您的来电，再见。同时匹配到\"好的\"再见语义则触发结束。"
    },
    "dialog": "前台员工: 你好",
    "examiner_role_name": "来电者",
    "examinee_role_name": "前台员工",
    "gender": "female",
    "topic_id": "1",
    "request_id": "7edbfe51-c988-48f7-9bc8-3ee8846101a1",
    "topic": "外部呼入电话的接待和转接场景",
    "description": "现在是一个非常宁静的午后，你正在酒店前台值班，突然前台电话响了，你会"
}
#   课程答疑
course_qa = {
    "session_id": "test_session_id",
    "query": "GOP是什么？",
    "context":
    "GOP课程概念～基础术语_原文\n2024年04月25日 10:19\n大家好，今天带大家五分钟快速掌握GOP基础术语。可售间夜，已售间夜，出租率。客房revpar和综合revpar,身为店长的你是不是已经被术语搞得晕头转向？别担心，今天我们就来一起学习关于GOP报表中的那些基础术语，让你快速掌握其中的含义和计算方法。\n\n首先我们得先清楚什么是GOP。GOP又叫经营毛利，是由营业收入减去营业支出得到的毛利。酒店行业把GOP报表作为考核的重要指标之一，来衡量酒店经营的结果，因此GOP报表有着酒店经营\"晴雨表\"的称号。那当我们拿到一份GOP报表该怎么来分析呢？常见的分析方法有预算比、同比、环比分析。在做任何经营数据分析时，我们都应该将实际经营数据与预算去年同期和上月情况进行对比。\n\n这里要提到另一个关键词，GOP率。它等于GOP除以营业收入，也就是营业毛利占营业收入的比值。由于酒不同酒店房量营业收入支出的量级不同，GOP的绝对值不具备可比性。这时我们可以用到GOP率来进行对比，得出不同酒店的业绩。横向对比，回到开篇，困惑店长的各种术语，他们是GOP报表中的重要信息。接下来我们逐一来分析。首先是已售间夜和可售间夜，分别是指酒店的实际房间出租数和可供房间数，两者进行相除就可以得到出租率及实际出租数占可供房量的比重。\n\n第二个是平均房价，是指客房收入和已售间夜的比值。它包含不含税和含税两种。平均房价等于客房收入除以已售间夜数，而平均房价含税则在原有的基础上乘以（1+税率）。这里扩充一下关于增值税的内容。增值税属于一种价外税，在我国商品含税定价的情况下，记账时应将含税收入换算成不含税收入和销项税额两部分。比如酒店当日房价300元，假设税率为6%，那么300乘以税率得到18元是税金，剩余282元是酒店可确认的收入。\n\n第三个是客房revpar，客房收入处于可售间夜，同时也等于平均房价乘以出租率。他们也是包含不含税和含税两种。客房revpar含税，这是在原有的基础上增加（1+税率）即客房收入乘以（1+税率）除以可售间夜数，或等于平均房价乘以（1+税率）再乘以出租率。\n\n第四个是综合revpar，即综合收入除以可售间夜。总收益是由客房收入和非客房收入两部分构成。综合收入除以可售间夜就可以得到综合revpar同理含税的综合revpar增加（1+税率）即可。\n\n最后需强调一点，revpar在不同报表中数值会不同。比如财务报表中是不含税的，而销售报表则是含税的。大家在核对报表时需要留意。到这里我们就基本说完了报表中的各类术语。\n\n接下来我们通过一个小案例实际来动手计算一下。上海某酒店房间数120间，九月份出租率90%，客房收入90万。请分别计算酒店可售间夜已售间夜，平均房价、客房revpar，大家可以先暂停视频算一算哟。下面揭晓正确答案。我们把数据以表格的形式进行梳理，酒店可售间夜数等于客房数乘以当月天数，酒店有120间客房 天数30天，得出结果为120乘以30等于3600间，用一致的出租率乘以可售间夜数就可以得到已售间夜数，3600乘以90%，得出已售间夜3240间，平均房价等于已知的当月收入90万除以已售间夜数3240间，结果为277.78元。客房revpar等于90万除以可售间夜数3600间，得到的结果为250元。大家都算对了吗？我。\n\n关于GOP的概念基础术语的讲解就结束了。大家可以再利用今天学到的知识反复实践。下节课我们一起来学习GOP报表中关于收入的知识。",
    "request_id": "264cf81b-7eaa-40b4-83f8-b4cb8a96f175"
}
#   线程数和循环次数
task_list = [
    {
        "thread": 1,
        "loop": 200,
        "service": "report_day",
        "data": data_day,
        "path": "/v1/ctai/boss_report_analysis"
    },
    {
        "thread": 3,
        "loop": 200,
        "service": "report_week",
        "data": data_week,
        "path": "/v1/ctai/boss_report_analysis"
    },
    {
        "thread": 6,
        "loop": 200,
        "service": "report_day",
        "data": data_day,
        "path": "/v1/ctai/boss_report_analysis"
    },
]


def send_stream(service, data, path):
    """
    流式请求
    :param qa_prompt:
    :param key:
    :param env:
    :return:
    """

    url = "http://self-hosting.chatmax.net:100" + path
    headers = {"content-Type": "application/json"}
    # 记录请求发送的时间（精确到毫秒）
    request_send_time = time.time()
    response = requests.post(url, headers=headers, json=data, stream=True)
    first_packet_time = None
    first_packet_time_2 = None
    total_character_count = 0
    answer = ""
    # 处理响应数据
    error = 0
    if service == "report_day" or service == "report_week":
        for line in response.iter_lines():
            package_list = []
            package_list.append(line)
            if not first_packet_time:
                package_list.append(line)
                # 记录第一个数据包返回的时间（精确到毫秒）
                first_packet_time = time.time()
            # 处理响应数据
            if line.startswith(b"data: "):
                json_data = line[6:]
                try:
                    data_dict = json.loads(json_data)
                except json.JSONDecodeError:
                    print("json loads error:->{}".format(line))
                    continue
                # answer += data_dict.get("outputs", {}).get("answer", "")

                if 'outputs' in data_dict:
                    data = data_dict['outputs']
                    if "answer" in data:
                        content = data["answer"]
                        #   计算第一个有效数据返回的时间
                        if content and not first_packet_time_2:
                            first_packet_time_2 = time.time()
                        # print(content)
                        content_length = len(content)
                        total_character_count += content_length
                        answer += content
                    else:
                        pass
                        # log.error("response not answer_text:->{}".format(line))
                else:
                    print("response not data:->{}".format(line))
        #     else:
        #         print(f"{get_time()}数据不是以data开头{line}")
        # if not answer:
        #     print(f"{get_time()},report_day or report_week 没有获取到数据\n{package_list}")
    elif service == "chat_DAI_week_report" or service == "chat_DAI_data":
        package_list = []
        for line in response.iter_lines():
            package_list.append(line)
            if not first_packet_time:
                # 记录第一个数据包返回的时间（精确到毫秒）
                first_packet_time = time.time()
            # 处理响应数据
            if line.startswith(b"data: "):
                json_data = line[6:]
                try:
                    data_dict = json.loads(json_data)
                except json.JSONDecodeError:
                    print("json loads error:->{}".format(line))
                    continue
                return_type = data_dict.get("data", {}).get("return_type", "")
                return_data = data_dict.get("data",
                                            {}).get("return_data",
                                                    {}).get(return_type, {})
                if 'insight' == return_type:
                    content = return_data.get("text")
                    #   计算第一个有效数据返回的时间
                    if content and not first_packet_time_2:
                        first_packet_time_2 = time.time()
                    content_length = len(content)
                    total_character_count += content_length
                    answer += content
            # else:
            #     print(f"{get_time()}数据不是以data开头{line}")
        # if not answer:
        #     print(f"{get_time()},chat_DAI_data 没有获取到数据\n{package_list}")
    elif service == "chat_rag":
        package_list = []
        for line in response.iter_lines():
            package_list.append(line)
            if not first_packet_time:
                # 记录第一个数据包返回的时间（精确到毫秒）
                first_packet_time = time.time()
            # 处理响应数据
            if line.startswith(b"data: "):
                json_data = line[6:]
                try:
                    data_dict = json.loads(json_data)
                except json.JSONDecodeError:
                    print("json loads error:->{}".format(line))
                    continue
                return_type = data_dict.get("data", {}).get("return_type", "")
                if return_type == "rag":
                    return_data = data_dict.get("data", {}).get(
                        "return_data", {}).get(return_type, {})
                    content = return_data.get("answer_text")
                    if content and not first_packet_time_2:
                        first_packet_time_2 = time.time()
                    # print(content)
                    content_length = len(content)
                    total_character_count += content_length
                    answer += content
        #     else:
        #         print(f"{get_time()}数据不是以data开头{line}")
        # if not answer:
        #     print(f"{get_time()},chat_rag 没有获取到数据\n{package_list}")
    elif service == "simulate_chat" or service == "course_qa":
        package_list = []
        for line in response.iter_lines():
            package_list.append(line)
            if not first_packet_time:
                # 记录第一个数据包返回的时间（精确到毫秒）
                first_packet_time = time.time()
            # 处理响应数据
            if line.startswith(b"data: "):
                json_data = line[6:]
                try:
                    data_dict = json.loads(json_data)
                except json.JSONDecodeError:
                    print("json loads error:->{}".format(line))
                    continue
                content = data_dict.get("data", {}).get("answer_text", "")
                if content and not first_packet_time_2:
                    first_packet_time_2 = time.time()
                    # print(content)
                content_length = len(content)
                total_character_count += content_length
                answer += content
        #     else:
        #         print(f"{get_time()}数据不是以data开头{line}")
        # if not answer:
        #     print(f"{get_time()},simulate_chat or course_qa没有获取到数据\n{package_list}")

    # 等待整个请求完全返回
    response.close()
    # 记录请求完成的时间（精确到毫秒）
    request_complete_time = time.time()
    # 转换为易读的时分秒格式（精确到毫秒）并保留毫秒的前3位
    request_send_time_str = datetime.fromtimestamp(request_send_time).strftime(
        '%Y-%m-%d %H:%M:%S.%f')[:23]
    first_packet_time_str = datetime.fromtimestamp(first_packet_time).strftime(
        '%Y-%m-%d %H:%M:%S.%f')[:23]
    request_complete_time_str = datetime.fromtimestamp(
        request_complete_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    if first_packet_time_2:
        first_packet_time_str_2 = datetime.fromtimestamp(
            first_packet_time_2).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
        first_packet_duration_2 = (first_packet_time_2 - request_send_time)
        tokens_time_2 = (request_complete_time - first_packet_time_2)
        performance_metric_2 = (total_character_count) / tokens_time_2
    else:
        performance_metric_2 = 0
        first_packet_duration_2 = 0
        first_packet_time_str_2 = ""

    # 计算并打印首包耗时和总耗时
    first_packet_duration = (first_packet_time - request_send_time)

    total_duration = request_complete_time - request_send_time
    tokens_time = (request_complete_time - first_packet_time)
    # 计算性能指标
    performance_metric = (total_character_count) / tokens_time
    # print("性能指标: {:.3f} 字/秒".format(performance_metric))
    # print("=" * 50)
    result = {
        "请求发送的时间": request_send_time_str,
        "第一个数据包返回的时间": first_packet_time_str,
        "第一个有效数据包返回的时间": first_packet_time_str_2,
        "请求完成的时间": request_complete_time_str,
        "首包耗时(s)": first_packet_duration,
        "有效数据_首包耗时(s)": first_packet_duration_2,
        "总耗时(s)": total_duration,
        # "输入": len(qa_prompt),
        "输出字数": total_character_count,
        "性能指标(字/秒)": performance_metric,
        "有效数字_性能指标(字/秒)": performance_metric_2,
        "answer": answer
    }
    return result


def get_now(format=None):
    if format:
        return datetime.now().strftime("%Y%m%d")
    else:
        return datetime.now().strftime("%Y%m%d%H%M%S")


def save_print_to_excel(thread, loop, service, path, data):
    results = []
    output_path = "{prefix_path}{sep}{service}{sep}{T}".format(
        prefix_path="./workspace/load_res",
        sep=os.sep,
        T=get_now("day"),
        service=service)
    output_file = f"Tread_{thread}_loop_{loop}_{get_now()}_{service}.xlsx"

    excel_path = f"{output_path}{os.sep}{output_file}"
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    with concurrent.futures.ThreadPoolExecutor(max_workers=thread) as executor:
        futures = []
        index = 0

        for question in range(loop):
            futures.append(executor.submit(send_stream, service, data, path))
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as exc:
                print(f"Error: {exc}")
    df = pd.DataFrame(results)
    try:
        #首包耗时
        first_cost = round(df["首包耗时(s)"].mean(), 2)
        #   有效首包耗时
        real_first_cost = round(df["有效数据_首包耗时(s)"].mean(), 2)
        #   总耗时平均值
        total_cost = round(df["总耗时(s)"].mean(), 2)
        #   性能指标字/秒
        speed_word = round(df["性能指标(字/秒)"].mean(), 2)
        #   有效数据性能指标字/秒
        real_speed_word = round(df["有效数字_性能指标(字/秒)"].mean(), 2)
        #   首包耗时计算

        orion_first_cost = sorted(df["首包耗时(s)"].to_list())
        first_line_90 = int(len(orion_first_cost) * 0.9)
        first_line_99 = int(len(orion_first_cost) * 0.99)
        first_cost_99 = orion_first_cost[first_line_99]
        first_cost_90 = orion_first_cost[first_line_90]
        #   总耗时计算

        orion_total_cost = sorted(df["总耗时(s)"].to_list())
        total_line_90 = int(len(orion_total_cost) * 0.9)
        total_line_99 = int(len(orion_total_cost) * 0.99)
        total_cost_99 = orion_total_cost[total_line_99]
        total_cost_90 = orion_total_cost[total_line_90]
        average_row = {
            "请求发送的时间": "Average",
            "首包耗时(s)": first_cost,
            "有效数据_首包耗时(s)": real_first_cost,
            "总耗时(s)": total_cost,
            "性能指标(字/秒)": speed_word,
            "有效数字_性能指标(字/秒)": real_speed_word
        }
        title_row = {"有效数据_首包耗时(s)": "99 line", "总耗时(s)": "90 line"}
        first_line_row = {
            "请求发送的时间": "首包耗时",
            "有效数据_首包耗时(s)": first_cost_99,
            "总耗时(s)": first_cost_90
        }
        total_line_row = {
            "请求发送的时间": "总耗时",
            "有效数据_首包耗时(s)": total_cost_99,
            "总耗时(s)": total_cost_90
        }

        average_row_to_all = {
            "接口": path,
            "并发数": thread,
            "执行次数": loop,
            "首包耗时(s)": first_cost,
            "有效数据_首包耗时(s)": real_first_cost,
            "总耗时(s)": total_cost,
            "性能指标(字/秒)": speed_word,
            "有效数字_性能指标(字/秒)": real_speed_word
        }
        all_res.append(average_row_to_all)
        df = pd.concat([df, pd.DataFrame([average_row])], ignore_index=True)
        df = pd.concat([df, pd.DataFrame([title_row])], ignore_index=True)
        df = pd.concat([df, pd.DataFrame([first_line_row])], ignore_index=True)
        df = pd.concat([df, pd.DataFrame([total_line_row])], ignore_index=True)
        df.to_excel(excel_path, index=False)
        print(f"save result file to: {output_path}/{output_file}")
    except BaseException:
        print(f"计算平均值异常\n{traceback.format_exc()}")
    return results


def run_single_scene():
    for task in task_list:
        thread = task.get("thread")
        loop = task.get("loop")
        service = task.get("service")
        data = task.get("data")
        path = task.get("path")
        print("thread:{}\nloop:{}".format(thread, loop))
        save_print_to_excel(thread, loop, service, path, data)


def run_1():
    thread = task_list[0].get("thread")
    loop = task_list[0].get("loop")
    service = task_list[0].get("service")
    data = task_list[0].get("data")
    path = task_list[0].get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    save_print_to_excel(thread, loop, service, path, data)


def run_5():
    thread = task_list[1].get("thread")
    loop = task_list[1].get("loop")
    service = task_list[1].get("service")
    data = task_list[1].get("data")
    path = task_list[1].get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    save_print_to_excel(thread, loop, service, path, data)


def run_6():
    thread = task_list[2].get("thread")
    loop = task_list[2].get("loop")
    service = task_list[2].get("service")
    data = task_list[2].get("data")
    path = task_list[2].get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    save_print_to_excel(thread, loop, service, path, data)
