# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : load_test.py
<AUTHOR> <EMAIL>
@Time   : 2023-12-06 10:29:16
@Version: v1.0
"""
import os, sys

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print(BASE_DIR)
sys.path.append(BASE_DIR)
import requests
import json
import time
import concurrent.futures
from datetime import datetime
import pandas as pd
import uuid
import traceback
from test_data import course as teach_course,chat_dai,chat_rag,chat_v2,simulate_data,loop_1,loop_3,loop_2,loop_1_thread,loop_2_thread,loop_3_thread

"""

华住接口测试

"""
all_res = []
#   聊天接口——DAI，supperboss_qa
chat_DAI_data = {
    "session_id":
    "85bc7c07-055b-4ef3-be99-cae2fa9b7b1c",
    "recommend":
    0,
    "query":
    "最近几周的出租率",
    "sence":
    "superboss_qa",
    "request_id":
    "55e92073-e959-4b9c-983c-d2395117ebc8",
    "store_id":
    "9007534",
    "dataaccess_token":
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoic3lzdGVtIiwiaG90ZWxfaWQiOiI5MDA3NTM0IiwiY3JlYXRlX3RpbWUiOiIyMDI0LTA1LTEzIiwiZXhwIjoxNzUxNjE5NTE1fQ.OfBbSuOBO1wRkkoOQiAA3njX49egLJVz8-DfYinxPfI"
}
res_all = []
#   线程数和循环次数

task_list = [
    {
        "thread": loop_1_thread,
        "loop": loop_1,
        "service": "chat_DAI_data",
        "data": chat_DAI_data,
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": loop_2_thread,
        "loop": loop_2,
        "service": "chat_DAI_data",
        "data": chat_DAI_data,
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": loop_3_thread,
        "loop": loop_3,
        "service": "chat_DAI_data",
        "data": chat_DAI_data,
        "path": "/v1/ctai/ai_chat"
    },
]
task_list_test = [
    {
        "thread": 1,
        "loop": 2,
        "service": "chat_DAI_data",
        "data": chat_DAI_data,
        "path": "/v1/ctai/ai_chat"
    }
]

def get_now(format=None):
    if format:
        return datetime.now().strftime("%Y%m%d")
    else:
        return datetime.now().strftime("%Y%m%d%H%M%S")

def send_stream(service, data, path):
    """
    流式请求
    :param qa_prompt:
    :param key:
    :param env:
    :return:
    """

    url = "http://self-hosting.chatmax.net:100" + path
    headers = {"content-Type": "application/json"}
    # 记录请求发送的时间（精确到毫秒）
    request_send_time = time.time()
    response = requests.post(url, headers=headers, json=data, stream=True)
    first_packet_time = None
    first_packet_time_2 = None
    total_character_count = 0
    answer = ""
    # 处理响应数据
    error = 0

    if service == "chat_DAI_week_report" or service == "chat_DAI_data":
        package_list = []
        for line in response.iter_lines():
            package_list.append(line)
            if not first_packet_time:
                # 记录第一个数据包返回的时间（精确到毫秒）
                first_packet_time = time.time()
            # 处理响应数据
            if line.startswith(b"data: "):
                json_data = line[6:]
                try:
                    data_dict = json.loads(json_data)
                except json.JSONDecodeError:
                    print("json loads error:->{}".format(line))
                    continue
                return_type = data_dict.get("data", {}).get("return_type", "")
                return_data = data_dict.get("data",
                                            {}).get("return_data",
                                                    {}).get(return_type, {})
                if 'insight' == return_type:
                    content = return_data.get("text")
                    #   计算第一个有效数据返回的时间
                    if content and not first_packet_time_2:
                        first_packet_time_2 = time.time()
                    content_length = len(content)
                    total_character_count += content_length
                    answer += content
                if return_type=="tips":
                    content = data_dict.get("data", {}).get(
                        "return_data", {}).get(return_type, {})
                    if content and not first_packet_time_2:
                        first_packet_time_2 = time.time()
                    # print(content)
                    content_length = len(content)
                    total_character_count += content_length
                    answer += content
            # else:
            #     print(f"{get_time()}数据不是以data开头{line}")
        # if not answer:
        #     print(f"{get_time()},chat_DAI_data 没有获取到数据\n{package_list}")

    # 等待整个请求完全返回
    response.close()
    # 记录请求完成的时间（精确到毫秒）
    request_complete_time = time.time()
    # 转换为易读的时分秒格式（精确到毫秒）并保留毫秒的前3位
    request_send_time_str = datetime.fromtimestamp(request_send_time).strftime(
        '%Y-%m-%d %H:%M:%S.%f')[:23]
    first_packet_time_str = datetime.fromtimestamp(first_packet_time).strftime(
        '%Y-%m-%d %H:%M:%S.%f')[:23]
    request_complete_time_str = datetime.fromtimestamp(
        request_complete_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    if first_packet_time_2:
        first_packet_time_str_2 = datetime.fromtimestamp(
            first_packet_time_2).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
        first_packet_duration_2 = (first_packet_time_2 - request_send_time)
        tokens_time_2 = (request_complete_time - first_packet_time_2)
        performance_metric_2 = (total_character_count) / tokens_time_2
    else:
        performance_metric_2 = 0
        first_packet_duration_2 = 0
        first_packet_time_str_2 = ""

    # 计算并打印首包耗时和总耗时
    first_packet_duration = (first_packet_time - request_send_time)
    total_duration = request_complete_time - request_send_time
    tokens_time = (request_complete_time - first_packet_time)
    # 计算性能指标
    performance_metric = (total_character_count) / tokens_time
    # print("性能指标: {:.3f} 字/秒".format(performance_metric))
    # print("=" * 50)
    result = {
        "请求发送的时间": request_send_time_str,
        "第一个数据包返回的时间": first_packet_time_str,
        "第一个有效数据包返回的时间": first_packet_time_str_2,
        "请求完成的时间": request_complete_time_str,
        "首包耗时(s)": first_packet_duration,
        "有效数据_首包耗时(s)": first_packet_duration_2,
        "总耗时(s)": total_duration,
        # "输入": len(qa_prompt),
        "输出字数": total_character_count,
        "性能指标(字/秒)": performance_metric,
        "有效数字_性能指标(字/秒)": performance_metric_2,
        "answer": answer
    }
    return result

def save_print_to_excel(thread, loop, service, path, data):
    results = []
    output_path = "{prefix_path}{sep}{service}{sep}{T}".format(
        prefix_path="./workspace/load_res",
        sep=os.sep,
        T=get_now("day"),
        service=service)
    output_file = f"Tread_{thread}_loop_{loop}_{get_now()}_{service}.xlsx"

    excel_path = f"{output_path}{os.sep}{output_file}"
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    with concurrent.futures.ThreadPoolExecutor(max_workers=thread) as executor:
        futures = []

        for index in range(loop):
            chat_DAI_data["query"] = chat_dai[index % len(chat_dai)]
            data = chat_DAI_data
            futures.append(executor.submit(send_stream, service, data, path))
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as exc:
                print(f"Error: {exc}")
    df = pd.DataFrame(results)
    try:
        #首包耗时
        first_cost = round(df["首包耗时(s)"].mean(), 2)
        #   有效首包耗时
        real_first_cost = round(df["有效数据_首包耗时(s)"].mean(), 2)
        #   总耗时平均值
        total_cost = round(df["总耗时(s)"].mean(), 2)
        #   性能指标字/秒
        speed_word = round(df["性能指标(字/秒)"].mean(), 2)
        #   有效数据性能指标字/秒
        real_speed_word = round(df["有效数字_性能指标(字/秒)"].mean(), 2)
        #   首包耗时计算

        orion_first_cost = sorted(df["首包耗时(s)"].to_list())
        first_line_90 = int(len(orion_first_cost) * 0.9)
        first_line_99 = int(len(orion_first_cost) * 0.99)
        first_cost_99 = orion_first_cost[first_line_99]
        first_cost_90 = orion_first_cost[first_line_90]
        #   总耗时计算

        orion_total_cost = sorted(df["总耗时(s)"].to_list())
        total_line_90 = int(len(orion_total_cost) * 0.9)
        total_line_99 = int(len(orion_total_cost) * 0.99)
        total_cost_99 = orion_total_cost[total_line_99]
        total_cost_90 = orion_total_cost[total_line_90]
        average_row = {
            "请求发送的时间": "Average",
            "首包耗时(s)": first_cost,
            "有效数据_首包耗时(s)": real_first_cost,
            "总耗时(s)": total_cost,
            "性能指标(字/秒)": speed_word,
            "有效数字_性能指标(字/秒)": real_speed_word
        }
        title_row = {"有效数据_首包耗时(s)": "99 line", "总耗时(s)": "90 line"}
        first_line_row = {
            "请求发送的时间": "首包耗时",
            "有效数据_首包耗时(s)": first_cost_99,
            "总耗时(s)": first_cost_90
        }
        total_line_row = {
            "请求发送的时间": "总耗时",
            "有效数据_首包耗时(s)": total_cost_99,
            "总耗时(s)": total_cost_90
        }

        average_row_to_all = {
            "接口": path,
            "并发数": thread,
            "执行次数": loop,
            "首包耗时(s)": first_cost,
            "有效数据_首包耗时(s)": real_first_cost,
            "总耗时(s)": total_cost,
            "性能指标(字/秒)": speed_word,
            "有效数字_性能指标(字/秒)": real_speed_word
        }
        all_res.append(average_row_to_all)
        df = pd.concat([df, pd.DataFrame([average_row])], ignore_index=True)
        df = pd.concat([df, pd.DataFrame([title_row])], ignore_index=True)
        df = pd.concat([df, pd.DataFrame([first_line_row])], ignore_index=True)
        df = pd.concat([df, pd.DataFrame([total_line_row])], ignore_index=True)
        df.to_excel(excel_path, index=False)
        print(f"save result file to: {output_path}/{output_file}")
    except BaseException:
        print(f"计算平均值异常\n{traceback.format_exc()}")
    return results


def run_1():
    thread = task_list[0].get("thread")
    loop = task_list[0].get("loop")
    service = task_list[0].get("service")
    data = task_list[0].get("data")
    path = task_list[0].get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    save_print_to_excel(thread, loop, service, path, data)


def run_5():
    thread = task_list[1].get("thread")
    loop = task_list[1].get("loop")
    service = task_list[1].get("service")
    data = task_list[1].get("data")
    path = task_list[1].get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    save_print_to_excel(thread, loop, service, path, data)


def run_6():
    thread = task_list[2].get("thread")
    loop = task_list[2].get("loop")
    service = task_list[2].get("service")
    data = task_list[2].get("data")
    path = task_list[2].get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    save_print_to_excel(thread, loop, service, path, data)


def run_test():
    thread = task_list_test[0].get("thread")
    loop = task_list_test[0].get("loop")
    service = task_list_test[0].get("service")
    data = task_list_test[0].get("data")
    path = task_list_test[0].get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    save_print_to_excel(thread, loop, service, path, data)

run_test()