# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : load_test.py
<AUTHOR> <EMAIL>
@Time   : 2023-12-06 10:29:16
@Version: v1.0
"""
import os, sys
import copy

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print(BASE_DIR)
sys.path.append(BASE_DIR)
import requests
import json
import time
import concurrent.futures
from datetime import datetime
import pandas as pd
import uuid
from concurrent.futures.thread import ThreadPoolExecutor
from test_data import course as teach_course, chat_dai, chat_rag, chat_v2, simulate_data, all_chat_data, loop_1, loop_3, \
    loop_2, loop_1_thread, loop_2_thread, loop_3_thread

import traceback

"""

华住接口测试

"""
all_res = []


def get_exe(max_works=1):
    exe = ThreadPoolExecutor(max_workers=max_works)
    return exe


#   聊天接口——DAI，supperboss_qa 出租率
chat_DAI_data = {
    "session_id":
        "85bc7c07-055b-4ef3-be99-cae2fa9b7b1c",
    "recommend":
        0,
    "query":
        "",
    "sence":
        "superboss_qa",
    "request_id":
        "55e92073-e959-4b9c-983c-d2395117ebc8",
    "store_id":
        "9007534",
    "dataaccess_token":
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoic3lzdGVtIiwiaG90ZWxfaWQiOiI5MDA3NTM0IiwiY3JlYXRlX3RpbWUiOiIyMDI0LTA1LTEzIiwiZXhwIjoxNzUxNjE5NTE1fQ.OfBbSuOBO1wRkkoOQiAA3njX49egLJVz8-DfYinxPfI"
}
#   线程数和循环次数
task_list = [
    {
        "thread": loop_1_thread,
        "loop": loop_1,
        "service": "mixed",
        "data": chat_DAI_data,
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": loop_2_thread,
        "loop": loop_2,
        "service": "mixed",
        "data": chat_DAI_data,
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": loop_3_thread,
        "loop": loop_3,
        "service": "mixed",
        "data": chat_DAI_data,
        "path": "/v1/ctai/ai_chat"
    }
]
task_list_test = [

    {
        "thread": 1,
        "loop": 1,
        "service": "mixed",
        "data": chat_DAI_data,
        "path": "/v1/ctai/ai_chat"
    }
]


def get_now(format=None):
    if format:
        return datetime.now().strftime("%Y%m%d")
    else:
        return datetime.now().strftime("%Y%m%d%H%M%S")


def send_stream(service, data, path):
    """
    流式请求
    :param qa_prompt:
    :param key:
    :param env:
    :return:
    """

    url = "http://self-hosting.chatmax.net:100" + path
    headers = {"content-Type": "application/json"}
    # 记录请求发送的时间（精确到毫秒）
    request_send_time = time.time()
    response = requests.post(url, headers=headers, json=data, stream=True)
    first_packet_time = None
    first_packet_time_2 = None
    total_character_count = 0
    answer = ""
    # 处理响应数据
    error = 0
    msg = ""
    package_list = []
    intent_list = []
    for line in response.iter_lines():
        package_list.append(line)
        if not first_packet_time:
            # 记录第一个数据包返回的时间（精确到毫秒）
            first_packet_time = time.time()
        # 处理响应数据
        if line.startswith(b"data: "):
            json_data = line[6:]
            try:
                data_dict = json.loads(json_data)
            except json.JSONDecodeError:
                print("json loads error:->{}".format(line))
                continue
            try:
                return_type = data_dict.get("data", {}).get("return_type", "")
                return_data = data_dict.get("data", {}).get("return_data", {}).get(return_type, {})
            except BaseException:
                return_type = ""
                print(f"获取return_type或者return_data报错")
                print(f"{data_dict}")
                print(traceback.format_exc())
            if return_type == "rag":
                return_data = data_dict.get("data", {}).get(
                    "return_data", {}).get(return_type, {})
                content = return_data.get("answer_text")
                if content and not first_packet_time_2:
                    first_packet_time_2 = time.time()
                # print(content)
                content_length = len(content)
                total_character_count += content_length
                answer += content
            elif return_type == "tips":
                content = data_dict.get("data", {}).get(
                    "return_data", {}).get(return_type, {})
                if content and not first_packet_time_2:
                    first_packet_time_2 = time.time()
                # print(content)
                content_length = len(content)
                total_character_count += content_length
                answer += content
            elif 'insight' == return_type:
                return_data = data_dict.get("data",
                                            {}).get("return_data",
                                                    {}).get(return_type, {})
                content = return_data.get("text")
                #   计算第一个有效数据返回的时间
                if content and not first_packet_time_2:
                    first_packet_time_2 = time.time()
                content_length = len(content)
                total_character_count += content_length
                answer += content
            elif return_type == "progress":
                intent_list.append(data_dict.get("data", {}).get("return_data", {}).get(return_type))
            elif data_dict.get("code") != 0:
                msg = data_dict.get("msg")
    if not answer:
        if not first_packet_time_2:
            first_packet_time_2 = time.time()
        msg = [msg] + package_list
        print(f"{get_now()},{service} 没有获取到数据\n{package_list}")
    # 等待整个请求完全返回
    response.close()
    # 记录请求完成的时间（精确到毫秒）
    request_complete_time = time.time()
    # 转换为易读的时分秒格式（精确到毫秒）并保留毫秒的前3位
    request_send_time_str = datetime.fromtimestamp(request_send_time).strftime(
        '%Y-%m-%d %H:%M:%S.%f')[:23]
    first_packet_time_str = datetime.fromtimestamp(first_packet_time).strftime(
        '%Y-%m-%d %H:%M:%S.%f')[:23]
    request_complete_time_str = datetime.fromtimestamp(
        request_complete_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    if first_packet_time_2:
        first_packet_time_str_2 = datetime.fromtimestamp(
            first_packet_time_2).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
        first_packet_duration_2 = (first_packet_time_2 - request_send_time)
        tokens_time_2 = (request_complete_time - first_packet_time_2)
        performance_metric_2 = (total_character_count) / tokens_time_2
    else:
        performance_metric_2 = 0
        first_packet_duration_2 = 0
        first_packet_time_str_2 = ""

    # 计算并打印首包耗时和总耗时
    first_packet_duration = (first_packet_time - request_send_time)

    total_duration = request_complete_time - request_send_time
    tokens_time = (request_complete_time - first_packet_time)
    # 计算性能指标
    performance_metric = (total_character_count) / tokens_time
    # print("性能指标: {:.3f} 字/秒".format(performance_metric))
    # print("=" * 50)
    result = {
        "请求发送的时间": request_send_time_str,
        "第一个数据包返回的时间": first_packet_time_str,
        "第一个有效数据包返回的时间": first_packet_time_str_2,
        "请求完成的时间": request_complete_time_str,
        "首包耗时(s)": first_packet_duration,
        "有效数据_首包耗时(s)": first_packet_duration_2,
        "总耗时(s)": total_duration,
        # "输入": len(qa_prompt),
        "输出字数": total_character_count,
        "性能指标(字/秒)": performance_metric,
        "有效数字_性能指标(字/秒)": performance_metric_2,
        "answer": answer,
        "query": data["query"],
        "request_id": data["request_id"],
        "msg": msg,
        "progress": json.dumps(intent_list, ensure_ascii=False),
        "session_id": data.get("session_id", "")
    }
    return result


def save_print_to_excel(thread, loop, service, path, data, run_number):
    results = []
    output_path = "{prefix_path}{sep}{run_number}{sep}{type_file}{sep}{service}".format(
        prefix_path=get_now("day"),
        sep=os.sep,
        service=service,
        type_file="Mixed_scenes",
        run_number=run_number
    )
    output_file = f"{thread}-{loop}-{get_now()}-{service}.xlsx"
    excel_path = f"{output_path}{os.sep}{output_file}"
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    with concurrent.futures.ThreadPoolExecutor(max_workers=thread) as executor:
        futures = []
        for index in range(loop):
            data = None
            if service == "mixed":
                chat_data = copy.deepcopy(chat_DAI_data)
                chat_data["query"] = all_chat_data[index % len(all_chat_data)]
                chat_data["session_id"] = str(uuid.uuid4())
                chat_data["request_id"] = str(uuid.uuid4())
                data = chat_data
            futures.append(executor.submit(send_stream, service, data, path))
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as exc:
                print(f"Error: {exc}")
    df = pd.DataFrame(results)
    df.to_excel(excel_path, index=False)
    return excel_path

def run_1(run_number):
    thread = task_list[0].get("thread")
    loop = task_list[0].get("loop")
    service = task_list[0].get("service")
    data = task_list[0].get("data")
    path = task_list[0].get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    return save_print_to_excel(thread, loop, service, path, data, run_number)


def run_5(run_number):
    thread = task_list[1].get("thread")
    loop = task_list[1].get("loop")
    service = task_list[1].get("service")
    data = task_list[1].get("data")
    path = task_list[1].get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    return save_print_to_excel(thread, loop, service, path, data, run_number)


def run_10(run_number):
    thread = task_list[2].get("thread")
    loop = task_list[2].get("loop")
    service = task_list[2].get("service")
    data = task_list[2].get("data")
    path = task_list[2].get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    return save_print_to_excel(thread, loop, service, path, data, run_number)


def run_test(run_number):
    thread = task_list_test[0].get("thread")
    loop = task_list_test[0].get("loop")
    service = task_list_test[0].get("service")
    data = task_list_test[0].get("data")
    path = task_list_test[0].get("path")
    print("thread:{}\nloop:{}".format(thread, loop))
    return save_print_to_excel(thread, loop, service, path, data, run_number)


def save(run_number):
    df = pd.DataFrame(all_res)
    df.to_excel(f"./{get_now('day')}/{run_number}/Mixed_scenes/{get_now()}_chat_other_all_res.xlsx", index=False)

# run_test(run_number=1)
