# 计算当天执行脚本的次数,获取当前执行存放文件夹

folder_path=$(date '+%Y%m%d')
start_t=$(date '+%Y-%m-%d %H:%M:%S')
# 获取文件夹中已存在的数字型子文件夹名称列表
existing_folders=($(ls  ${folder_path}   2>/dev/null |grep -oE '[0-9]+' |sort -n))

# 确定下一个应该创建的数字型子文件夹的名称
if [ ${#existing_folders[@]} -eq 0 ]; then
    next_folder=1
else
    last_folder=${existing_folders[${#existing_folders[@]}-1]}
    next_folder=$((last_folder + 1))
fi
# 执行环境
env="dev"
#single会顺序执行多个接口场景。一级指标、非一级指标、超级老板知识问答、模拟对话、课程答疑、较优一级指标数据问答、较优超级老板知识问答、较优非一级指标数据问答
#  一级指标
python3 dev_load_report_new.py --env $env  --c 1 --n 1 --action "single" --i "primary_indicator" --run_number $next_folder 

#  非一级指标
python3 dev_load_report_new.py --env $env  --c 5 --n 2 --action "single" --i "no_primary_indicator" --run_number $next_folder 

#  rag
python3 dev_load_report_new.py --env $env  --c 5 --n 2 --action "single" --i "rag" --run_number $next_folder 

#  模拟对话
python3 dev_load_report_new.py --env $env  --c 5 --n 2 --action "single" --i "simulate_chat" --run_number $next_folder 

#  课程答疑
python3 dev_load_report_new.py --env $env  --c 5 --n 2 --action "single" --i "course_qa" --run_number $next_folder

#  较优一级指标数据问答
python3 dev_load_report_new.py --env $env  --c 5 --n 2 --action "single" --i "good_primary_indicator" --run_number $next_folder

#  较优非一级指标数据问答
python3 dev_load_report_new.py --env $env  --c 5 --n 2 --action "single" --i "good_no_primary_indicator" --run_number $next_folder

#  较优RAG数据问答
python3 dev_load_report_new.py --env $env  --c 5 --n 2 --action "single" --i "good_rag" --run_number $next_folder


#  混合场景
python3 dev_load_report_new.py --env $env  --c 1 --n 1 --action "mixed" --i "ai_chat" --run_number $next_folder &
python3 dev_load_report_new.py --env $env  --c 2 --n 1 --action "mixed" --i "simulate_chat" --run_number $next_folder &
python3 dev_load_report_new.py --env $env  --c 2 --n 1 --action "mixed" --i "course_qa" --run_number $next_folder &

wait
echo "等待所有任务完成..."
end_t=$(date '+%Y-%m-%d %H:%M:%S')
echo "开始时间：$start_t"
echo "结束时间：$end_t"
start_seconds=$(date -j -f "%Y-%m-%d %H:%M:%S" "$start_t" "+%s")
end_seconds=$(date -j -f "%Y-%m-%d %H:%M:%S" "$end_t" "+%s")

diff_seconds=$((end_seconds - start_seconds))
diff_minutes=$((diff_seconds / 60))

echo "耗时：$diff_minutes 分钟"
#  计算数据
python3 dev_load_report_new.py --action "calc"  --run_number $next_folder

#  发送通知,只在DEV环境下可以发送
# python3 dev_load_report_new.py --action "send"