# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : load_test.py
<AUTHOR> <EMAIL>
@Time   : 2023-12-06 10:29:16
@Version: v1.0
"""
import os, sys

BASE_DIR = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
print(BASE_DIR)
sys.path.append(BASE_DIR)
import requests
import json
import time
import concurrent.futures
from datetime import datetime
import pandas as pd
import uuid
from concurrent.futures.thread import ThreadPoolExecutor
from test_data import course as teach_course, chat_dai, chat_rag, chat_v2, simulate_data, chat_dai_good, chat_rag_good, \
    chat_v2_good
import traceback
import copy
import pymysql
from dbutils.pooled_db import PooledDB
import threading

lock = threading.Lock()

"""

华住接口测试

"""
all_res = []
#   记录所有文件
all_file_list = []


def get_exe(max_works=1):
    exe = ThreadPoolExecutor(max_workers=max_works)
    return exe


def get_now(format=None):
    if format:
        return datetime.now().strftime("%Y%m%d")
    else:
        return datetime.now().strftime("%Y%m%d%H%M%S")


#   聊天接口——DAI，supperboss_qa 出租率
chat_DAI_data = {
    "session_id":
        "85bc7c07-055b-4ef3-be99-cae2fa9b7b1c",
    "recommend":
        0,
    "query":
        "",
    "sence":
        "superboss_qa",
    "request_id":
        "55e92073-e959-4b9c-983c-d2395117ebc8",
    "store_id":
        "9007534",
    "dataaccess_token":
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoic3lzdGVtIiwiaG90ZWxfaWQiOiI5MDA3NTM0IiwiY3JlYXRlX3RpbWUiOiIyMDI0LTA1LTEzIiwiZXhwIjoxNzUxNjE5NTE1fQ.OfBbSuOBO1wRkkoOQiAA3njX49egLJVz8-DfYinxPfI"
}
#   模拟对话
simulate_chat = [
    {
        "info": {
            "responsibility":
                "前台员工：作为酒店前线的代表，该角色承担着满足客人需求、解决客户问题的重任，同时必须维护酒店的服务质量标准。此外，该员工还负责准确、高效地进行系统数据录入，确保酒店运营的顺畅。",
            "description":
                "现在我们进行外部来电接听和转接的考核。我将扮演来电者，你需要按照服务规范模拟305房间没有人接通的场景。\n现在是一个非常宁静的午后，你正在酒店前台值班，突然前台电话响了，你会…",
            "male_background":
                "张先生是一位工作认真负责的职场人士，与王小姐是同事关系。他对工作非常敬业，积极主动地帮助同事解决问题。",
            "male_motivation":
                "张先生想联系305房间的王小姐，可能是因为有重要事情需要与她商讨或交流。",
            "female_background":
                "张女士是一位工作认真负责的职场人士，与王小姐是同事关系。她对工作非常敬业，积极主动地帮助同事解决问题。",
            "female_motivation":
                "张小姐想联系305房间的王小姐，可能是因为有重要事情需要与她商讨或交流。",
            "assessment_points":
                "考核要点主要包括五个方面：电话问候、聆听与记录、核实信息、无人应答处理以及礼貌道别",
            "scoring_criteria": [{
                "content": "使用标准的电话问候语",
                "score": 20,
                "expected_behavior": True
            }, {
                "content": "前台人员有确认房客信息",
                "score": 20,
                "expected_behavior": True
            }, {
                "content": "核实来电者提供房间号",
                "score": 10,
                "expected_behavior": True
            }, {
                "content": "核实来电者提供房客姓名",
                "score": 10,
                "expected_behavior": True
            }, {
                "content": "礼貌地处理无人应答情况并询问是否留言",
                "score": 20,
                "expected_behavior": True
            }, {
                "content": "使用礼貌的道别语结束通话，出现了感谢和再见的话术",
                "score": 20,
                "expected_behavior": True
            }],
            "dialogue_example":
                "前台员工: 您好，这里是XX酒店，有什么能够为您服务的？\n来电者: 你好，我想跟住在305房间的王xx小姐联系。\n前台员工：跟您确认一下，您说的是305房间的王xx小姐是吗？\n来电者:是的。\n前台员工: 好的，非常感谢您的来电，我是前台的李经理，请问您是...？\n来电者: 我是她的同事，张先生。\n前台员工: 好的，张先生，请稍等，我查询一下。\n来电者：好的，我等一下。\n前台员工：很抱歉，没有查到王小姐的入住信息。请问还有什么能够为您服务的吗？\n来电者: 没有了。\n前台员工: 好的，张先生，感谢您的来电，祝您有美好的一天，再见。",
            "end_condition":
                "前台回答内容包括：\"好的\"，感谢您的来电，再见。同时匹配到\"好的\"再见语义则触发结束。"
        },
        "dialog": "前台员工: 你好",
        "examiner_role_name": "来电者",
        "examinee_role_name": "前台员工",
        "gender": "female",
        "topic_id": "1",
        "request_id": "7edbfe51-c988-48f7-9bc8-3ee8846101a1",
        "topic": "外部呼入电话的接待和转接场景",
        "description": "现在是一个非常宁静的午后，你正在酒店前台值班，突然前台电话响了，你会"
    },
]
#   课程答疑
course_qa = {
    "session_id": "test_session_id",
    "query": "GOP是什么？",
    "context":
        "GOP课程概念～基础术语_原文\n2024年04月25日 10:19\n大家好，今天带大家五分钟快速掌握GOP基础术语。可售间夜，已售间夜，出租率。客房revpar和综合revpar,身为店长的你是不是已经被术语搞得晕头转向？别担心，今天我们就来一起学习关于GOP报表中的那些基础术语，让你快速掌握其中的含义和计算方法。\n\n首先我们得先清楚什么是GOP。GOP又叫经营毛利，是由营业收入减去营业支出得到的毛利。酒店行业把GOP报表作为考核的重要指标之一，来衡量酒店经营的结果，因此GOP报表有着酒店经营\"晴雨表\"的称号。那当我们拿到一份GOP报表该怎么来分析呢？常见的分析方法有预算比、同比、环比分析。在做任何经营数据分析时，我们都应该将实际经营数据与预算去年同期和上月情况进行对比。\n\n这里要提到另一个关键词，GOP率。它等于GOP除以营业收入，也就是营业毛利占营业收入的比值。由于酒不同酒店房量营业收入支出的量级不同，GOP的绝对值不具备可比性。这时我们可以用到GOP率来进行对比，得出不同酒店的业绩。横向对比，回到开篇，困惑店长的各种术语，他们是GOP报表中的重要信息。接下来我们逐一来分析。首先是已售间夜和可售间夜，分别是指酒店的实际房间出租数和可供房间数，两者进行相除就可以得到出租率及实际出租数占可供房量的比重。\n\n第二个是平均房价，是指客房收入和已售间夜的比值。它包含不含税和含税两种。平均房价等于客房收入除以已售间夜数，而平均房价含税则在原有的基础上乘以（1+税率）。这里扩充一下关于增值税的内容。增值税属于一种价外税，在我国商品含税定价的情况下，记账时应将含税收入换算成不含税收入和销项税额两部分。比如酒店当日房价300元，假设税率为6%，那么300乘以税率得到18元是税金，剩余282元是酒店可确认的收入。\n\n第三个是客房revpar，客房收入处于可售间夜，同时也等于平均房价乘以出租率。他们也是包含不含税和含税两种。客房revpar含税，这是在原有的基础上增加（1+税率）即客房收入乘以（1+税率）除以可售间夜数，或等于平均房价乘以（1+税率）再乘以出租率。\n\n第四个是综合revpar，即综合收入除以可售间夜。总收益是由客房收入和非客房收入两部分构成。综合收入除以可售间夜就可以得到综合revpar同理含税的综合revpar增加（1+税率）即可。\n\n最后需强调一点，revpar在不同报表中数值会不同。比如财务报表中是不含税的，而销售报表则是含税的。大家在核对报表时需要留意。到这里我们就基本说完了报表中的各类术语。\n\n接下来我们通过一个小案例实际来动手计算一下。上海某酒店房间数120间，九月份出租率90%，客房收入90万。请分别计算酒店可售间夜已售间夜，平均房价、客房revpar，大家可以先暂停视频算一算哟。下面揭晓正确答案。我们把数据以表格的形式进行梳理，酒店可售间夜数等于客房数乘以当月天数，酒店有120间客房 天数30天，得出结果为120乘以30等于3600间，用一致的出租率乘以可售间夜数就可以得到已售间夜数，3600乘以90%，得出已售间夜3240间，平均房价等于已知的当月收入90万除以已售间夜数3240间，结果为277.78元。客房revpar等于90万除以可售间夜数3600间，得到的结果为250元。大家都算对了吗？我。\n\n关于GOP的概念基础术语的讲解就结束了。大家可以再利用今天学到的知识反复实践。下节课我们一起来学习GOP报表中关于收入的知识。",
    "request_id": "264cf81b-7eaa-40b4-83f8-b4cb8a96f175"
}
#   线程数和循环次数
thread_1, loop_1 = 10, 1
thread_2, loop_2 = 30, 1
thread_3, loop_3 = 100, 500
thread_4, loop_4 = 50, 500
good_thread, good_loop, = 10, 1

task_list = [
    {
        "thread": thread_3,
        "loop": loop_3,
        "service": "chat_DAI_data",
        "data": chat_DAI_data,
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": thread_4,
        "loop": loop_4,
        "service": "chat_DAI_data",
        "data": chat_DAI_data,
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": thread_3,
        "loop": loop_3,
        "service": "chat_rag",
        "data": chat_rag,
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": thread_4,
        "loop": loop_4,
        "service": "chat_rag",
        "data": chat_rag,
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": thread_3,
        "loop": loop_3,
        "service": "simulate_chat",
        "data": simulate_chat,
        "path": "/v1/ctai/teach_simulate_chat"
    },
    {
        "thread": thread_4,
        "loop": loop_4,
        "service": "simulate_chat",
        "data": simulate_chat,
        "path": "/v1/ctai/teach_simulate_chat"
    },
    {
        "thread": thread_3,
        "loop": loop_3,
        "service": "course_qa",
        "data": course_qa,
        "path": "/v1/ctai/teach_course_qa"
    },
    {
        "thread": thread_4,
        "loop": loop_4,
        "service": "course_qa",
        "data": course_qa,
        "path": "/v1/ctai/teach_course_qa"
    },
    {
        "thread": thread_3,
        "loop": loop_3,
        "service": "chat_v2",
        "data": "",
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": thread_4,
        "loop": loop_4,
        "service": "chat_v2",
        "data": "",
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": thread_1,
        "loop": loop_1,
        "service": "chat_DAI_data",
        "data": chat_DAI_data,
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": thread_2,
        "loop": loop_2,
        "service": "chat_DAI_data",
        "data": chat_DAI_data,
        "path": "/v1/ctai/ai_chat"
    },

    {
        "thread": thread_1,
        "loop": loop_1,
        "service": "chat_rag",
        "data": chat_rag,
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": thread_2,
        "loop": loop_2,
        "service": "chat_rag",
        "data": chat_rag,
        "path": "/v1/ctai/ai_chat"
    },

    {
        "thread": thread_1,
        "loop": loop_1,
        "service": "simulate_chat",
        "data": simulate_chat,
        "path": "/v1/ctai/teach_simulate_chat"
    },
    {
        "thread": thread_2,
        "loop": loop_2,
        "service": "simulate_chat",
        "data": simulate_chat,
        "path": "/v1/ctai/teach_simulate_chat"
    },

    {
        "thread": thread_1,
        "loop": loop_1,
        "service": "course_qa",
        "data": course_qa,
        "path": "/v1/ctai/teach_course_qa"
    },
    {
        "thread": thread_2,
        "loop": loop_2,
        "service": "course_qa",
        "data": course_qa,
        "path": "/v1/ctai/teach_course_qa"
    },

    #  新增加非一级指标请求
    {
        "thread": thread_1,
        "loop": loop_1,
        "service": "chat_v2",
        "data": "",
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": thread_2,
        "loop": loop_2,
        "service": "chat_v2",
        "data": "",
        "path": "/v1/ctai/ai_chat"
    },
    #
    # {
    #     "thread": good_thread,
    #     "loop": good_loop,
    #     "service": "chat_dai_good",
    #     "data": "",
    #     "path": "/v1/ctai/ai_chat"
    # },
    # {
    #     "thread": good_thread,
    #     "loop": good_loop,
    #     "service": "chat_rag_good",
    #     "data": "",
    #     "path": "/v1/ctai/ai_chat"
    # },
    # {
    #     "thread": good_thread,
    #     "loop": good_loop,
    #     "service": "chat_v2_good",
    #     "data": "",
    #     "path": "/v1/ctai/ai_chat"
    # }
]
task_list_test = [
    {
        "thread": 1,
        "loop": 1,
        "service": "course_qa",
        "data": course_qa,
        "path": "/v1/ctai/teach_course_qa"
    },
    {
        "thread": 1,
        "loop": 1,
        "service": "simulate_chat",
        "data": simulate_chat,
        "path": "/v1/ctai/teach_simulate_chat"
    },
    {
        "thread": 1,
        "loop": 1,
        "service": "chat_v2",
        "data": "",
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": 1,
        "loop": 1,
        "service": "chat_DAI_data",
        "data": chat_DAI_data,
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": 1,
        "loop": 1,
        "service": "chat_rag",
        "data": chat_rag,
        "path": "/v1/ctai/ai_chat"
    },

    {
        "thread": 1,
        "loop": 1,
        "service": "chat_dai_good",
        "data": "",
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": 1,
        "loop": 1,
        "service": "chat_rag_good",
        "data": "",
        "path": "/v1/ctai/ai_chat"
    },
    {
        "thread": 1,
        "loop": 1,
        "service": "chat_v2_good",
        "data": "",
        "path": "/v1/ctai/ai_chat"
    }
]


def send_stream(service, data, path):
    """

    :param service:
    :param data:
    :param path:
    :return:
    """
    url = "http://self-hosting.chatmax.net:100" + path
    headers = {"content-Type": "application/json"}
    # 记录请求发送的时间（精确到毫秒）
    request_send_time = time.time()
    response = requests.post(url, headers=headers, json=data, stream=True)
    first_packet_time = None
    first_packet_time_2 = None
    total_character_count = 0
    answer = ""
    intent_list = []
    # 处理响应数据
    error = 0
    msg = ""
    content = ""
    if service == "report_day" or service == "report_week":
        for line in response.iter_lines():
            package_list = []
            package_list.append(line)
            if not first_packet_time:
                package_list.append(line)
                # 记录第一个数据包返回的时间（精确到毫秒）
                first_packet_time = time.time()
            # 处理响应数据
            if line.startswith(b"data: "):
                json_data = line[6:]
                try:
                    data_dict = json.loads(json_data)
                except json.JSONDecodeError:
                    print("json loads error:->{}".format(line))
                    continue
                # answer += data_dict.get("outputs", {}).get("answer", "")

                if 'outputs' in data_dict:
                    data = data_dict['outputs']
                    if "answer" in data:
                        content = data["answer"]
                        #   计算第一个有效数据返回的时间
                        if content and not first_packet_time_2:
                            first_packet_time_2 = time.time()
                        # print(content)
                        content_length = len(content)
                        total_character_count += content_length
                        answer += content
                    else:
                        pass
                        # log.error("response not answer_text:->{}".format(line))
                else:
                    print("response not data:->{}".format(line))
        #     else:
        #         print(f"{get_time()}数据不是以data开头{line}")
        if not answer:
            print(f"{get_now()},report_day or report_week 没有获取到数据\n{package_list}")
            msg = package_list
    elif (service == "chat_dai_good" or service == "chat_rag_good" or service == "chat_v2_good" or
          service == "chat_DAI_data" or service == "chat_v2" or service == "chat_rag"):
        package_list = []
        for line in response.iter_lines():
            package_list.append(line)
            if not first_packet_time:
                # 记录第一个数据包返回的时间（精确到毫秒）
                first_packet_time = time.time()
            # 处理响应数据
            if line.startswith(b"data: "):
                json_data = line[6:]
                try:
                    data_dict = json.loads(json_data)
                except json.JSONDecodeError:
                    print("json loads error:->{}".format(line))
                    continue
                try:
                    return_type = data_dict.get("data", {}).get("return_type", "")
                    return_data = data_dict.get("data", {}).get("return_data", {}).get(return_type, {})
                except BaseException:
                    return_type = ""
                    return_data = {}
                    print(f"获取return_type或者return_data报错")
                    print(f"{data_dict}")
                    print(traceback.format_exc())
                if 'insight' == return_type:
                    content = return_data.get("text")
                    #   计算第一个有效数据返回的时间
                    if content and not first_packet_time_2:
                        first_packet_time_2 = time.time()
                    content_length = len(content)
                    total_character_count += content_length
                    answer += content
                elif return_type == "rag":
                    return_data = data_dict.get("data", {}).get(
                        "return_data", {}).get(return_type, {})
                    content = return_data.get("answer_text")
                    if content and not first_packet_time_2:
                        first_packet_time_2 = time.time()
                    # print(content)
                    content_length = len(content)
                    total_character_count += content_length
                    answer += content
                elif return_type == "tips":
                    return_data = data_dict.get("data", {}).get(
                        "return_data", {}).get(return_type, {})
                    content = return_data
                    if content and not first_packet_time_2:
                        first_packet_time_2 = time.time()
                    content_length = len(content)
                    total_character_count += content_length
                    answer += content
                elif return_type == "progress":
                    intent_list.append(data_dict.get("data", {}).get("return_data", {}).get(return_type))
                elif data_dict.get("code") != 0:
                    msg = data_dict.get("msg")

            # else:
            #     print(f"{get_time()}数据不是以data开头{line}")
        if not answer:
            msg = package_list
            if not first_packet_time_2:
                first_packet_time_2 = time.time()
            print(f"{get_now()},{service} 没有获取到数据\n{package_list}")

    elif service == "simulate_chat" or service == "course_qa":
        package_list = []
        for line in response.iter_lines():
            package_list.append(line)
            if not first_packet_time:
                # 记录第一个数据包返回的时间（精确到毫秒）
                first_packet_time = time.time()
            # 处理响应数据
            if line.startswith(b"data: "):
                json_data = line[6:]
                try:
                    data_dict = json.loads(json_data)
                except json.JSONDecodeError:
                    print("json loads error:->{}".format(line))
                    continue
                content = ""
                try:
                    content = data_dict.get("data", {}).get("answer_text", "")
                except BaseException:
                    print(f"{service}:获取data报错")
                    print(f"{data_dict}")
                    print(traceback.format_exc())
                if content and not first_packet_time_2:
                    first_packet_time_2 = time.time()
                    # print(content)
                content_length = len(content)
                total_character_count += content_length
                answer += content
        #     else:
        #         print(f"{get_time()}数据不是以data开头{line}")
        if not answer:
            if not first_packet_time_2:
                first_packet_time_2 = time.time()
            print(f"{get_now()},{service}没有获取到数据\n{package_list}")
            msg = package_list

    # 等待整个请求完全返回
    response.close()
    # 记录请求完成的时间（精确到毫秒）
    request_complete_time = time.time()
    # 转换为易读的时分秒格式（精确到毫秒）并保留毫秒的前3位
    request_send_time_str = datetime.fromtimestamp(request_send_time).strftime(
        '%Y-%m-%d %H:%M:%S.%f')[:23]

    first_packet_time_str = datetime.fromtimestamp(first_packet_time).strftime(
        '%Y-%m-%d %H:%M:%S.%f')[:23]
    request_complete_time_str = datetime.fromtimestamp(
        request_complete_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    if first_packet_time_2:
        first_packet_time_str_2 = datetime.fromtimestamp(
            first_packet_time_2).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
        first_packet_duration_2 = (first_packet_time_2 - request_send_time)
        tokens_time_2 = (request_complete_time - first_packet_time_2)
        performance_metric_2 = (total_character_count) / tokens_time_2
    else:
        performance_metric_2 = 0
        first_packet_duration_2 = 0
        first_packet_time_str_2 = ""

    # 计算并打印首包耗时和总耗时
    first_packet_duration = (first_packet_time - request_send_time)

    total_duration = request_complete_time - request_send_time
    tokens_time = (request_complete_time - first_packet_time)
    # 计算性能指标
    performance_metric = (total_character_count) / tokens_time
    # print("性能指标: {:.3f} 字/秒".format(performance_metric))
    # print("=" * 50)
    result = {
        "请求发送的时间": request_send_time_str,
        "第一个数据包返回的时间": first_packet_time_str,
        "第一个有效数据包返回的时间": first_packet_time_str_2,
        "请求完成的时间": request_complete_time_str,
        "首包耗时(s)": first_packet_duration,
        "有效数据_首包耗时(s)": first_packet_duration_2,
        "总耗时(s)": total_duration,
        # "输入": len(qa_prompt),
        "输出字数": total_character_count,
        "性能指标(字/秒)": performance_metric,
        "有效数字_性能指标(字/秒)": performance_metric_2,
        "answer": answer,
        "query": data.get("query", "") if service != "simulate_chat" else data.get("dialog", ""),
        "request_id": data["request_id"],
        "msg": msg,
        "progress": json.dumps(intent_list, ensure_ascii=False),
        "session_id": data.get("session_id", "")
    }
    return result


def save_print_to_excel(thread, loop, service, path, data, run_number):
    results = []
    output_path = "{T}{sep}{run_number}{sep}{service}".format(
        sep=os.sep,
        T=get_now("day"),
        service=service,
        run_number=run_number
    )
    output_file = f"Thread_{thread}_loop_{loop}_{get_now()}_{service}.xlsx"

    excel_path = f"{output_path}{os.sep}{output_file}"
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    with concurrent.futures.ThreadPoolExecutor(max_workers=thread) as executor:
        futures = []
        index = 0

        for index in range(loop):
            data = None
            if service == "chat_DAI_data":
                chat_data = copy.deepcopy(chat_DAI_data)
                chat_data["query"] = chat_dai[index % len(chat_dai)]
                chat_data["session_id"] = str(uuid.uuid4())
                chat_data["request_id"] = str(uuid.uuid4())
                data = chat_data
            elif service == "chat_rag":
                chat_data = copy.deepcopy(chat_DAI_data)
                chat_data["query"] = chat_rag[index % len(chat_rag)]
                chat_data["session_id"] = str(uuid.uuid4())
                chat_data["request_id"] = str(uuid.uuid4())
                data = chat_data
            elif service == "chat_v2":
                chat_data = copy.deepcopy(chat_DAI_data)
                chat_data["query"] = chat_v2[index % len(chat_v2)]
                chat_data["session_id"] = str(uuid.uuid4())
                chat_data["request_id"] = str(uuid.uuid4())
                data = chat_data
            elif service == "simulate_chat":
                orion_data = simulate_data[index % len(simulate_data)]
                data = copy.deepcopy(orion_data)
                data["request_id"] = str(uuid.uuid4())
            elif service == "course_qa":
                course_data = copy.deepcopy(course_qa)
                course_data["query"] = teach_course[index % len(teach_course)].get("query")
                course_data["context"] = teach_course[index %
                                                      len(teach_course)].get("context")
                course_data["session_id"] = str(uuid.uuid4())
                course_data["request_id"] = str(uuid.uuid4())
                data = course_data
            elif service == "chat_v2_good":
                chat_data_v2 = copy.deepcopy(chat_DAI_data)
                chat_data_v2["query"] = chat_v2_good[index % len(chat_v2_good)]
                chat_data_v2["session_id"] = str(uuid.uuid4())
                chat_data_v2["request_id"] = str(uuid.uuid4())
                data = chat_data_v2
            elif service == "chat_rag_good":
                chat_rag_good_data = copy.deepcopy(chat_DAI_data)
                chat_rag_good_data["query"] = chat_rag_good[index % len(chat_rag_good)]
                chat_rag_good_data["session_id"] = str(uuid.uuid4())
                chat_rag_good_data["request_id"] = str(uuid.uuid4())
                data = chat_rag_good_data
            elif service == "chat_dai_good":
                chat_dai_good_data = copy.deepcopy(chat_DAI_data)
                chat_dai_good_data["query"] = chat_dai_good[index % len(chat_dai_good)]
                chat_dai_good_data["session_id"] = str(uuid.uuid4())
                chat_dai_good_data["request_id"] = str(uuid.uuid4())
                data = chat_dai_good_data
            futures.append(executor.submit(send_stream, service, data, path))
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as exc:
                print(f"Error: {exc}\n{traceback.format_exc()}")
    df = pd.DataFrame(results)
    try:
        # 将字符串时间列转换为 datetime 类型
        start = pd.to_datetime(df["请求发送的时间"].min())
        end = pd.to_datetime(df["请求完成的时间"].max())
        cost = round((end - start).total_seconds(), 1)
        #   获取执行开始时间
        begin_time = df["请求发送的时间"].min()
        #   获取执行结束时间
        end_time = df["请求完成的时间"].max()
        #   计算执行总耗时
        # all_cost=end_time-begin_time
        # print(f"start:{start},end:{end},cost:{cost}\nbegin_time:{begin_time},end_time:{end_time}")
        # 首包耗时
        first_cost = round(df["首包耗时(s)"].mean(), 2)
        #   有效首包耗时
        real_first_cost = round(df["有效数据_首包耗时(s)"].mean(), 2)

        #   有效首包耗时最大值
        real_first_max = round(df["有效数据_首包耗时(s)"].max(), 2)

        #   有效首包耗时最小值
        # real_first_min = round(df["有效数据_首包耗时(s)"].min(), 2)
        #   去除为0的数据
        real_first_min = round(df.loc[df["有效数据_首包耗时(s)"] != 0, "有效数据_首包耗时(s)"].min(), 2)

        #   有效首包耗时中位数
        real_first_median = round(df["有效数据_首包耗时(s)"].median(), 2)

        #   总耗时平均值
        total_cost = round(df["总耗时(s)"].mean(), 2)

        #   总耗时最大值
        total_cost_max = round(df["总耗时(s)"].max(), 2)

        #   总耗时最小值
        total_cost_min = round(df.loc[df["总耗时(s)"] != 0, "总耗时(s)"].min(), 2)

        #   总耗时中位数
        total_cost_median = round(df["总耗时(s)"].median(), 2)
        #   性能指标字/秒
        speed_word = round(df["性能指标(字/秒)"].mean(), 2)
        #   有效数据性能指标字/秒
        real_speed_word = round(df["有效数字_性能指标(字/秒)"].mean(), 2)
        #   首包耗时计算
        orion_first_cost = sorted(df["有效数据_首包耗时(s)"].to_list())
        first_line_90 = int(len(orion_first_cost) * 0.90)
        first_line_95 = int(len(orion_first_cost) * 0.95)
        first_line_99 = int(len(orion_first_cost) * 0.99)

        first_cost_90 = round(orion_first_cost[first_line_90], 2)
        first_cost_99 = round(orion_first_cost[first_line_99], 2)
        first_cost_95 = round(orion_first_cost[first_line_95], 2)
        #   总耗时计算
        orion_total_cost = sorted(df["总耗时(s)"].to_list())
        total_line_90 = int(len(orion_total_cost) * 0.90)
        total_line_95 = int(len(orion_total_cost) * 0.95)
        total_line_99 = int(len(orion_total_cost) * 0.99)
        total_cost_90 = round(orion_total_cost[total_line_90], 2)
        total_cost_99 = round(orion_total_cost[total_line_99], 2)
        total_cost_95 = round(orion_total_cost[total_line_95], 2)
        title_row = {"请求发送的时间": "总耗时(s)",
                     "第一个数据包返回的时间": "开始时间",
                     "第一个有效数据包返回的时间": "结束时间",
                     "请求完成的时间": "首包99 line",
                     "首包耗时(s)": "首包95 line",
                     "有效数据_首包耗时(s)": "首包90 Line",
                     "总耗时(s)": "首包耗时中位数",
                     "输出字数": "首包耗时最大值",
                     "性能指标(字/秒)": "首包耗时最小值",
                     "有效数字_性能指标(字/秒)": "总耗时 99line",
                     "answer": "总耗时 95line",
                     "query": "总耗时 90line",
                     "request_id": "总耗时中位数",
                     "msg": "总耗时最大值",
                     "progress": "总耗时最小值"}

        data_row = {"请求发送的时间": cost,
                    "第一个数据包返回的时间": begin_time,
                    "第一个有效数据包返回的时间": end_time,
                    "请求完成的时间": first_cost_99,
                    "首包耗时(s)": first_cost_95,
                    "有效数据_首包耗时(s)": first_cost_90,
                    "总耗时(s)": real_first_median,
                    "输出字数": real_first_max,
                    "性能指标(字/秒)": real_first_min,
                    "有效数字_性能指标(字/秒)": total_cost_99,
                    "answer": total_cost_95,
                    "query": total_cost_90,
                    "request_id": total_cost_median,
                    "msg": total_cost_max,
                    "progress": total_cost_min}

        average_row_to_all = {
            "接口": path,
            "since": service,
            "并发数": thread,
            "执行次数": loop,
            "总耗时": cost,
            "开始时间": begin_time,
            "结束时间": end_time,
            "首包99line": first_cost_99,
            "首包95line": first_cost_95,
            "首包90Line": first_cost_90,
            "首包中位数": real_first_median,
            "首包最大值": real_first_max,
            "首包最小值": real_first_min,
            "总耗时99line": total_cost_99,
            "总耗时95line": total_cost_95,
            "总耗时90line": total_cost_90,
            "总耗时中位数": total_cost_median,
            "总耗时最大值": total_cost_max,
            "总耗时最小值": total_cost_min,
            "性能指标(字/秒)": speed_word,
            "有效数字_性能指标(字/秒)": real_speed_word
        }
        all_res.append(average_row_to_all)
        df = pd.concat([df, pd.DataFrame([title_row])], ignore_index=True)
        df = pd.concat([df, pd.DataFrame([data_row])], ignore_index=True)
        df.to_excel(excel_path, index=False)
        print(f"save result file to: {output_path}/{output_file}")
        existing_data = pd.read_excel(summary_res)
        new_df = pd.concat([existing_data, pd.DataFrame([average_row_to_all])], ignore_index=True)
        new_df.to_excel(summary_res, index=False)
    except BaseException:
        print(f"计算平均值异常\n{traceback.format_exc()}")
    return excel_path


from hz_single_script import course_qa as course
from hz_single_script import simulate_chat as simulate
from hz_single_script import chat_other as chat_mixed


def run_merge_scene_1(run_number):
    print("执行混合场景1并发")
    futures = []
    exe = get_exe(5)
    futures.append(exe.submit(chat_mixed.run_1, run_number))
    futures.append(exe.submit(course.run_1, run_number))
    futures.append(exe.submit(simulate.run_1, run_number))
    for future in concurrent.futures.as_completed(futures):
        try:
            result = future.result()
            all_file_list.append(result)
        except Exception as exc:
            print(f"Error: {exc}")


def run_merge_scene_5(run_number):
    print("执行混合场景5并发")
    futures = []
    exe = get_exe(5)
    futures.append(exe.submit(chat_mixed.run_5, run_number))
    futures.append(exe.submit(course.run_5, run_number))
    futures.append(exe.submit(simulate.run_5, run_number))

    for future in concurrent.futures.as_completed(futures):
        try:
            result = future.result()
            all_file_list.append(result)
        except Exception as exc:
            print(f"Error: {exc}")


def run_merge_scene_10(run_number):
    print("执行混合场景10并发")
    futures = []
    exe = get_exe(5)
    futures.append(exe.submit(chat_mixed.run_10, run_number))
    futures.append(exe.submit(course.run_10, run_number))
    futures.append(exe.submit(simulate.run_10, run_number))
    for future in concurrent.futures.as_completed(futures):
        try:
            result = future.result()
            all_file_list.append(result)
        except Exception as exc:
            print(f"Error: {exc}")


def run_single_scene(run_number):
    for task in task_list:
        thread = task.get("thread")
        loop = task.get("loop")
        service = task.get("service")
        data = task.get("data")
        path = task.get("path")
        print("thread:{}\nloop:{}".format(thread, loop))
        file_path = save_print_to_excel(thread, loop, service, path, data, run_number)
        all_file_list.append(file_path)
        print("单场景执行结束")


def run_test(run_number):
    print("开始验证单场景")
    for task in task_list_test:
        thread = task.get("thread")
        loop = task.get("loop")
        service = task.get("service")
        data = task.get("data")
        path = task.get("path")
        print("thread:{}\nloop:{}".format(thread, loop))
        file_path = save_print_to_excel(thread, loop, service, path, data, run_number)
        all_file_list.append(file_path)
        print("单场景验证通过")


def run_test_all(run_number):
    run_test(run_number)
    print("验证混合场景各脚本正常")
    futures = []
    exe = get_exe(3)
    futures.append(exe.submit(chat_mixed.run_test, run_number))
    futures.append(exe.submit(course.run_test, run_number))
    futures.append(exe.submit(simulate.run_test, run_number))
    for future in concurrent.futures.as_completed(futures):
        try:
            result = future.result()
            all_file_list.append(result)
        except Exception as exc:
            print(f"Error: {exc}")
    print("混合场景验证通过")


def get_log_info(session_id, env):
    #   根据session_id获取数据库中的数据
    query_sql = "select trace_logs from trace_logs where session_id='{}'".format(session_id)
    sql_dict = {}
    res = DB.query_all(sql=query_sql, env=env)
    if res:
        res_str = res[0].get("trace_logs")
        # start_index = res_str.find("====指标抽取时间====")
        #   抽取时间
        start_len = "====时间抽取结果====".__len__()
        start_index = res_str.find("====时间抽取结果====")
        end_index = res_str.find("====执行指标sql结果====")
        actual = res_str[start_index + start_len:end_index]
        try:
            dict_time = json.loads(actual.replace("'", "\""))
            # print(f"最终获取到的time数据:{dict_time}\n类型:{type(dict_time)}")
        except BaseException:
            dict_time = {"data": actual}
            # print(f"load数据异常->{actual}")

        #   抽取是否走指标
        status_len = "====是否走指标====".__len__()
        status_start_index = res_str.find("====是否走指标====")
        status_end_index = res_str.find("====时间抽取结果====")
        status = res_str[status_start_index + status_len:status_end_index]

        #   指标sql
        goal_sql_str = "====指标sql===="
        sql_len = goal_sql_str.__len__()
        sql_start_index = res_str.find(goal_sql_str)
        sql_end_index = res_str.find("====开始执行指标计算====")
        if sql_end_index != -1:
            goal_sql = res_str[sql_start_index + sql_len:sql_end_index]
            sql_dict["指标sql"] = goal_sql

        #   ====获取指标远期sql_维度====sql
        forward_sql_str = "====获取指标远期sql_维度===="
        sql_len = forward_sql_str.__len__()
        sql_start_index = res_str.find(forward_sql_str)
        sql_end_index = res_str.find("====获取指标远期sql_维度结果====")
        if sql_end_index != -1:
            forward_sql = res_str[sql_start_index + sql_len:sql_end_index]
            sql_dict["指标远期sql_维度"] = forward_sql

        #   ====获取指标远期sql====
        forward_goal_sql_str = "====获取指标远期sql===="
        sql_len = forward_goal_sql_str.__len__()
        sql_start_index = res_str.find(forward_goal_sql_str)
        sql_end_index = res_str.find("====获取指标远期sql结果====")
        if sql_end_index != -1:
            forward_goal_sql = res_str[sql_start_index + sql_len:sql_end_index]
            sql_dict["指标远期sql"] = forward_goal_sql

        #   ====获取指标实时sql====
        real_time_goal_sql_str = "====获取指标实时sql===="
        sql_len = real_time_goal_sql_str.__len__()
        sql_start_index = res_str.find(real_time_goal_sql_str)
        sql_end_index = res_str.find("====获取指标实时sql结果====")
        if sql_end_index != -1:
            real_time_goal_sql = res_str[sql_start_index + sql_len:sql_end_index]
            sql_dict["指标实时sql"] = real_time_goal_sql
        return dict_time, status, sql_dict
    else:
        # print("没有从库里查询到日志数据")
        return {"data": "没有查到log"}, "无", {}


class DB(object):
    db_infos = {
        "dev": {"host": "************", "port": 3306, "user": "decision_ai", "passwd": "Decision_Ai123",
                "database": "decision_ai"},
        "test": {"host": "**************", "port": 32553, "user": "decision_ai", "passwd": "Decision_Ai123",
                 "database": "decision_ai"},
        "online": {"host": "************", "port": 3306, "user": "decision_ai", "passwd": "Decision_Ai123",
                   "database": "decision_ai"}
    }

    @staticmethod
    def get_pool(env):
        db_info = DB.db_infos[env]
        pool = PooledDB(creator=pymysql, mincached=1, maxcached=10,
                        host=db_info['host'], port=int(db_info['port']), user=db_info['user'],
                        passwd=db_info['passwd'],
                        db=db_info['database'], charset="utf8")
        return pool

    @staticmethod
    def query_all(sql, env=None):

        # 查询所有
        pool = DB.get_pool(env)
        con = pool.connection()
        cursors = con.cursor(cursor=pymysql.cursors.DictCursor)
        # log.info("pool is :{0},con is {1}".format(pool, con))

        try:
            lock.acquire()
            cursors.execute(sql)
            result = cursors.fetchall()
            cursors.close()
            lock.release()
            # log.info('query all result: {0}'.format(result))
            con.close()
            return result
        except Exception as e:
            lock.release()
            print('query all is error sql = {0} msg = {1}'.format(sql, e))
            return None


if __name__ == "__main__":
    #   测试压测脚本和服务时，设置action=test,实际压测action=run_load
    action = "test"
    #   压测结束后,获取请求对应的log信息,通过数据库查询,如无法连数据库,设置false
    query_log_info = True
    #   根据环境动态取数据库,同时,只能在dev环境发送消息
    env = "dev"
    run_number = 1
    #   创建出记录汇总结果文件
    all_df = pd.DataFrame(all_res)
    res_path = f"./{get_now('day')}{os.sep}{run_number}"
    if not os.path.exists(res_path):
        os.makedirs(res_path)
    summary_res = f"./{get_now('day')}{os.sep}{run_number}{os.sep}{get_now()}_all.xlsx"
    mixed_path = f"./{get_now('day')}/{run_number}/Mixed_scenes"
    if not os.path.exists(mixed_path):
        os.makedirs(mixed_path)
    other_summary = f"./{get_now('day')}/{run_number}/Mixed_scenes/chat_other_all_res.xlsx"
    simulate_summary = f"./{get_now('day')}/{run_number}/Mixed_scenes/simulate_all_res.xlsx"
    course_summary = f"./{get_now('day')}/{run_number}/Mixed_scenes/course_qa_all_res.xlsx"

    all_df.to_excel(summary_res, index=False)
    all_df.to_excel(other_summary, index=False)
    all_df.to_excel(simulate_summary, index=False)
    all_df.to_excel(course_summary, index=False)

    start_time = datetime.now()
    start_time_str = start_time.strftime("%Y-%m-%d:%H-%M-%S")
    if action == "test":
        run_test_all(run_number)
    else:
        run_single_scene(run_number)
    # all_df = pd.DataFrame(all_res)
    # summary_res=f"{get_now('day')}{os.sep}{run_number}{os.sep}{get_now()}_all.xlsx"
    # all_df.to_excel(summary_res, index=False)
    print("单场景结果已经保存")
    if action != "test":
        run_merge_scene_1(run_number)
        run_merge_scene_5(run_number)
        run_merge_scene_10(run_number)
    print(f"全部任务执行结束")
    # course.save(run_number)
    # simulate.save(run_number)
    # chat_mixed.save(run_number)
    # print("全部保存结果")
    run_number += 1

    end_time = datetime.now()
    end_time_str = end_time.strftime("%Y-%m-%d:%H-%M-%S")
    cost_time = (end_time - start_time)

    print(f"所有数据存储列表:\n{all_file_list}")
    send_url = "https://open.feishu.cn/open-apis/bot/v2/hook/bf9c14c9-5d6c-4094-9547-a957dabbcefb"
    if env == "dev":
        from tool.util import send_card_message

        #   测试群
        load_msg = f"压测结束！\n压测开始时间:{start_time_str}\n压测结束时间:{end_time_str}\n压测总共耗时:{cost_time}\n\n"
        title = "压测结果通知"
        send_card_message(send_url=send_url, load_msg=load_msg, title=title)

    if query_log_info:
        print("query trace")
        for file_path in all_file_list:
            df = pd.read_excel(file_path, engine='openpyxl')
            for index, row in df.iterrows():
                rq = row["session_id"]
                actual_time, status, sql = get_log_info(rq, env)
                df.at[index, "实际时间"] = json.dumps(actual_time, ensure_ascii=False)
                df.at[index, "是否走指标"] = status
                for k in sql:
                    df.at[index, k] = sql.get(k)
            df.to_excel(file_path, index=False)
        print("query trace end!")
        if env == "dev":
            send_card_message(send_url=send_url, load_msg="获取log信息结束！", title="解析log日志")
