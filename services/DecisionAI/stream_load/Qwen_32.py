
# _*_ coding:utf-8 _*_

import os
import sys

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print(BASE_DIR)
sys.path.append(BASE_DIR)
from test_conifg.config import ip, port
from tool.logger import log
import requests
import json
import time
import concurrent.futures
from datetime import datetime
import pandas as pd
from test_conifg.data.prompt import prompt
from test_conifg.path_config import PROMPT_JSON, MODEL_OUTPUT_PATH, SQL_PATH
from tool.util import get_time, get_tqdm, task_time, get_path, send_card_message
from tool.common import get_sql, load_yml, query_condition_joint
from tool.DB import DB
from tool.custom_tokenizer import get_num_tokens

sql_datas = load_yml(SQL_PATH)
batch_insert = get_sql(sql_datas, "batch_insert_model_performance")
qa_prompt="""请对下面的数据描述做出洞察，找出新颖、有趣的观点，每一条洞察文字限制在30字以内，如果数据描述为空，则不输出任何洞察，最多一共三条。\n数据描述：\n[]\n{\n    \"同比\": [\n        {\n            \"metric_name\": \"总营收\",\n            \"time_unit\": \"周\",\n            \"current_period\": \"2023-16\",\n            \"previous_period\": \"2022-16\",\n            \"metric_type\": \"同比\",\n            \"analysis_of_changing_factors\": {\n                \"同比\": {\n                    \"客房营收\": \"9245.0元\",\n                    \"餐饮营收\": \"443.0元\",\n                    \"会员卡收入\": \"607.0元\",\n                    \"其他营收\": \"253.0元\"\n                }\n            }\n        }\n    ]\n}\n[]\n洞察输出样例为：\n1）...\n\n"""
gen_code_prompt="""You are a helpful assistant highly skilled in writing PERFECT code for data analysis and Data Transformation for Visualization. Given some code template, you complete the template to generate a data analysis and data transformation for visualization given the dataset and the goal described.

The dataset summary provide an overview of the dataset, including: 
  - `fields`: A list of dictionaries, each representing a dataset field (column). Include details like 'data_type', 'range', 'standard_deviation', and 'sample_values'.
  - `field_names`: An array of strings listing all field names in the dataset.
The dataset summary is : {'fields': [{'column': 'week_key2', 'properties': {'dtype': 'category', 'samples': ['2024-20', '2024-15', '2023-37'], 'num_unique_values': 60, 'description': '业务周。格式是:yyyy-ww (例如2024-15）,是string类型'}}, {'column': 'roomtype', 'properties': {'dtype': 'category', 'samples': ['高级双床房', '零压-高级大床房', '高级大床房'], 'num_unique_values': 5, 'description': '房型，有五个类别：商务大床房、商务双床房、流金岁月房、普通双床房和普通大床房'}}, {'column': 'crevenueroom', 'properties': {'dtype': 'number', 'std': 16423.56065787939, 'min': 918.0, 'max': 64490.2, 'samples': [9051.0, 14556.0, 25963.5], 'num_unique_values': 300, 'description': '净客房收入。净客房收入简称客房收入，已剥离含早收入和OTA佣金'}}], 'field_names': ['week_key2', 'roomtype', 'crevenueroom']}

goal: 最近8周我们哪个房型最赚钱.

Solve the task carefully by completing ONLY the <imports> AND <stub> section of the template below. DO NOT WRITE ANY CODE TO LOAD THE DATA. The data is already loaded and available in the variable data. DO NOT add any explanation. The code should: 1) Be executable, capable of performing the required analysis and data transformation. 2) Prepare data for visualization without directly plotting graphs. 

Guidelines for Visualization Types:
1) For trend analysis (such as “xxx趋势如何?”或“xxx变化情况？”), prepare data for a line chart.
2) For distribution or proportion analysis (such as “xxx构成如何?”，“xxx由哪些组成？”或“xxx分布如何?”), prepare data for a pie chart.
3) For comparison or causation analysis (such as “xxx相比怎么样?”或“xxx为什么下降/上升？”), prepare data for a bar chart.
4) For all other query types, no chart preparation is needed.

###TEMPLATE CODE START:

import warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)
import pandas as pd
import numpy as np

# Add necessary imports here
<imports>  

def calculate_metric_name_X(data: pd.DataFrame) -> dict:
    metric = {
    "metric_name": "metric_name_X", # name of the metric
    "metric_value": None, # calculated value, may be a int, float, string, array, etc, must add fillna with None
    "description": "" # description of the metric
    }

    <stub> # only modify this section

    metric["metric_value"] = ... # calculated value
    metric["description"] = "This is a description of the metric"

    return metric

#不要修改该函数名
def transform_data(data: pd.DataFrame, metrics: list[dict]):
    plot_data = {
        "data_type": "", # drawing type: one of the following three chart types: bar, line and pie，data_type只能是这三种类型之一
        "title": "", # title of chart
        "value": None # data used to draw the graph，bar图类型返回的value格式如下：{"categories":["category1","category2",...],"series":[{"name":"柱状图的名称","data":[data1, data2, ...]}]},line图类型返回的value格式如下：{"categories":["category1","category2",...],"series":[{"name":"折线图的名称","data":[data1,data2,...]}]},pie图类型返回的value格式如下：[{"name":"name1","value":value1},{"name":"name2","value":value2},...]
    }

    #Data transformation logic here
    <stub> # only modify this section

    plot_data["data_type] = ... # one of bar, line and pie
    plot_data["title"] = ... # title of chart
    plot_data["value"] = ... # Converted data used for drawing
    return plot_data

def calculate(data: pd.DataFrame) -> list[dict]:
    metrics : list[dict] = [] # Key is the data metric name, value is the calculated value

    # Call the calculation function for each metric
    for metric_func in [calculate_metric_name_X, calculate_metric_, ...]:
        metrics.append(metric_func(data))

    return metrics

metrics = calculate(data)
plot_data = transform_data(data)

###TEMPLATE CODE END:

The FINAL COMPLETED CODE BASED ON THE TEMPLATE above is ..."""
#
# thread = 8  # 并发数
# loop = 1  # 循环次数
# task_list = [{"thread": 1, "loop": 5}, {"thread": 5, "loop": 2}, {"thread": 8, "loop": 2}]
# task_list = [{"thread": 1, "loop": 1}]
task_list_7b = [
    {"thread": 40, "loop": 500},
    {"thread": 32, "loop": 2000}, {"thread": 33, "loop": 2000}]
task_list_7b_code = [
     {"thread": 8, "loop": 500}, {"thread": 9, "loop": 500}]
task_list = [{"thread": 1, "loop": 1}]
Conversion_ratio = 1.54  # 1个token=1.54汉字长度
env = "华住DEV"
GPU = "V100*2"
scene_description = "Qwen7B生成code"
model_name = "OrionStarAether2_7B"


#   3090
# model_url = "http://************:8117/v1/chat/completions"
#   V100
model_url="http://*************:8007/v1/chat/completions"

headers = {
    "Content-Type": "application/json",
}

file_path = "{}{}baidu.json".format(PROMPT_JSON, os.sep)  # 设置JSON文件路径（读取json，本地文件路径）

prompt_pre = ""


def send_request(mode_name,qa_prompt=None):

    data = {
        "model": mode_name,
        "stream": True,
        "messages": [{"role": "user", "content": prompt_pre + qa_prompt}],
        "temperature": 0
    }

    # 记录请求发送的时间（精确到毫秒）
    request_send_time = time.time()
    response = requests.post(model_url, headers=headers, json=data, stream=True)

    first_packet_time = None
    total_character_count = 0
    answer = ""
    # 处理响应数据
    error = 0
    error_answer = ""
    for line in response.iter_lines():
        response_line = line.decode()
        # print("返回数据包:", response_line)
        # 只处理第二行，因为第一行是空行
        if not first_packet_time:
            # 记录第一个数据包返回的时间（精确到毫秒）
            first_packet_time = time.time()

        # 处理响应数据
        if not line.startswith(b"data: "):
            error_answer = response_line
        else:
            # if line.startswith(b"data: "):
            json_data = line[6:]
            try:
                data_dict = json.loads(json_data)
            except json.JSONDecodeError:
                data = line.decode("utf-8")
                if data.__contains__("DONE"):
                    continue
                else:
                    log.error("json loads error:->{}".format(line))
                    continue
            if 'choices' in data_dict:
                choices = data_dict['choices']
                for choice in choices:
                    if choice["finish_reason"] == "stop":
                        break
                    if 'delta' in choice and 'content' in choice['delta']:
                        content = choice['delta']['content']
                        content_length = len(content)
                        if content_length==0:
                            print("大模型返回空包")
                        answer += content
                        total_character_count += content_length
                    else:
                        error += 1
                        if error > 1:
                            log.error("prompt : {}".format(qa_prompt))
                            log.error("choices not delta or delta not content:->{}".format(line))
            else:
                log.error("response not choices:->{}".format(line))
    # print(f"大模型返回结果{answer}")
    # 等待整个请求完全返回
    response.close()
    # print(answer)
    # 记录请求完成的时间（精确到毫秒）
    request_complete_time = time.time()

    # 转换为易读的时分秒格式（精确到毫秒）并保留毫秒的前3位
    request_send_time_str = datetime.fromtimestamp(request_send_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    first_packet_time_str = datetime.fromtimestamp(first_packet_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    request_complete_time_str = datetime.fromtimestamp(request_complete_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]

    # 计算并打印首包耗时和总耗时
    first_packet_duration = (first_packet_time - request_send_time) * 1000
    total_duration = request_complete_time - request_send_time
    tokens_time = (request_complete_time - first_packet_time)
    # 计算性能指标（字/s)
    performance_metric = total_character_count / tokens_time  # 先除以1.54换算成token
    performance_metric = round(performance_metric,2)
    # print(performance_metric)
    #   计算输出、输入token数

    output_token = get_num_tokens(provider="orionstar", tokenizer_name="qwen", text=answer)
    input_token = get_num_tokens(provider="orionstar", tokenizer_name="qwen", text=prompt_pre + qa_prompt)
    performance_token = output_token / tokens_time
    performance_token= round(performance_token,2)

    result = {
        "请求发送的时间": request_send_time_str,
        "第一个数据包返回的时间": first_packet_time_str,
        "请求完成的时间": request_complete_time_str,
        "首包耗时(ms)": first_packet_duration,
        "总耗时(s)": total_duration,
        "输入字数": len(prompt_pre + qa_prompt),
        "输出字数": total_character_count,
        "输入token": input_token,
        "输出token": output_token,
        "性能指标(字/s)": performance_metric,
        "推理速度(tokens/s)": performance_token,
        "prompt": qa_prompt,
        "error_answer": error_answer,
        "answer":answer
    }
    return result


def save_print_to_excel(thread, loop,  task_id, task_name,model_name,prompt):
    results = []
    db_result = []
    output_path = get_path(prefix=MODEL_OUTPUT_PATH, dirs=model_name)
    output_file = "Tread_{thread}_loop_{loop}_{T}_prompt_len_{p_len}_orion.xlsx".format(T=get_time(), thread=thread,
                                                                                        loop=loop, p_len=572)
    excel_path = "{output_path}{sep}{output_file}".format(output_path=output_path, sep=os.sep, output_file=output_file)
    with concurrent.futures.ThreadPoolExecutor(max_workers=thread) as executor:
        futures = []
        sample_count = 0
        for i in range(loop):
            # with open(file_path, "r", encoding="utf-8") as file:
            #     lines = file.readlines()
            #     index = 0
            #     for line in lines:
            #         if index > 100: break
            #         index += 1
            #         json_obj = json.loads(line.strip())
                    # qa_prompt = json_obj.get("qa_prompt", "")
                    # print(qa_prompt)
                    futures.append(executor.submit(send_request,model_name,prompt))
                    sample_count += 1
        tqdm_task = get_tqdm(iter_len=len(futures))
        for future in concurrent.futures.as_completed(futures):
            tqdm_task.update(1)
            try:
                result = future.result()
                res = (task_id, task_name + str(sample_count),
                       result.get("请求发送的时间"), result.get("第一个数据包返回的时间"),
                       result.get("请求完成的时间"), result.get("首包耗时(ms)"),
                       result.get("总耗时(s)"), result.get("输入字数"),
                       result.get("输出字数"), result.get("输入token"),
                       result.get("输出token"), result.get("推理速度(tokens/s)"),
                       result.get("性能指标(字/s)"), thread, env, scene_description,
                       GPU, model_name, result.get("error_answer"),result.get("answer")
                       )
                db_result.append(res)
                #   修改为处理一条直接写库
                DB.batch_insert(batch_insert, db_result)
                db_result = []
                results.append(result)
            except Exception as exc:
                print(f"Error: {exc}")
        tqdm_task.close()
    #   批量入库
    # DB.batch_insert(batch_insert, db_result)
    df = pd.DataFrame(results)
    df.to_excel(excel_path, index=False)
    return results


def insert_performance_res(max_id):
    """
    获取最近的性能测试结果,将结果整理写入到性能汇总表
    :param max_id:
    :return:
    """
    insert_summary = get_sql(sql_datas, "batch_insert_performance_summary")
    query_performance_res = get_sql(sql_datas, "query_batch_performance", key_id=max_id)
    #   查询计算出汇总结果
    db_res = DB.query_all(query_performance_res)
    summary_data = [tuple(x.values()) for x in db_res]
    #   写入汇总表
    DB.batch_insert(insert_summary, summary_data)
    return json.dumps(db_res, ensure_ascii=False)


def query_performance(start_task_id: str = "", end_task_id: str = ""):
    """

    :param start_task_id:
    :param end_task_id:
    :return:
    """
    query = get_sql(sql_datas, "query_performance_summary")
    if start_task_id and end_task_id:
        query = query_condition_joint(query, {"task_id": "between {} and {}".format(start_task_id, end_task_id)})
    elif start_task_id:
        query = query_condition_joint(query, {"task_id": ">= {}".format(start_task_id)})
    elif end_task_id:
        query = query_condition_joint(query, {"task_id": "<= {}".format(end_task_id)})

    res = DB.query_all(query)
    return res


def query_error_list(max_id):
    """
    查询没有返回answer的请求信息
    :param max_id:
    :return:
    """
    query = get_sql(sql_datas, "query_error", max_id=max_id)
    db_res = DB.query_all(query)
    return db_res


def get_max_id():
    max_id_sql = get_sql(sql_datas, "query_max_id")
    db_max_id = DB.query_one(max_id_sql)
    max_id = db_max_id.get("id")
    return max_id

def run_task(model_name):
    print(f"开始执行大模型:{model_name}性能测试\n")
    test_task = []
    if model_name=="OrionStarAether2_7B":
        prompt = gen_code_prompt
        model_name="OrionStarAether2_7B"
        test_task = task_list_7b_code
    else:
        prompt=qa_prompt
        model_name="OrionStarMessiah2_7B"
        test_task=task_list_7b
    res = []
    # task_time(d=10, h=0)
    #   作为临时查询条件
    start_task_id = get_time()
    for i in range(1):
        max_id = get_max_id()
        #   作为临时查询条件
        query_condition = get_time()
        log.info("{}->run {} loop".format(get_time(), i))
        for task in test_task:
            log.info("{}->run for task {}".format(get_time(), task.get("thread")))
            thread = task.get("thread")
            loop = task.get("loop")
            ids = 0

            # if ids>=0:break
            ids += 1
            log.info("{}->run for prompt {}".format(get_time(), "生成code"))
            task_id = get_time()
            task_name = "{name}_model_thread_{thread}_samples_".format(name="生成code", thread=thread)

            # log.info("run prompt :->{}".format(prompt_text))
            save_print_to_excel(thread, loop, task_id, task_name,model_name,prompt)

        insert_performance_res(max_id)
        end_task_id = get_time()
        db_res = query_error_list(max_id)
        if db_res:
            res = "loop-{}性能测试脚本执行完成.answer为空{}条".format(i, len(db_res))
        else:
            res = "loop-{}性能测试脚本执行完成全部成功!".format(i)
        task_url = 'https://open.feishu.cn/open-apis/bot/v2/hook/bf9c14c9-5d6c-4094-9547-a957dabbcefb'
        path = "/api/performance?start_task_id={start_task_id}&end_task_id={end_task_id}".format(
            start_task_id=start_task_id, end_task_id=end_task_id)
        url = "http://{ip}:{port}{path}".format(ip=ip, port=port, path=path)
        # send_card_message(title=res, down_url=url, tag="点击查看性能详情")

if __name__ == "__main__":
    end_time=get_time("T+10m")
    now = get_time()
    while now<end_time:
        run_task("OrionStarMessiah2_7B")
        print("OrionStarMessiah2_7B执行结束")
        now=get_time()
    # run_task("OrionStarAether2_7B")
    # print("OrionStarAether2_7B执行结束")
#     res = []
#     # task_time(d=10, h=0)
#     #   作为临时查询条件
#     start_task_id = get_time()
#     for i in range(1):
#         max_id = get_max_id()
#         #   作为临时查询条件
#         query_condition = get_time()
#         log.info("{}->run {} loop".format(get_time(), i))
#         for task in task_list:
#             log.info("{}->run for task {}".format(get_time(), task.get("thread")))
#             thread = task.get("thread")
#             loop = task.get("loop")
#             ids = 0
#
#             # if ids>=0:break
#             ids += 1
#             log.info("{}->run for prompt {}".format(get_time(), "生成code"))
#             task_id = get_time()
#             task_name = "{name}_model_thread_{thread}_samples_".format(name="生成code", thread=thread)
#
#             # log.info("run prompt :->{}".format(prompt_text))
#             save_print_to_excel(thread, loop, task_id, task_name)
#
#         insert_performance_res(max_id)
#         end_task_id = get_time()
#         db_res = query_error_list(max_id)
#         if db_res:
#             res = "loop-{}性能测试脚本执行完成.answer为空{}条".format(i, len(db_res))
#         else:
#             res = "loop-{}性能测试脚本执行完成全部成功!".format(i)
#         task_url = 'https://open.feishu.cn/open-apis/bot/v2/hook/bf9c14c9-5d6c-4094-9547-a957dabbcefb'
#         path = "/api/performance?start_task_id={start_task_id}&end_task_id={end_task_id}".format(
#             start_task_id=start_task_id, end_task_id=end_task_id)
#         url = "http://{ip}:{port}{path}".format(ip=ip, port=port, path=path)
#         # send_card_message(title=res, down_url=url, tag="点击查看性能详情")
