import json
import pandas as pd
from gpt import chat_with_gpt

file = r"F:\code\jytest\services\DecisionAI\evaluation_scripts\autoQA\智能答疑.xlsx"
df = pd.read_excel(file)

# 初始化一个空列表来存储所有生成的问答对
all_qa_pairs = []

# 创建一个空的 DataFrame 来存储生成的问答对
qa_df = pd.DataFrame(columns=["query", "参考答案", "文本序号"])

for idx, data in enumerate(df["文本"]):
    prompt = f"""### 角色能力 ###
    你是一个问答对生成器，你可以对下面给定上下文的主要内容进行概括和提炼，并按照下面给定的生成规则去生成。

    ### 生成规则 ###
    1. 生成10到12组用户可能会问的问题以及对应的答案，要求问题要简洁、真实、口语化。
    2. 避免生成内容相同或相似的问答对，且问答和答案要一定要准确、严谨、口语化。
    3. 确保问答对尽可能覆盖上下文的所有内容。
    4. 确保生成的问答对是基于上下文内容，不要胡编乱造

    ### 上下文 ###
    Content:
    ---------
    {data}
    ----------

    ### 返回格式 ###
    请严格按照下面描述的JSON列表格式进行输出，不需要解释，输出JSON格式如下:
    [
        {{"question": "generated question one",
           "answer": "generated answer one"}},
        {{
    "question": "generated question two",
            "answer": "generated answer two"
        }}
        ...
    ]
    确保输出的格式可以被Python的json.loads方法解析。"""
    # print(prompt)

    try:
        resp = chat_with_gpt(prompt)
        # 去除多余的换行符和空格
        resp = resp.strip()
        # print(resp)
        qa_pairs = json.loads(resp)

        # 将生成的问答对加入到总列表中
        all_qa_pairs.extend(qa_pairs)

        # 将生成的问答对回写到 DataFrame 中
        for qa in qa_pairs:
            qa_df = pd.concat([qa_df, pd.DataFrame([{"query": qa["question"], "参考答案": qa["answer"], "文本序号": f"文本序号{idx + 1}"}])], ignore_index=True)

    except json.JSONDecodeError as e:
        print(f"JSONDecodeError: {e}")
        print(f"Failed to parse response: {resp}")
    except Exception as e:
        print(f"An error occurred: {e}")

# 处理完所有数据后，可以将所有问答对保存到一个文件中，或者根据需要进行其他处理
with open("output_qa_pairs.json", "w", encoding="utf-8") as f:
    json.dump(all_qa_pairs, f, ensure_ascii=False, indent=4)

# 将更新后的 DataFrame 写回到 Excel 文件中
qa_df.to_excel("updated_智能答疑.xlsx", index=False)

print("生成的问答对已保存到 output_qa_pairs.json 文件中，并更新到 updated_智能答疑.xlsx 文件中。")
