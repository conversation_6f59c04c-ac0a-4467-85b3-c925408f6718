# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : gpt.py
<AUTHOR> <EMAIL>
@Time   : 2023-11-11 18:19:56
@Version: v1.0
"""
import time

import openai

openai.api_key = '***************************************************'  # 替换为您的 OpenAI API 密钥


def chat_with_gpt(prompt):
    try:
        time.sleep(2)
        response = openai.ChatCompletion.create(
            model="gpt-4",
            messages=[
                {"role": "user", "content": prompt}
            ],
            # temperature=0.9
        )

        answer = response['choices'][0]['message']['content']
        # print("GPT-4 Answer:-> ".format(answer))
        return answer
    except:
        return ""


if __name__ == '__main__':
    ansuer = chat_with_gpt(112233)
    print(type(ansuer))
    print(ansuer)
