"""
Iterate all topics and all tasks
1. 沟通题
2. 行为判断题
3. 对话结束条件
4. 对话点评
5. 口述题对话点评

Format:
{
    "task_id": str,
    "item_id": str,
    "prompt": str,
    "type": str,
    "gender": str,
    "parameters": dict
}

* type
    options: 情景模拟题-对话, 情景模拟题-终止条件, 情景模拟题-对话得分, 情景模拟题-点评, 口述题-点评

* gender
    options: male, female
    只有“情景模拟题-对话”有gender字段，其他类型的gender字段的值为None
"""
import json

import jsonlines
import pandas as pd


END_PROMPT = """你是一个判断对话是否结束的决策者，必须严格按照**对话结束条件**， 根据**对话**来判断是否要结束对话，命中任一对话结束条件就要结束对话，否则要让对话继续。
## 要求
1. 不要混淆**所需情节**和**对话**，**所需情节**是预期对话的发展方向，**对话**是{examinee_role_name}和{examiner_role_name}实际回答的内容，你必须对比这两个字段来判断是否结束对话

## 所需情节（这是对照组、预期发展，不是现在的对话内容！！！不要用这里的内容判断对话是否结束！！！！）
================
{script}
================

## 对话结束条件
1. **对话**包含了**所需情节**的所有对话内容：{examinee_role_name}的回答中必须按照顺序包含了三大部分内容：和{examinee_role_name}打招呼、处理完了{examinee_role_name}的需求、和{examinee_role_name}礼貌告别，必须都完成才能结束对话
2. **对话**中{examinee_role_name}连续**3次**回答未按照**对话**的发展方向回答或拒绝对话，{examiner_role_name}连续3次提醒{examinee_role_name}按照剧本回答

## 对话
{dialog}

## 结束标识
0: 不结束
1: 结束

## 结果
下面给出你的判断，命中任一对话结束条件就要结束对话，如果对话正常进行，你要保证对话继续。返回的结果是一个Python Dict。 格式如下：
{{  
    'number': int, # 命中的对话结束条件，如果没有命中任何条件返回 0
    'thought': 'xxx',  # 判断的原因，需要引用“对话”的原始内容作为判定依据
	'flag': 'xxx'  # 是否要结束对话。可选值为: 不结束 or 结束
}}
需要确保返回内容能够被Python中的eval成功执行，不要任何的解释。"""


DIALOG_SCORE_PROMPT = """作为酒店管理的前台员工培训教练，你模拟了{examiner_role_name}，并和员工进行了对话，你现在要遍历**行为判断**的每一条，并判断**对话内容**{examinee_role_name}是否有相应具体的行为。

## 约束
1. 你考核的对象是{examinee_role_name}，你要判断**对话内容**中{examinee_role_name}说的话是否存在**{examinee_role_name}的行为判断**相应的考核点
2. 你必须严谨的判断对话内容是否命中一条**行为判断**中的所有考核点，如果一条**行为判断**中的所有考核点都命中，你要输出True，否则输出False
2. 不要使用英文单引号或双引号，使用中文标点符号代替。

## 考核场景
{description}

## {examinee_role_name}的行为判断
{points}

## 对话内容
{dialogue}

返回的结果是一个Python Dict。 格式如下：
[
	{{

		'content': 'xxx',  # 行为判断中的每一条的content
		'hit': bool  # 可选值为: True or False, **对话内容**中{examinee_role_name}是否有相应的行为

	}}, 
	...
]
需要确保返回内容能够被Python中的eval成功执行，不要任何的解释。"""


COMMENT_PROMPT = """作为酒店管理的{examinee_role_name}培训教练，你对员工在以下场景模拟中进行了行为得分点的评估，现在你要用中文给出评语。

## 场景模拟
{description}

## 对话内容
{dialogue}

## 行为得分
{behavior_score}

你的评语是："""


RECITE_PROMPT = """作为酒店管理的培训教练，你要对参与考核的被考核人口述题的内容进行评分。

## 约束
1. 满分为100分

## 题目
{topic}

## 参考答案
{assessment_content}

## 打分标准
{assessment_points}

## 被考核对象的口述内容
{user_question}

返回的结果是一个Python Dict。 格式如下：
{{
    'thought': 'xxx',  # 判断的原因，必须用中文
	'score': xx # int类型，0-100

}}

需要确保返回内容能够被Python中的eval成功执行，不要任何的解释。"""


CHAT_PROMPT = """作为酒店管理的培训教练，你必须根据{examiner_role_name}背景和动机，扮演符合{examiner_role_name}人设的角色，对{examinee_role_name}进行考核。
## 剧本大场景
{topic}

## 示例对话
{script}

## 要求
1. 你的目的是对员工进行考核，不要过多引导员工;必须牢记：你是客人的角色，不要礼貌，要用‘你’代替‘您’
2. 注意**示例对话**只是例子，你必须遵循{examiner_role_name}的人物背景和动机进行对话
3. 如果当前情节无法顺利推进，你必须使用多变的语言提示员工。
4. 不要使用**对话上下文**中扮演{examiner_role_name}相同的回复。
5. 如果对话上下文已经完成**示例对话**的所有剧情，你扮演的角色应该说再见，结束对话，不要进行无关对话。
6. **当前对话内容**中{examinee_role_name}重复进行跟剧本剧情无关的回复或错误回复，你扮演的角色要提醒{examiner_role_name}按照你的动机回答。
7. 如果在对话上下文中{examinee_role_name}说了“稍等”，你要输出“好的”。
8. 你必须只给出符合{examiner_role_name}的人物背景动机和情节发展的回答话语，不要在回答话语前添加任何{examiner_role_name}的心理活动或者是动作。

## {examiner_role_name}人设
### {examiner_role_name}背景
{background}

### {examiner_role_name}动机
{motivation}

## 场景模拟
{description}

你必须只扮演{examiner_role_name}，只给出符合{examiner_role_name}的人物背景动机和情节发展的回答，你的语气不要礼貌，语言要丰富多变，不要加任何的解释和续写对话。
{dialogue}
{examiner_role_name}："""


def generate_prompt_templates():
    """
    Generate prompt templates for all topics
    """
    origin_data = pd.read_excel("AI_TEST_514.xlsx")
    for index, row in origin_data.iterrows():
        task_id = row["task_id"]
        item_id = row["题目编号"]
        topic = row["题目名称"]
        examinee_role_name = row["examinee_role_name"]
        examiner_role_name = row["examiner_role_name"]
        info = row["题目额外信息(info)"]
        if isinstance(info, str):
            info = json.loads(info)

        if row["题目类型"] == "选择题":
            continue

        female_motivation = info.get("female_motivation")
        female_background = info.get("female_background")
        male_motivation = info.get("male_motivation")
        male_background = info.get("male_background")
        responsibility = info.get('responsibility')
        script = info.get('dialogue_example')
        description = info.get('description')
        assessment_content = info.get('assessment_content')
        assessment_points = info.get('assessment_points')

        if row["题目类型"] == "情景模拟题":
            # 情景模拟题-对话
            female_chat_prompt = CHAT_PROMPT.format(
                topic=topic,
                script=script,
                examinee_role_name=examinee_role_name,
                examiner_role_name=examiner_role_name,
                background=female_background,
                motivation=female_motivation,
                description=description,
                dialogue="{dialogue}"
            )
            female_chat_prompt_data = {
                "task_id": task_id,
                "item_id": item_id,
                "prompt": female_chat_prompt,
                "type": "情景模拟题-对话",
                "gender": "female",
                "parameters": {
                    "topic": topic,
                    "script": script,
                    "examinee_role_name": examinee_role_name,
                    "examiner_role_name": examiner_role_name,
                    "background": female_background,
                    "motivation": female_motivation,
                    "description": description,
                }
            }

            print(female_chat_prompt)
            with jsonlines.open("带教一期9套_prompt.jsonl", mode="a") as writer:
                writer.write(female_chat_prompt_data)

            male_chat_prompt = CHAT_PROMPT.format(
                topic=topic,
                script=script,
                examinee_role_name=examinee_role_name,
                examiner_role_name=examiner_role_name,
                background=female_background,
                motivation=female_motivation,
                description=description,
                dialogue="{dialogue}"
            )
            male_chat_prompt_data = {
                "task_id": task_id,
                "item_id": item_id,
                "prompt": male_chat_prompt,
                "type": "情景模拟题-对话",
                "gender": "male",
                "parameters": {
                    "topic": topic,
                    "script": script,
                    "examinee_role_name": examinee_role_name,
                    "examiner_role_name": examiner_role_name,
                    "background": male_background,
                    "motivation": male_motivation,
                    "description": description,
                }
            }
            with jsonlines.open("带教一期9套_prompt.jsonl", mode="a") as writer:
                writer.write(male_chat_prompt_data)

            # 情景模拟题-终止条件
            end_prompt = END_PROMPT.format(
                script=script,
                examinee_role_name=examinee_role_name,
                examiner_role_name=examiner_role_name,
                dialog="{dialogue}"
            )
            end_prompt_data = {
                "task_id": task_id,
                "item_id": item_id,
                "prompt": end_prompt,
                "type": "情景模拟题-终止条件",
                "gender": None,
                "parameters": {
                    "script": script,
                    "examinee_role_name": examinee_role_name,
                    "examiner_role_name": examiner_role_name,
                }
            }
            print(end_prompt)
            with jsonlines.open("带教一期9套_prompt.jsonl", mode="a") as writer:
                writer.write(end_prompt_data)

            # 情景模拟题-对话得分
            dialog_score_prompt = DIALOG_SCORE_PROMPT.format(
                description=description,
                points=assessment_points,
                dialogue="{dialogue}",
                examinee_role_name=examinee_role_name,
                examiner_role_name=examiner_role_name
            )
            dialog_score_prompt_data = {
                "task_id": task_id,
                "item_id": item_id,
                "prompt": dialog_score_prompt,
                "type": "情景模拟题-对话得分",
                "gender": None,
                "parameters": {
                    "description": description,
                    "points": assessment_points,
                    "examinee_role_name": examinee_role_name,
                    "examiner_role_name": examiner_role_name
                }
            }
            print(dialog_score_prompt)
            with jsonlines.open("带教一期9套_prompt.jsonl", mode="a") as writer:
                writer.write(dialog_score_prompt_data)

            # 情景模拟题-点评
            comment_prompt = COMMENT_PROMPT.format(
                description=description,
                behavior_score="{behavior_score}",
                dialogue="{dialogue}",
                examinee_role_name=examinee_role_name
            )
            comment_prompt_data = {
                "task_id": task_id,
                "item_id": item_id,
                "prompt": comment_prompt,
                "type": "情景模拟题-点评",
                "gender": None,
                "parameters": {
                    "description": description,
                    "examinee_role_name": examinee_role_name,
                }
            }
            print(comment_prompt)
            with jsonlines.open("带教一期9套_prompt.jsonl", mode="a") as writer:
                writer.write(comment_prompt_data)
        else:
            # 口述题
            recite_prompt = RECITE_PROMPT.format(
                topic=topic,
                assessment_content=assessment_content,
                assessment_points=assessment_points,
                user_question="{user_question}"
            )
            recite_prompt_data = {
                "task_id": task_id,
                "item_id": item_id,
                "prompt": recite_prompt,
                "type": "口述题-点评",
                "gender": None,
                "parameters": {
                    "topic": topic,
                    "assessment_content": assessment_content,
                    "assessment_points": assessment_points,
                }
            }
            print(recite_prompt)
            with jsonlines.open("带教一期9套_prompt.jsonl", mode="a") as writer:
                writer.write(recite_prompt_data)


if __name__ == "__main__":
    generate_prompt_templates()