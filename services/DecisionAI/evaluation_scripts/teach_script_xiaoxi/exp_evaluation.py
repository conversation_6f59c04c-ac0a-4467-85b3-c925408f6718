import os, sys

BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
print(BASE_DIR)
sys.path.append(BASE_DIR)
import datetime
import json

import requests
import pandas as pd
from tqdm import tqdm
import click

import config
from tool.util import get_time

EVALUATION_PROMPT = """作为酒店管理的前台员工培训教练，你模拟了{examiner_role_name}，并和员工进行了对话，你现在要遍历**行为判断**的每一条，并判断**对话内容**{examinee_role_name}是否有相应具体的行为。

## 约束
1. 你考核的对象是{examinee_role_name}，你要判断**对话内容**中{examinee_role_name}说的话是否存在**{examinee_role_name}的行为判断**相应的考核点
2. 你必须严谨的判断对话内容是否命中一条**行为判断**中的所有考核点，如果一条**行为判断**中的所有考核点都命中，你要输出True，否则输出False
2. 不要使用英文单引号或双引号，使用中文标点符号代替。

## 考核场景
{description}

## {examinee_role_name}的行为判断
{points}

## 对话内容
{dialogue}

返回的结果是一个Python Dict。 格式如下：
[
	{{

		'content': 'xxx',  # 行为判断中的每一条的content
	    'thought': 'xxx',  # 命中或者没有命中的原因
		'hit': bool  # 可选值为: True or False, **对话内容**中{examinee_role_name}是否有相应的行为

	}}, 
	...
]
需要确保返回内容能够被Python中的eval成功执行，不要任何的解释。"""
def get_evaluation_result(examinee_role_name, examiner_role_name, info, background, dialogue, points):
    """
    获取LLM的生成结果
    """
    prompt_text = EVALUATION_PROMPT.format(
        examinee_role_name=examinee_role_name,
        examiner_role_name=examiner_role_name,
        description=info['description'],
        background=background,
        dialogue=dialogue,
        points=points
    )
    print(f"EVALUATION_PROMPT prompt_text: {prompt_text}")

    with requests.Session() as session:
        # 准备请求头，添加必要的'Content-Type'和'Accept'
        headers = {
            'Authorization': 'Bearer ***************************************************',
            'Content-Type': 'application/json',
            # 'Accept': 'text/event-stream',
        }

        messages = []
        messages.append({"role": "user", "content": prompt_text})

        stop = None
        post_data = {
            # "model": "OrionStarMessiah2_7B",
            # "model": "gpt-4o",
            "model": config.Config.model_name,

            "temperature": 0.0,
            "top_p": 1.0,
            "messages": messages,
            "max_tokens": 2000,
            "stop": stop
        }

        # POST数据
        print(f"request_json_rag: {post_data}")

        # 发送POST请求
        # response = session.post("https://api.openai.com/v1/chat/completions", headers=headers, json=post_data)

        response = session.post(config.Config.url, headers=headers, json=post_data)
        print(f"response_json_rag: {response}")
        print(f"response_json_rag headers: {response.headers}")

        result = response.json()
        answer_text = result['choices'][0]['message']['content']

        try:
            evaluation_result = eval(answer_text)  # 得分结果
        except Exception as e:
            print(f"error: {e}")
            return None

        return evaluation_result


def evaluation_experiment(test_case_path, resource_path):
    origin_data = pd.read_excel(resource_path)
    test_case_data = pd.read_excel(test_case_path)

    origin_data_map = {}
    for index, row in origin_data.iterrows():
        task_id = row["task_id"]
        origin_data_map.setdefault(task_id, {})
        item_id = row["题目编号"]
        origin_data_map[task_id].setdefault(item_id, {})
        topic = row["题目名称"]
        examinee_role_name = row["examinee_role_name"]
        examiner_role_name = row["examiner_role_name"]
        info = row["题目额外信息(info)"]
        if isinstance(info, str):
            info = json.loads(info)

        origin_data_map[task_id][item_id] = {
            "topic": topic,
            "examinee_role_name": examinee_role_name,
            "examiner_role_name": examiner_role_name,
            "info": info
        }

    print(f"origin data {origin_data_map}")

    total_pass = 0
    test_results = []
    for index, row in tqdm(test_case_data.iterrows(), total=test_case_data.shape[0]):
        task_id = row["task_id"]
        item_id = row["item_id"]
        print(f"current task_id: {task_id}, item_id: {item_id}")
        dialog = row["对话内容"].strip()
        expected_result = row["预期"]
        classification = row["分类"]

        try:
            expected_result = json.loads(expected_result)
        except Exception as e:
            continue

        examiner_role_name = origin_data_map[task_id][item_id]["examiner_role_name"]
        examinee_role_name = origin_data_map[task_id][item_id]["examinee_role_name"]
        info = origin_data_map[task_id][item_id]["info"]
        background = info.get("female_background")

        points = []
        for point in info['scoring_criteria']:
            points.append({
                "content": point['content']
            })

        # examinee_role_name, examiner_role_name, info, topic, background, motivation, dialogue
        record = {
            "eval_result": "",
            "total_data": {},
            "number": row["number"],
            "score": 0,
            "classification": classification,
            "expected_result": expected_result,
            "dialog": dialog,
            "points": points
        }
        try:
            result = get_evaluation_result(examinee_role_name, examiner_role_name, info, background, dialog, points)
            record["eval_result"] = result
        except Exception as e:
            print(f"error: {e}")
            continue

        # compare two dicts
        total_score = len(expected_result)
        score = 0
        # naive compare, when the key and value are the same, then score + 1
        for i, item in enumerate(expected_result):
            if i >= len(result):
                break
            if item['hit'] == result[i]['hit']:
                score += 1

        record["score"] = score / total_score
        test_results.append(record)
    df = pd.DataFrame(test_results)
    df.to_excel(f"{get_time()}_evaluation.xlsx",index=False)
    with open(f"evaluation_experiment_result_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.json", "w") as f:
        json.dump(test_results, f, ensure_ascii=False, indent=4)

    avg_score = sum([item["score"] for item in test_results]) / len(test_results)
    print(f"Avg score: {avg_score}")
    return avg_score


@click.command()
@click.argument("test_case_path")
@click.option('--resource', default="AI_TEST_514.xlsx", help='题库文件')
def run(test_case_path, resource):
    return evaluation_experiment(test_case_path, resource)


if __name__ == "__main__":
    run()
