import os, sys

BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
print(BASE_DIR)
sys.path.append(BASE_DIR)
import datetime
import json

import requests
import pandas as pd
from tqdm import tqdm
import click
import config
from tool.util import get_time


END_PROMPT = """你是一个对{examinee_role_name}的职责考核的考官，你要判断是否继续通过模拟对话的方式考核考生，你必须严格按照**对话结束条件**， 根据**对话**来判断是否要结束对话，命中其中一个对话结束条件要结束对话，否则必须要让对话继续。
## 要求
1. 不要混淆**所需情节**和**对话**，**所需情节**是预期对话的发展方向，**对话**是{examinee_role_name}和{examiner_role_name}实际回答的内容，你必须对比这两个字段来判断是否结束对话
2. 如果你不确定是否要停止对话，你必须让对话继续，更加全面的考察{examinee_role_name}的表现，不要轻易停止对话

## 所需情节（这是对照组、预期发展，不是现在的对话内容！！！不要用这里的内容判断对话是否结束！！！！）
================
{script}
================

## 对话结束条件
1. **对话**包含了**所需情节**的所有对话内容：{examinee_role_name}中必须按照顺序包含了三大部分内容：和{examinee_role_name}打招呼、处理完了{examinee_role_name}的需求、和{examinee_role_name}礼貌告别，必须都完成才能结束对话
2. **对话**中{examinee_role_name}连续3次回答未按照**对话**的发展方向回答或拒绝对话，{examiner_role_name}连续3次提醒{examinee_role_name}

## 对话
{dialog}

## 结束标识
0: 不结束
1: 结束

## 举例

## 结果
下面给出你的判断，命中其中一个对话结束条件要结束对话，如果对话正常进行，你要保证对话继续。返回的结果是一个Python Dict。 格式如下：
{{  
    'number': int, # 命中的对话结束条件，如果没有命中任何条件返回 0
    'thought': 'xxx',  # 判断的原因，需要引用“对话”的原始内容作为判定依据
	'flag': 'xxx'  # 是否要结束对话。可选值为: 不结束 or 结束
}}
需要确保返回内容能够被Python中的eval成功执行，不要任何的解释。"""

def get_end_result(examinee_role_name, examiner_role_name, info, topic, background, motivation, dialogue):
    """
    获取GPT3的生成结果
    """
    prompt_text = END_PROMPT.format(
        examinee_role_name=examinee_role_name,
        examiner_role_name=examiner_role_name,
        responsibility=info['responsibility'],
        topic=topic,
        description=info['description'],
        background=background,
        motivation=motivation,
        script=info['dialogue_example'],
        dialog=dialogue,
    )
    print(f"conversation_chat_end prompt_text: {prompt_text}")

    with requests.Session() as session:
        # 准备请求头，添加必要的'Content-Type'和'Accept'
        headers = {
            'Authorization': 'Bearer sk-***',
            'Content-Type': 'application/json',
            # 'Accept': 'text/event-stream',
        }

        messages = []
        messages.append({"role": "user", "content": prompt_text})

        stop = None
        post_data = {
            # "model": "OrionStarMessiah2_7B",
            # "model": "gpt-4o",
            "model": config.Config.model_name,

            "temperature": 0.0,
            "top_p": 1.0,
            "messages": messages,
            "max_tokens": 2000,
            "stop": stop
        }

        # POST数据
        print(f"request_json_rag: {post_data}")

        # 发送POST请求
        # response = session.post("https://api.openai.com/v1/chat/completions", headers=headers, json=post_data)

        response = session.post(config.Config.url, headers=headers, json=post_data)
        print(f"response_json_rag: {response}")
        print(f"response_json_rag headers: {response.headers}")

        result = response.json()
        answer_text = result['choices'][0]['message']['content']

        try:
            end_result = eval(answer_text)
            end_result = end_result["flag"]

            if end_result == "结束":
                end_result = 1
            else:
                end_result = 0
        except:
            end_result = 0

        return end_result, answer_text


def end_condition_experiment(test_case_path, resource_path):
    origin_data = pd.read_excel(resource_path)
    test_case_data = pd.read_excel(test_case_path)

    origin_data_map = {}
    for index, row in origin_data.iterrows():
        task_id = row["task_id"]
        origin_data_map.setdefault(task_id, {})
        item_id = row["题目编号"]
        origin_data_map[task_id].setdefault(item_id, {})
        topic = row["题目名称"]
        examinee_role_name = row["examinee_role_name"]
        examiner_role_name = row["examiner_role_name"]
        info = row["题目额外信息(info)"]
        if isinstance(info, str):
            info = json.loads(info)

        origin_data_map[task_id][item_id] = {
            "topic": topic,
            "examinee_role_name": examinee_role_name,
            "examiner_role_name": examiner_role_name,
            "info": info
        }

    print(f"origin data {origin_data_map}")

    total_pass = 0
    test_results = []
    for index, row in tqdm(test_case_data.iterrows(), total=test_case_data.shape[0]):
        task_id = row["task_id"]
        item_id = row["item_id"]
        print(f"current task_id: {task_id}, item_id: {item_id}")
        dialog = row["对话内容"].strip()
        expected_result = row["预期是否结束"]
        classification = row["分类"]

        examiner_role_name = origin_data_map[task_id][item_id]["examiner_role_name"]
        examinee_role_name = origin_data_map[task_id][item_id]["examinee_role_name"]
        info = origin_data_map[task_id][item_id]["info"]
        topic = origin_data_map[task_id][item_id]["topic"]
        motivation = info.get("female_motivation")
        background = info.get("female_background")

        # examinee_role_name, examiner_role_name, info, topic, background, motivation, dialogue
        try:
            record = {
                "end_result": "",
                "total_data": {},
                "number": row["number"],
                "is_pass": False,
                "classification": classification,
                "expected_result": expected_result,
                "dialog": dialog,
                "script": info['dialogue_example']
            }
        except BaseException:
            print(f"record 异常{info}")
        try:
            end_result, total_data = get_end_result(examinee_role_name, examiner_role_name, info, topic, background, motivation, dialog)
            record["end_result"] = end_result
            record["total_data"] = total_data
        except Exception as e:
            end_result = -1
            print(f"error: {e}")

        if expected_result == end_result:
            total_pass += 1
            record["is_pass"] = True

        test_results.append(record)
    df = pd.DataFrame(test_results)
    df.to_excel(f"{get_time()}_end_condition.xlsx",index=False)
    with open(f"end_condition_experiment_result_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.json", "w") as f:
        json.dump(test_results, f, ensure_ascii=False, indent=4)

    print(f"total pass: {total_pass}/{test_case_data.shape[0]}")
    return total_pass/test_case_data.shape[0]


@click.command()
@click.argument("test_case_path")
@click.option('--resource', default="AI_TEST_514.xlsx", help='题库文件')
def run(test_case_path, resource):
    return end_condition_experiment(test_case_path, resource)


if __name__ == "__main__":
    run()
