# 智能带教自动测评脚本

主要包括两个脚本，对应智能带教任务的2和3。

* exp_end_condition.py
  * 评估自动判定对话结束的效果
* exp_evaluation.py
  * 评估自动打分的效果


## Quick Start

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置文件

config.py 配置 model_name 和 url

### 3. 运行脚本

#### exp_end_condition.py
```bash
python exp_end_condition.py --help
```
Usage: exp_end_condition.py [OPTIONS] TEST_CASE_PATH

Options:
  --resource TEXT  题库文件
  --help           Show this message and exit.

```bash
python exp_end_condition.py 带教负向结束条件测试.xlsx
python exp_end_condition.py 带教正向结束条件测试.xlsx
```

#### exp_evaluation.py
```bash
python exp_evaluation.py --help
```
Usage: exp_evaluation.py [OPTIONS] TEST_CASE_PATH

Options:
  --resource TEXT  题库文件
  --help           Show this message and exit.

```bash
python exp_evaluation.py 评分测试用例.xlsx
```



### 4. 测试集说明

* 带教负向结束条件测试.xlsx 11个
  * 用于测试应该结束对话的准确性
* 带教正向结束条件测试.xlsx 43个
  * 用于测试不应该结束对话的效果准确性
* 评分测试用例.xlsx 100个
  * 用于测试打分的效果