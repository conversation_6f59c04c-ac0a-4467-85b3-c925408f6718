# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : gen_report.py
<AUTHOR> <EMAIL>
@Time   : 2024-05-24 15:50:54
@Version: v1.0
"""
import os,sys
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
print(BASE_DIR)
sys.path.append(BASE_DIR)
import json
from decimal import Decimal
import requests,traceback,time
from tool.DB import DB

host = "http://self-hosting.chatmax.net:100/v1/ctai/boss_report_analysis"
headers={"content-Type":"application/json"}
def request_verify(payload):
    try:
        res = requests.request(url=host, method="POST", json=payload, headers=headers).json()
        return res
    except BaseException:
        print(f"{payload}\n")
        print(f"请求异常:{traceback.format_exc()}")
        return "接口调用异常"

def get_dat(date_str):
    from datetime import datetime, timedelta

    # 将日期字符串转换为 datetime 对象
    date_obj = datetime.strptime(date_str, "%Y-%m-%d")

    # 获取前一天的日期
    previous_2day = date_obj - timedelta(days=2)
    previous_day = date_obj - timedelta(days=1)

    # 获取去年今天的日期
    last_year_today = previous_day.replace(year=date_obj.year - 1)

    # 格式化日期
    date_str_formatted = date_obj.strftime("%Y-%m-%d")
    previous_day_formatted = previous_day.strftime("%Y-%m-%d")
    yesterday = previous_2day.strftime("%Y-%m-%d")
    last_year_today_formatted = last_year_today.strftime("%Y-%m-%d")

    print("指定日期:", date_str_formatted)
    print("前一天日期:", previous_day_formatted)
    print("day",previous_day_formatted)
    print("yesterday",yesterday)
    print("去年今天日期:", last_year_today_formatted)
    return previous_day_formatted,yesterday,last_year_today_formatted
def get_previous_week(year, week):
    if week > 1:
        wk = week - 1
        if wk<10:
            wk=f"0{wk}"
        return year, wk
    else:
        return year - 1, 52

def get_last_year_week(year, week):
    if week<10:
        week=f"0{week}"
    return year - 1, week

def get_day_payload(orion_day,hotel_id):
    day ,yesterday,last_year_day= get_dat(orion_day)
    d_first_query = "select * from ads_powerboss_revenue_hotel_d where create_day in ('{}','{}','{}') and hotel_id='{}'".format(day,yesterday,last_year_day,hotel_id)
    d_second_query="select * from ads_powerboss_revenue_hotel_detail_d where create_day in ('{}','{}','{}') and hotel_id='{}'".format(day,yesterday,last_year_day,hotel_id)

    d_first_res = DB.query_all(d_first_query, env="hz")
    d_second_res = DB.query_all(d_second_query, env="hz")
    d_first = json.dumps(d_first_res,default=decimal_handler,ensure_ascii=False)
    d_second = json.dumps(d_second_res,default=decimal_handler,ensure_ascii=False)
    payload = {
        "hotel_id": hotel_id,
        "begin_date": orion_day,
        "end_date": orion_day,
        "week_in_year": "2023-16",
        "context": "",
        "request_id": f"{time.time()}",
        "stream": 0,
        "first_level_data":d_first,
        "second_level_data":d_second,
        "type":2
    }
    return payload

def get_week_payload(year_week_str,hotel_id):

    # 解析年份和周数
    year, week = map(int, year_week_str.split('-'))

    # 获取上一周的周数
    previous_year, previous_week = get_previous_week(year, week)

    # 获取去年同一周的周数
    last_year, last_year_week = get_last_year_week(year, week)
    yes_week=f"{previous_year}-{previous_week}"
    year_week=f"{last_year}-{last_year_week}"
    print("上一周:"+yes_week)
    print("去年同一周:"+year_week )
    w_first_query = "select * from ads_powerboss_revenue_hotel_w where week_key2 in ('{}','{}','{}') and hotel_id='{}'".format(year_week_str,yes_week,year_week,hotel_id)
    w_second_query="select * from ads_powerboss_revenue_hotel_detail_w where week_key2 in ('{}','{}','{}') and hotel_id='{}'".format(year_week_str,yes_week,year_week,hotel_id)


    w_first_res = DB.query_all(w_first_query, env="hz")
    w_second_res = DB.query_all(w_second_query, env="hz")
    w_first=json.dumps(w_first_res,default=decimal_handler,ensure_ascii=False)
    w_second = json.dumps(w_second_res,default=decimal_handler,ensure_ascii=False)

    print(w_first)
    print(w_second)

    payload = {
        "hotel_id": hotel_id,
        "begin_date": "2023-04-23",
        "end_date": "2023-04-29",
        "week_in_year": year_week_str,
        "context": "",
        "request_id": f"{time.time()}",
        "stream": 0,
        "first_level_data":w_first,
        "second_level_data":w_second
    }
    return payload

def decimal_handler(obj):
    if isinstance(obj, Decimal):
        return float(obj)
    else:
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

if __name__ == '__main__':

    payload = get_week_payload("2024-01")

    res =request_verify(payload)
    print(f"生成的周报：{res}")

    # payload = get_day_payload("2023-06-11")
    # print(json.dumps(payload,ensure_ascii=False))
    # res =request_verify(payload)
    # print(f"生成的日报：{res}")
