# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : runCase.py
<AUTHOR> <EMAIL>
@Time   : 2024-04-19 16:18:31
@Version: v1.0
"""
import os, sys

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
print(BASE_DIR)
sys.path.append(BASE_DIR)
import json
import traceback
import requests
import openpyxl
from jsonpath_ng import jsonpath, parse
import time
import pandas as pd
import uuid
from gen_report import *
# import tool.DB
import datetime

"""
效果脚本，批量调用接口实现快速拿到系统结果，对结果进行评测
"""
book = openpyxl.Workbook()
sheet_data = book.active
sheet_data.append(
    ["desc", "query", "info", "hotel_id", "意图识别", "answer", "预期结果", "简报原始数据", "期望时间", "实际时间",
     "时间对比结果", "session_id"])
sheet = []
supperboss_qa = {"user_id": "123456", "session_id": "7bad636e-8f33-4dad-b873-3762b05f01d4", "recommend": 0,
                 "query": "华通账号登录不上怎么办？", "sence": "supperboss_qa",
                 "request_id": "4c7fd7c5-f2f2-4ac5-bb8f-4264c4e86753", "store_id": "9007534",
                 "dataaccess_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoic3lzdGVtIiwiaG90ZWxfaWQiOiI5MDA3NTM0IiwiY3JlYXRlX3RpbWUiOiIyMDI0LTA1LTEzIiwiZXhwIjoxNzUxNjE5NTE1fQ.OfBbSuOBO1wRkkoOQiAA3njX49egLJVz8-DfYinxPfI"}
supperboss_qa_path = "/v1/ctai/ai_chat"


def case(max_row=1):
    """
    执行case，结果文件在当前目录，只需要修改case_path路径
    case url:https://orionstar.feishu.cn/sheets/RduHsoPXAhbGSEtXtUjcak8wnih
    :return:
    """
    case_path = "effect_chat.xlsx"
    # case_path = "effect_test.xlsx"
    # 定义新文件夹的名称
    folder_name = "effect_time"

    # 检查文件夹是否已存在，如果不存在则创建新文件夹
    if not os.path.exists(folder_name):
        os.makedirs(folder_name)
    res_file = f"{folder_name}/{datetime.datetime.now()}effect_result.xlsx"
    index = 1
    host = "http://self-hosting.chatmax.net:100"
    xls = pd.read_excel(case_path, sheet_name="Sheet1", keep_default_na=False)
    print(f"{datetime.datetime.now()}case 开始执行")
    for rows in xls.iterrows():
        if index>max_row:
            break
        headers = {"content-type": "application/json"}
        answer = ""
        orion_data = ""
        business_type = rows[1].get("desc")
        query = rows[1].get("query")
        info = json.loads(rows[1].get("info")) if rows[1].get("info") else rows[1].get("info")
        hotel_id = rows[1].get("hotel_id")
        expect_res = rows[1].get("预期结果")
        expect_time = rows[1].get("期望时间")
        print(f"执行第{index}条，{query}")
        if business_type == "问答":
            url = host + supperboss_qa_path
            supperboss_qa["query"] = query
            supperboss_qa["session_id"] = str(uuid.uuid4())
            supperboss_qa["request_id"] = str(uuid.uuid4())
            payload = supperboss_qa
            try:
                results_list_package = request_stream(url=url, method="POST", payload=payload, headers=headers)
                status_event, answer, intent = check_event("chat", results_list_package)
            except BaseException:
                print(f"获取流式请求接口失败\n{query}\n{traceback.format_exc()}")
                answer = "调用流式请求失败"
                sheet.append(
                    [business_type, query, json.dumps(info, ensure_ascii=False), hotel_id, intent, answer, expect_res,
                     expect_time, '', '不通过', supperboss_qa["session_id"]])
                continue
        elif business_type == "口述题评分":
            payload = {"question": "请简述交接班时的准备工作有哪些？", "answer": query, "info": info,
                       "request_id": str(uuid.uuid4())}

            url = host + "/v1/ctai/teach_qa_evaluate"
            status, results = request_verify(
                url=url,
                method="POST",
                payload=payload,
                headers=headers
            )
            socre = results.get("data").get("score")
            review = results.get("data").get("review")
            answer = f"得分:{socre}\n点评:{review}"
        elif business_type == "日报":
            payload = get_day_payload(query, hotel_id)
            url = host + "/v1/ctai/boss_report_analysis"
            status, results = request_verify(
                url=url,
                method="POST",
                payload=payload,
                headers=headers
            )
            if results.get("outputs"):
                answer = results["outputs"]["answer"]
            else:
                answer = results['msg']
            first_data = json.dumps(payload["first_level_data"], ensure_ascii=False)
            second_data = json.dumps(payload["second_level_data"], ensure_ascii=False)
            orion_data = f"一级数据:{first_data}\n二级数据:{second_data}"
        elif business_type == "周报":
            payload = get_week_payload(query, hotel_id)
            url = host + "/v1/ctai/boss_report_analysis"
            status, results = request_verify(
                url=url,
                method="POST",
                payload=payload,
                headers=headers
            )
            if results.get("outputs"):
                answer = results["outputs"]["answer"]
            else:
                answer = results['msg']
            first_data = json.dumps(payload["first_level_data"], ensure_ascii=False)
            second_data = json.dumps(payload["second_level_data"], ensure_ascii=False)
            orion_data = f"一级数据:{first_data}\n二级数据:{second_data}"
        sheet.append(
            [business_type, query, json.dumps(info, ensure_ascii=False), hotel_id, intent, answer, expect_res,
             orion_data, expect_time, "", "不通过", supperboss_qa["session_id"]])
        index += 1

    print(f"{datetime.datetime.now()}case 执行完成")

    diff_res = diff_expect_time(sheet)
    for row in diff_res:
        sheet_data.append(row)
    book.save(res_file)
    print(f"case结果保存在{res_file}")


def check_event(event_type: str, res: list) -> tuple:
    if event_type == "report":

        note = ""
        answer = ""
        stop_flag = False
        for r in res:
            answer += r.get("outputs", {}).get("answer", "")
            if r.get("status") != 200:
                note += r.get("msg", "")
            if r.get("event") == "stop":
                stop_flag = True

        if not stop_flag:
            note += "event没有stop标记"
        if not note:
            return False, answer, note
        else:
            return True, answer, note

    if event_type == "chat":
        flag = {
            "progress": False,
            "insight": False,
            "recommend": False,
            "end": False,
            "chart": False,
            "rag": False,
            "report": True,
            "reply": True,
            "tips": True
        }
        #   记录问答结果
        answer = ""
        note = ""
        intent = ""
        for r in res:
            try:
                return_type = r.get("data", {}).get("return_type", "")
            except BaseException:
                print(r)
                answer = r.get("msg")
                return False, answer, intent

            #   判断data中return_type指定的数据在return_data中有对应的key数据
            return_data = r.get("data", {}).get("return_data", {}).get(return_type, {})
            is_last_event = r.get("data", {}).get("is_last_event", "")
            if return_type != "end" and return_type in flag and return_data and not is_last_event:
                #   获取洞察信息
                if return_type == "progress":
                    if return_data.get("progress") == "10002":
                        intent = return_data.get("value")
                if return_type == "tips":
                    answer = return_data
                #   校验洞察有数据
                if return_type == "insight":
                    if return_data.get("text"):
                        answer += return_data.get("text")
                    else:
                        note += f"{return_type}洞察数据空\n"
                if return_type == "reply":
                    if return_data.get("text"):
                        answer += return_data.get("text")
                    else:
                        note += f"{return_type}反问内容空\n"
                if return_type == "report":
                    if return_data.get("text"):
                        answer += return_data.get("text")
                    else:
                        note += f"{return_type}report内容空\n"

                #   校验chart success阶段有数据
                if return_type == "chart" and return_data.get("status") == "success":
                    echart_data = return_data.get("echart_data")
                    if echart_data:
                        data_type = echart_data[0].get("data_type")
                        echart_data = echart_data[0].get("data")
                        if data_type == "bar" or data_type == "line":
                            if echart_data:
                                try:
                                    if not echart_data.get("categories") or not echart_data.get("series"):
                                        note += f"{return_type}柱状图or折线图数据错误\n"
                                except BaseException:
                                    print("echart 数据包错误")
                        elif data_type == "pie":
                            if echart_data:
                                for data_dict in echart_data:
                                    if not data_dict.get("value"):
                                        note += f"{return_type}饼图 数据错误\n"

                if return_type == "rag":
                    answer_text = return_data.get("answer_text", "default")
                    if answer_text or answer_text == "default":
                        answer += return_data.get("answer_text")
                    else:
                        note += f"{return_type}获取数据失败\n"
            elif is_last_event:
                flag[return_type] = True
            # else:
            #     note+=f"{return_type} 校验失败->{r}\n"
        if all(value for value in flag.values()):
            return True, answer, intent
        else:
            return False, answer, intent
    #   模拟对话
    if event_type == "simulate_chat":
        note = ""
        answer = ""
        for index, r in enumerate(res):
            answer += r.get("data", {}).get("answer_text", "")
            #   判断最后一个包
            if index == len(res) - 1:
                if "stop" != r.get("data", {}).get("event"):
                    note += "event没有stop，对话结束"
                # if not r.get("data", {}).get("end"):
                #     note += "最后一个包，end不是1"
            # elif r.get("data", {}).get("end", "无") != "无":
            #     note += "非尾包存在end字段"
            if not r.get("code"):
                note += r.get("msg", "")
        if answer:
            return True, answer, note
        else:
            return False, answer, note


def request_verify(url, method, payload, headers):
    try:
        res = requests.request(url=url, method=method, json=payload, headers=headers).json()
        return "success", res
    except BaseException:
        print(f"请求异常:{traceback.format_exc()}")
        return "fail", "接口调用异常"


def my_verify(res, expect_data, v_type):
    if v_type == "eq":
        return res == expect_data
    if v_type == "json_path_eq":
        flag = True
        for expect in expect_data:
            key = expect.get("key")
            value = expect.get("value")
            jsonpath_expr = parse(key)
            matches = [match.value for match in jsonpath_expr.find(res)]
            if not matches or not matches[0] == value:
                return False
        return flag
    if v_type == "json_path_has":
        flag = True
        for expect in expect_data:
            key = expect.get("key")
            jsonpath_expr = parse(key)
            matches = [match.value for match in jsonpath_expr.find(res)]
            if not matches:
                return False
        return flag
    if v_type == "json_path_no":
        flag = True
        for expect in expect_data:
            key = expect.get("key")
            jsonpath_expr = parse(key)
            matches = [match.value for match in jsonpath_expr.find(res)]
            if matches:
                return False
        return flag
    if v_type == "structure":
        if isinstance(res, list):
            for r in res:
                if not my_verify(res=r, expect_data=expect_data, v_type=v_type):
                    return False
            return True
        return compare_dicts_structure(dict1=expect_data, dict2=res)


def compare_dicts_structure(dict1, dict2):
    if len(dict1) != len(dict2):
        return False

    for key in dict1:
        if key not in dict2:
            return False

        value1 = dict1[key]
        value2 = dict2[key]

        if isinstance(value1, dict) and isinstance(value2, dict):
            # 递归比较嵌套字典
            if not compare_dicts_structure(value1, value2):
                return False
        elif type(value1) != type(value2):
            return False

    return True


def request_stream(url, method, payload, headers):
    package_info = []
    response = requests.request(url=url, method=method, json=payload, headers=headers, stream=True)

    if response.headers.get("content-type") == "application/json":
        return [response.json()]
    for line in response.iter_lines():
        response_line = line.decode()
        # 处理响应数据
        if not line.startswith(b"data: "):
            error_answer = response_line
        else:
            json_data = line[6:]
            try:
                data_dict = json.loads(json_data)
                package_info.append(data_dict)
            except json.JSONDecodeError:
                data = line.decode("utf-8")
                if data.__contains__("DONE"):
                    continue
                else:
                    print("json loads error:->{}".format(line))
                    continue
    # 等待整个请求完全返回
    response.close()
    if len(json.dumps(package_info, ensure_ascii=False)) > 32762:
        print(package_info)
    return package_info


def diff_expect_time(date_list):
    ok_number = 0
    print(f"{datetime.datetime.now()}开始执行校验期望时间和预期时间")
    for row in date_list:
        session_id = row[-1]
        actual_time = get_log_time(session_id)
        expect_time = row[-4]
        if isinstance(expect_time, str):
            try:
                expect_time = json.loads(expect_time)
            except BaseException:
                print(f"load 预期时间异常:{expect_time}")
        row[-3] = json.dumps(actual_time, indent=2, ensure_ascii=False)
        if compare_dicts_structure_time(actual_time, expect_time):
            row[-2] = "通过"
            ok_number += 1
    ok_rate = round(ok_number / len(date_list), 2) * 100
    date_list.append(["时间对比成功率:", f"{ok_rate}分"])
    print(f"{datetime.datetime.now()}校验期望时间和时间系统识别时间对比结束\n成功率{ok_rate}%")
    return date_list


def get_log_time(session_id)->dict:
    #   根据session_id获取数据库中的数据
    query_sql = "select trace_logs from trace_logs where session_id='{}'".format(session_id)

    res = DB.query_all(sql=query_sql, env="dev_log")
    if res:
        res_str = res[0].get("trace_logs")
        # start_index = res_str.find("====指标抽取时间====")
        start_len = "新工作流指标抽取结果====".__len__()
        start_index = res_str.find("新工作流指标抽取结果====")
        end_index = res_str.find("====获取指标sql====")
        actual = res_str[start_index + start_len:end_index]

        try:
            dict_time = json.loads(actual.replace("'", "\""))
            print(f"最终获取到的time数据:{dict_time}\n类型:{type(dict_time)}")
        except BaseException:
            dict_time={"data":actual}
            print("load数据异常")
        return dict_time
    else:
        print("没有从库里查询到日志数据")
        return {"data":"没有查到log"}


def compare_dicts_structure_time(res, exp):
    for key in exp:
        if key not in res:
            return False

        value1 = res[key]
        value2 = exp[key]

        if isinstance(value1, dict) and isinstance(value2, dict):
            # 递归比较嵌套字典
            if not compare_dicts_structure(value1, value2):
                return False
        elif type(value1) != type(value2):
            return False

    return True


if __name__ == '__main__':
    max_row=999
    case(max_row=max_row)
