import sys
from chat_teach_test import main
from teach_chat_gpt import get_asw_gpt


def main2(file_case_name):
    # 这里是 main2 方法的实现
    print(f"Executing main2 with {file_case_name}")


if __name__ == '__main__':
    if len(sys.argv) < 3:
        print("请提供表格名称和是否评测参数作为参数")
        sys.exit(1)

    file_case_name = sys.argv[1]
    gpt_flag = sys.argv[2].lower() == 'true'

    path = main(file_case_name)

    if gpt_flag:
        get_asw_gpt(path)
