# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : gpt.py
<AUTHOR> <EMAIL>
@Time   : 2023-11-11 18:19:56
@Version: v1.0
"""
import time

import openai
from openai import OpenAI


# 替换为您的 OpenAI API 密钥


def chat_with_gpt_4(prompt):
    try:
        # time.sleep(2)
        client = OpenAI(api_key='***************************************************')
        response = client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "user", "content": prompt}
            ],
            # temperature=0.9
        )

        answer = response.choices[0].message.content
        return answer
    except:
        return ""


def chat_with_gpt_4o(prompt):
    try:
        # time.sleep(2)
        client = OpenAI(api_key='***************************************************')
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "user", "content": prompt}
            ],
            temperature=0.0
        )

        answer = response.choices[0].message.content
        return answer
    except:
        return ""


if __name__ == '__main__':
    ansuer = chat_with_gpt_4o("112233")
    # print(type(ansuer))
    print(ansuer)
