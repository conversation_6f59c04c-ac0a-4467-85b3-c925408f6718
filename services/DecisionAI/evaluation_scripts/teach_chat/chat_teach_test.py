import os
import re
import json
from datetime import datetime
import requests
import pandas as pd


def api_chat(data, url, headers):
    """
    调用模拟聊天API，返回响应
    """
    response = requests.post(url, headers=headers, json=data, verify=False)
    return response


def chat_evaluate(data, url, headers):
    """
    调用聊天评估API，返回响应
    """
    # print(f"接口入参：{data}")
    response = requests.post(url, headers=headers, json=data, verify=False)
    return response


def process_api_response(task_id, response, examinee_role_name, examiner_role_name):
    """
    处理API响应并返回对话文本
    """
    lines = response.text.strip().split('\n')
    answer_text_combined = ''
    end = '0'

    for line in lines:
        line = line.replace('data: ', '')
        if line.strip():
            try:
                data_dict = json.loads(line)
                answer_text = data_dict["data"]["answer_text"]
                end = str(data_dict['data']['end'])  # 确保 end 是字符串类型
                answer_text_combined += answer_text
            except (json.JSONDecodeError, KeyError) as e:
                print(f"错误: {e}, 行内容: {line}")

    if end == '1':
        # return '********对话已在此处停止********'
        return ''
    else:
        return examiner_role_name + ": " + answer_text_combined
    # if task_id != 'T2-010':
    #     return '客人: ' + answer_text_combined
    # else:
    #     return '来电者: ' + answer_text_combined


def get_config_value(file, parameter):
    """
    打开配置文件并获取指定参数
    """
    with open(file, 'r', encoding='utf-8') as f:
        infos = json.load(f)
        return infos[parameter]


def configuration_files(task_id, item_id):
    path = os.path.dirname(os.getcwd())

    # 构造配置文件的路径
    file_path = os.path.join(path, 'teach_chat', 'configuration', '剧本.xlsx')

    # 读取资源文件
    resource = pd.read_excel(file_path)

    # 打印resource的前几行，检查列名和数据
    # print(resource.head())
    # print(resource["题目额外信息(info)"][0])
    # 筛选数据
    resource_data = resource[(resource['task_id'] == task_id) & (resource['题目编号'] == item_id)]
    # print(resource_data)
    return resource_data


def dictate_raw_json(task_id, item_id):
    # 筛选数据
    resource_data = configuration_files(task_id, item_id)
    question = resource_data['题目名称'].values[0]
    info = json.loads(resource_data['题目额外信息(info)'].values[0])
    # 创建字典数据
    data = {
        "question": question,
        "answer": "",
        "info": info,
        "request_id": "53e676c9-1e0a-49v2-827b-2d5218bd38b6"
    }

    return data


# 获取情景模拟接口需要的raw
def chat_raw_json(task_id, item_id):
    # 筛选数据
    resource_data = configuration_files(task_id, item_id)
    # print(resource_data['题目额外信息(info)'])

    # 打印筛选后的数据
    # print(resource_data)

    # 获取所需列的数据
    info = json.loads(resource_data['题目额外信息(info)'].values[0])
    topic = resource_data['题目名称'].values[0]
    description = resource_data['题目描述(description)'].values[0]
    examinee_role_name = resource_data['examinee_role_name'].values[0]
    examiner_role_name = resource_data['examiner_role_name'].values[0]

    # 创建数据字典
    data = {
        "info": info,
        "topic": topic,
        "description": description,
        "dialog": "前台员工: 1",
        "examiner_role_name": examiner_role_name,
        "examinee_role_name": examinee_role_name,
        "gender": "female",
        "topic_id": "1",
        "request_id": "1049b093-105c-4914-9535-d23f0fb7ae3b"
    }
    # print(type(examinee_role_name))
    return data, examinee_role_name, examiner_role_name


def extract_receptionist_lines(dialogue, examinee_role_name):
    """
    提取对话中“前台员工”说的话
    """
    examinee_role_name = str(examinee_role_name)
    # 使用正则表达式匹配“前台员工”说的话
    lines = re.findall(f'{examinee_role_name}[:：](.*?)(?=\n|$)', dialogue, re.DOTALL)
    # 去除每句话的前后空格
    lines = [examinee_role_name + ": " + line.strip() for line in lines]
    # print(lines)
    if lines is not None:
        for _ in range(15): lines.append('前台员工: 再见')
        return lines
    return lines


# 入参case文件名称，xlsx格式
def main(file_case_name):
    # 获取当前工作目录
    current_dir = os.getcwd()
    # 构造文件路径
    file_path = os.path.join(current_dir, file_case_name)
    # print(file_path)
    if not os.path.exists(file_path):
        print(f"路径下没有测试集 {file_path}")
        return

    df = pd.read_excel(file_path)
    # print(df.keys())
    # print(df.keys())
    script = df['剧本剧情']
    task_id = df['task_id']
    item_id = df['item_id'].fillna(0).astype(int)
    question_type = df['考题类型']
    all_dialogs = []  # 测试剧情
    all_remarks = []  # 点评总结
    resp_data = []

    chat_url = "http://self-hosting.chatmax.net:3001/v1/ctai/teach_simulate_chat"
    evaluate_url = "http://self-hosting.chatmax.net:3001/v1/ctai/teach_chat_evaluate"
    qa_evaluate_url = 'http://self-hosting.chatmax.net:3001/v1/ctai/teach_qa_evaluate'
    headers = {
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Connection": "keep-alive",
        "Content-Type": "application/json;charset=UTF-8",
        "Origin": "http://self-hosting.chatmax.net:3001",
        "Referer": "http://self-hosting.chatmax.net:3001/examination/T2-008/c48877d9-03fa-4fbe-a123-36ff4cbc2c98",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "accept": "text/event-stream"
    }

    for i in range(len(task_id)):
        print("===" * 20)
        print(f"正在处理第 {i + 1} 条数据，共 {len(task_id)} 条")
        print(question_type[i])
        if question_type[i] == '口述题':
            dialog_data = dictate_raw_json(task_id[i], item_id[i])
            # print(dialog_data)
            # 直接调用点评接口
            dialog_data['answer'] = script[i]

            eval_resp = chat_evaluate(dialog_data, qa_evaluate_url, headers)
            eval_resp_dict = json.loads(eval_resp.text)
            print(dialog_data)
            print(eval_resp_dict)
            remark = eval_resp_dict.get('data', '无点评内容')
            resp_data.append(eval_resp_dict)
            all_remarks.append(remark)
            # print(remark)
            all_dialogs.append(script[i])
        elif question_type[i] == '情景模拟题':
            dialog = ''
            dialog_data, examinee_role_name, examiner_role_name = chat_raw_json(task_id[i], item_id[i])
            user_query = extract_receptionist_lines(script[i], examinee_role_name)
            # print(dialog_data)
            for idx, query in enumerate(user_query):
                if dialog == '':
                    dialog += query
                else:
                    dialog += '\n' + query

                dialog_data['dialog'] = dialog  # 修改 dialog_data 而不是 data
                resp = process_api_response(task_id[i], api_chat(dialog_data, chat_url, headers), examinee_role_name,
                                            examiner_role_name)
                # print(resp)
                dialog += '\n' + resp

                if idx + 1 >= 16 or resp == '':
                    break

            eval_resp = chat_evaluate(dialog_data, evaluate_url, headers)
            eval_resp_dict = json.loads(eval_resp.text)
            remark = eval_resp_dict.get('data', '无点评内容')
            del remark["behavior_score"]
            print(dialog)
            print(dialog_data)
            print(remark)
            resp_data.append(eval_resp_dict)
            all_remarks.append(remark)
            all_dialogs.append(dialog)
        else:
            print("未知题型")
            resp_data.append("")
            all_remarks.append("")
            all_dialogs.append("")
    # 确保长度一致
    while len(all_dialogs) < len(df):
        all_dialogs.append('')
    while len(all_remarks) < len(df):
        all_remarks.append('')

    # 将测试剧情和点评内容添加到数据框中
    df['测试剧情'] = all_dialogs
    df['点评内容'] = all_remarks
    df["接口返回"] = resp_data
    now = datetime.now()
    output_dir = os.path.join(current_dir, now.strftime('%m-%d'))
    os.makedirs(output_dir, exist_ok=True)  # 确保目录存在
    output_file_path = os.path.join(output_dir, f'测试集_结果{now.strftime("%H_%M")}.xlsx')
    df.to_excel(output_file_path, index=False)
    print(f"结果已保存到 '{output_file_path}'")
    return output_file_path


if __name__ == '__main__':
    main("智能带教评测_1121_7b(V20_V2)_-huazhu-app-api-150_一期_情景模拟&口述_猎户.xlsx")
    main("智能带教评测_1121_7b(V20_V2)_-huazhu-app-api-150_三期_情景模拟_猎户.xlsx")
    main("智能带教评测_1121_7b(V20_V2)_-huazhu-app-api-150_华住线上情景模拟.xlsx")
    main('智能带教评测_1121_7b(V20_V2)_-huazhu-app-api-150_二期_情景模拟_猎户.xlsx')
    main("智能带教评测_1121_7b(V20_V2)_-huazhu-app-api-150_二期口述题_华住.xlsx")
    main("智能带教评测_1121_7b(V20_V2)_-huazhu-app-api-150_二期口述题_猎户.xlsx")
