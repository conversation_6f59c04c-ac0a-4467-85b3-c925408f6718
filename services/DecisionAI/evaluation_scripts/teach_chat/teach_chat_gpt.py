import json
import os
from datetime import datetime

import pandas as pd
from chat_teach_test import configuration_files
from gpt import chat_with_gpt_4o


# 读取结果表，需要入参结果表绝对路径
def df_excel(file):
    df = pd.read_excel(file)
    df = df.dropna(subset=['task_id'])
    # df['item_id'] = df['item_id'].fillna(0).astype(int)
    # print(df["task_id"])# 处理缺失值
    return df


def parse_gpt_response(response):
    try:
        data = json.loads(response)
        if 'des' in data and 'desc' in data['des'] and 'score' in data['des']:
            return data['des']['desc'], data['des']['score']
        else:
            # print(f"Unexpected JSON format: {data}")
            return None, None
    except json.JSONDecodeError as e:
        # print(f"JSONDecodeError: {e}")
        return None, None


def get_gpt_response_with_retries(prompt, retries=3):
    for attempt in range(retries):
        response = chat_with_gpt_4o(prompt)
        # print(response)
        response = response[7:-3]
        print(response)
        desc, score = parse_gpt_response(response)
        if desc is not None and score is not None:
            return desc, score
        # print(f"Retry {attempt + 1}/{retries} failed.")

    return "数据异常", "数据异常"


def get_asw_gpt(file_path):
    df = df_excel(file_path)
    chat_desc = []  # 对话内容
    chat_score = []  # 对话评分
    review_desc = []  # 点评内容
    review_score = []  # 点评分数
    for i in range(len(df)):
        print(f'正在处理第{i + 1}条数据，共{len(df)}条')
        df_data = df.iloc[i]
        # print(df_data)
        # print(df_data['task_id'], df_data['item_id'])
        data = configuration_files(df_data['task_id'], df_data['item_id'])
        # print(data["题目额外信息(info)"][0])
        description = data["题目描述(description)"]
        data = json.loads(data["题目额外信息(info)"].iloc[0])
        print(data)

        # print(data)
        if df_data["考题类型"] == '情景模拟题':
            # 点评prompt
            prompt_review = f""" 你是一个点评系统的评估专家。你的任务是根据以下评测标准评估系统对前台员工模拟对话的点评内容{{review}}和评分{{score}}是否准确，并最终给出你对系统点评的评测分数。
**步骤**
1.判断回答内容：首先，检查对话中前台员工的回答是否满足考核要点中的具体考核点（content）
2.比较评分差距：接着，对比你基于考核要点给出的分数与系统给出的分数，按照评测标准中的“评分是否符合考核标准”进行评测。
3.评估点评内容：根据评测标准中的“点评内容是否全面”对系统点评（review）的内容进行评测。根据评测标准中的“点评内容是否正确”对系统点评（review）的内容进行评测。




**输出数据**

输出数据为一个json格式的字符串,包含评测标准评测说明和评测分数,要求如下：
1. 评测标准的评测说明需要给出评测标准得分项的得分或者扣分原因，并且评测说明必须是desc字段的参数值
2. 评测说明必须在json格式里面输出


**输出示例**
```
{{
    "des":{{
                "desc": ["实际评分100分，基于考核要点的评分应为100分，差距为0分，得4分", "点评项全部覆盖，得3分", "点评项全部正确，得3分"],
                "score": 10
            }}
}}
```

**注意**
1. **考核要点**中content为一条考核点, 实际对话内容符合该条考核点时，即可得到该项对应的分值，否则该项得分为0
2. 如果一条考核点包含多项考核点，需要所有项都满足才算符合
3. 问候语为“你好”即可判定为“使用亲切的礼貌的问候语”
4. 你只能并且必须按照**输出示例**输出，不要输出其他任何其他内容
5. 你只能并且必须按照**输出示例**输出，不要输出其他任何其他内容
6. 你只能并且必须按照**输出示例**输出，不要输出其他任何其他内容

**评测标准：**
1. 评分是否符合考核标准，满分4分
 1.1 基于考核要点评测差距在0-10分，得4分
 1.2 基于考核要点评测差距在11-20分，得3分
 1.3 基于考核要点评测差距在21-30分，得2分
 1.4 基于考核要点评测差距在31-40分，得1分
 1.5 基于考核要点评测差距在40及以上，得0分
 1.6 评分超过100分或者负分，得0分
 1.7 评分失败，得0分
2. 点评内容是否覆盖到所有考核要点，满分3分
 2.1 点评内容全部覆盖，得3分
 2.2 点评项大部分覆盖，得2分
 2.3 点评项大部分未覆盖，得1分
 2.4 点评项全部未覆盖，得0分
3. 点评内容是否正确，满分3分
 3.1 点评项全部正确，得3分
 3.2 点评项大部分准确，得2分
 3.3 点评项大部分错误，得1分
 3.4 点评项全部错误，得0分

**考核要点：**
{data["scoring_criteria"]}


**系统点评：**

 {df_data['点评内容']}


 **对话内容：**

```
{df_data['测试剧情']}

```

**请根据以上信息评估系统点评的准确性和全面性，严格按照输出格式输出，确保输出的格式可以被Python的json.loads方法解析**
"""
            # 对话点评prompt
            prompt_chat = f"""
你是判断对话评分的对话助手，根据下面的{{剧情}}和{{评测标准}}对客人的对话内容进行评分。

### 评测要求
1.只针对客人的回复内容根据评测标准进行评测
2.不要针对前台员工的回复内容进行评测
3.评测的对话内容一定要基于{{剧情}}进行
4.你只需要输出评分和评分的理由即可，不需要输出其他无关的内容

### 输出数据

输出数据为一个json格式的字符串,包含评测标准评测说明和评测分数,要求如下：
1. 评测标准的评测说明需要给出评测标准得分项的得分或者扣分原因
2. 评测标准的评测说明必须是输出在json格式里面的
3. 输出的内容必须是json格式


### 输出格式
```
{{
    "des":{{
            "desc":["对话内容全部符合剧情，得2分","前两轮之后出现不符合角色，得1分","对话结束不符合结束意图，得0分","对话出现非预期的反复重复，得0分","对话语气不卑不亢，得1分"],
        "score":4，
    }}
}}
```


### 剧情

{data['description']}

### 角色

1. 客人
2. 前台员工

### 评测标准

1. 客人的对话内容是否符合剧情,满分2分
   - 1.1 对话内容全部符合剧情，得2分
   - 1.2 非首轮出现不符合剧情，得1分
   - 1.3 首轮出现不符合剧情，得0分
2. 客人的对话内容是否符合客人这一角色，满分2分
   - 2.1 对话内容全部符合角色，得2分
   - 2.2 前两轮之后出现不符合角色，得1分
   - 2.3 前两轮出现不符合角色，得0分
3. 对于前台员工的有效回应，客人的每一次回答是否跟前台员工的回答保持连贯，满分2分
   - 3.1 对话内容全部衔接，得2分
   - 3.2 对话内容部分不衔接，得1分
   - 3.3 对话内容全部不衔接，得0分
4. 对话结束是否符合结束意图，满分2分
   - 4.1 如果前台员工连续三次未给出有效回应，在第三次未给出有效回应后客人不应该再有回复对话应该立即结束，属于符合结束意图，得2分
   - 4.2 如果前台员工连续三次未给出有效回应，在第三次未给出有效回应后客人仍然有回复对话未立即结束，属于不符合结束意图，得0分
   - 4.3 如果对话达到了10次，第11次时对话自动结束，属于符合结束意图，得2分
5. 对话是否出现非预期的反复重复，满分1分
   - 5.1 如果前台员工的对话内容脱离了剧情或不符合前台员工的角色或对客人的诉求没有有效回应，客人反复询问或者表明自己的诉求或者重复某一句话，不属于非预期的反复重复，得1分
   - 5.2 如果客人没有有效回应前台员工且出现2次及以上在重复相同的一句话，属于非预期的反复重复，得0分
6. 对话的语气是否极端，满分1分
   - 6.1 不卑不亢，得1分
   - 6.2 极度客气或者极度生硬，得0分



### 对话

```
{df_data['测试剧情']}

```

**请根据以上信息，严格按照输出格式输出，确保输出的格式可以被Python的json.loads方法解析**
"""

            # 处理点评prompt
            review_desc_val, review_score_val = get_gpt_response_with_retries(prompt_review)
            review_desc.append(review_desc_val)
            review_score.append(review_score_val)

            # 处理对话点评prompt
            chat_desc_val, chat_score_val = get_gpt_response_with_retries(prompt_chat)
            chat_desc.append(chat_desc_val)
            chat_score.append(chat_score_val)
        else:
            # 口述prompt
            prompt_dictate = f"""
你是一个回复效果评测专家。你的任务是根据以下评测标准对答题者的实际回答进行自动化评分。每个问题都有一个标准答案和相应的评分标准。请你按照评分标准对答题者的实际回答进行评分。


**步骤**
1.判断回答内容：首先，按照考核要点中的考核点参考标准答案对答题者的实际回答进行打分。
2.比较评分差距：接着，对比你基于考核要点给出的分数与系统点评给出的分数（score），按照评测标准中的“评分是否符合考核标准”进行评测。
3.评估点评内容：根据评测标准中的“点评内容是否全面”对系统点评（review）的内容进行评测。根据评测标准中的“点评内容是否正确”对系统点评（review）的内容进行评测。


**输出数据**

输出数据为一个json格式的字符串,包含评测标准评测说明和评测分数,要求如下：
1. 评测标准的评测说明需要给出评测标准得分项的得分或者扣分原因
2. 评测标准的评测说明必须是输出在json格式里面的
3. 输出的内容必须是json格式


**输出格式**
```
{{
    "des":{{
            "desc":["实际评分100分，基于考核要点的评分应为100分，差距为0分，得4分","点评项全部覆盖，得3分","点评项全部正确，得3分"],
        "score":10
    }}
}}
```

**注意**
1. 你只能并且必须按照**输出示例**输出，不要输出任何其他内容
2. 你只能并且必须按照**输出示例**输出，不要输出任何其他内容
3. 你只能并且必须按照**输出示例**输出，不要输出任何其他内容

**评测标准：**
1. 评分是否符合考核标准，满分4分
 1.1 基于考核要点评测差距在0-10分，得4分
 1.2 基于考核要点评测差距在11-20分，得3分
 1.3 基于考核要点评测差距在21-30分，得2分
 1.4 基于考核要点评测差距在31-40分，得1分
 1.5 基于考核要点评测差距在40及以上，得0分
 1.6 评分超过100分，得0分
 1.7 评分失败，得0分
2. 点评内容是否全面，满分3分
 2.1 点评项全部覆盖，得3分
 2.2 点评项大部分覆盖，得2分
 2.3 点评项大部分未覆盖，得1分
 2.4 点评项全部未覆盖，得0分
3. 点评内容是否正确，满分3分
 3.1 点评项全部正确，得3分
 3.2 点评项大部分准确，得2分
 3.3 点评项大部分错误，得1分
 3.4 点评项全部错误，得0分


 **考核要点：**
[
  {{
    "content": "回答的内容完整无遗漏",
    "score": 40,
  }},
  {{
    "content": "回答的信息准确无错误",
    "score": 40,
  }},
  {{
    "content": "表达保持条理清晰，逻辑严密，讲述流利",
    "score": 20,
  }}
]


**问题和标准答案**
{description}

{data['assessment_content']}

**实际回答**
{df_data['测试剧情']}


**系统点评：**
{df_data['点评内容']}

**请根据以上信息评估系统点评的准确性和全面性，严格按照输出格式输出，确保输出的格式可以被Python的json.loads方法解析**
"""

            # 处理口述点评prompt
            dictate_desc_val, dictate_score_val = get_gpt_response_with_retries(prompt_dictate)
            review_desc.append(dictate_desc_val)
            review_score.append(dictate_score_val)

            # 对话评分和评分内容填入None
            chat_desc.append(None)
            chat_score.append(None)

    df['对话评分'] = chat_score
    df['对话评分说明'] = chat_desc
    df['点评评分'] = review_score
    df['点评评分说明'] = review_desc
    try:
        # 获取当前时间
        now = datetime.now()

        # 获取当前工作目录的上一级目录
        current_dir = os.path.dirname(os.getcwd())

        # 创建输出目录
        output_dir = os.path.join(current_dir, 'teach_chat', now.strftime('%m-%d'))
        os.makedirs(output_dir, exist_ok=True)

        # 创建输出文件路径
        output_file_path = os.path.join(output_dir, f'测试集_评测结果表{now.strftime("%H_%M")}.xlsx')

        # 保存 DataFrame 到 Excel 文件
        df.to_excel(output_file_path, index=True)

        # 打印结果保存路径
        print(f"结果已保存到 '{output_file_path}'")
    except Exception as e:
        print(f"操作失败: {e}")
    # except:
    #     print(f'结果保存到{file_path}失败')
    #     path = os.path.dirname(os.getcwd())
    #     # 构造配置文件的路径
    #     file_path = os.path.join(path, 'teach_chat', '临时结果表.xlsx')
    #     df.to_excel(file_path, index=False)
    #     print(f'结果已保存至{file_path}临时表内')


if __name__ == '__main__':
    # file_path = r"F:\code\jytest\services\DecisionAI\evaluation_scripts\teach_chat\09-18\测试集_结果21_33.xlsx"
    # file_path=r"F:\code\jytest\services\DecisionAI\evaluation_scripts\teach_chat\华住二期_智能带教_测试集.xlsx"
    get_asw_gpt(file_path=r"F:\code\jytest\services\DecisionAI\evaluation_scripts\teach_chat\10-22\测试集_结果13_11.xlsx")
