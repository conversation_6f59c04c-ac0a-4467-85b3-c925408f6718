import requests

# 替换为你的API密钥
api_key = "u-cBDydaKltdXHQA4DyyKXrrgg2u6M4gv3ogG000C8wee4"

# 替换为你的多维表格ID
spreadsheet_id = "tblL0JZFVluS4YsC"

# API端点
url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{spreadsheet_id}/values_batch_update"

# 请求头
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json; charset=utf-8"
}

# 请求体
data = {
    "value_ranges": [
        {
            "range": "原始数据!A1:B2",  # 替换为你想更新的范围
            "values": [
                ["Value1", "Value2"],
                ["Value3", "Value4"]
            ]
        }
    ]
}

# 发送请求
response = requests.post(url, headers=headers, json=data)

# 打印响应
print(response.json())
