# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : runCase.py
<AUTHOR> <EMAIL>
@Time   : 2024-04-19 16:18:31
@Version: v1.0
"""
import argparse
import concurrent.futures
import os, sys

BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
print(BASE_DIR)
sys.path.append(BASE_DIR)
import json
import traceback
import requests
import openpyxl
import time
import uuid
import datetime
import pandas as pd
import copy

"""
此脚本多线程执行，支持指定文件和执行行数:
1. 推荐问题
2. 50条10分的query
"""
supperboss_qa = {"user_id": "123456", "session_id": "7bad636e-8f33-4dad-b873-3762b05f01d4", "recommend": 0,
                 "query": "华通账号登录不上怎么办？", "sence": "supperboss_qa",
                 "request_id": "4c7fd7c5-f2f2-4ac5-bb8f-4264c4e86753", "store_id": "9007534",
                 "dataaccess_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoic3lzdGVtIiwiaG90ZWxfaWQiOiI5MDA3NTM0IiwiY3JlYXRlX3RpbWUiOiIyMDI0LTA1LTEzIiwiZXhwIjoxNzUxNjE5NTE1fQ.OfBbSuOBO1wRkkoOQiAA3njX49egLJVz8-DfYinxPfI"}
supperboss_qa_path = "/v1/ctai/ai_chat"


def case(rows, index):
    """
    执行case，结果文件在当前目录，只需要修改case_path路径
    case url:https://orionstar.feishu.cn/sheets/RduHsoPXAhbGSEtXtUjcak8wnih
    :return:
    """
    host = "http://self-hosting.chatmax.net:100"
    query = rows[1].get("query")
    business_type = rows[1].get("type")
    expect_res = rows[1].get("exp")
    print(f"执行第{index}条:->{query}")
    headers = {"content-type": "application/json"}
    url = host + supperboss_qa_path
    payload = copy.deepcopy(supperboss_qa)
    payload["query"] = query
    payload["session_id"] = str(uuid.uuid4())
    payload["request_id"] = str(uuid.uuid4())
    results_list_package = []
    try:
        results_list_package = request_stream(url=url, method="POST", payload=payload, headers=headers)
        status_event, answer, intent, echart_data = check_event("chat", results_list_package)
    except BaseException:
        print(f"获取流式请求接口失败\n{query}\n{traceback.format_exc()}")
        print(f"返回的所有包:{results_list_package}")
        answer = "执行脚本,调用流式请求失败"
        return {
            "index": index,
            "type": business_type,
            "query": query,
            "expect_result": expect_res,
            "actual_result": answer,
            "intent": "",
            "echart_data": [],
            "session_id": payload["session_id"],
            "status(脚本辅助校验)": "scucess" if expect_res == answer else "fail"
        }
    return {
        "index": index,
        "type": business_type,
        "query": query,
        "expect_result": expect_res,
        "actual_result": answer,
        "intent": intent,
        "echart_data": echart_data,
        "session_id": payload["session_id"],
        "status(脚本辅助校验)": "scucess" if expect_res == answer else "fail"
    }


def check_event(event_type: str, res: list) -> tuple:
    if event_type == "chat":
        flag = {
            "progress": False,
            "insight": False,
            "recommend": False,
            "end": False,
            "chart": False,
            "rag": False,
            "report": True,
            "reply": True,
            "tips": True
        }
        #   记录问答结果
        answer = ""
        note = ""
        intent = []
        echart = []
        for r in res:
            try:
                return_type = r.get("data", {}).get("return_type", "")
            except BaseException:
                print(f"获取data数据异常{r}")
                answer = r.get("msg")
                return False, answer, json.dumps(intent, ensure_ascii=False), json.dumps(echart, ensure_ascii=False)

            #   判断data中return_type指定的数据在return_data中有对应的key数据
            return_data = r.get("data", {}).get("return_data", {}).get(return_type, {})
            is_last_event = r.get("data", {}).get("is_last_event", "")
            if return_type != "end" and return_type in flag and return_data and not is_last_event:
                #   获取洞察信息
                if return_type == "progress":
                    intent.append(return_data)
                if return_type == "tips":
                    answer = return_data
                #   校验洞察有数据
                if return_type == "insight":
                    if return_data.get("text"):
                        answer += return_data.get("text")
                    else:
                        note += f"{return_type}洞察数据空\n"
                if return_type == "reply":
                    if return_data.get("text"):
                        answer += return_data.get("text")
                    else:
                        note += f"{return_type}反问内容空\n"
                if return_type == "report":
                    if return_data.get("text"):
                        answer += return_data.get("text")
                    else:
                        note += f"{return_type}report内容空\n"

                #   校验chart success阶段有数据
                if return_type == "chart" and return_data.get("status") == "success":
                    echart_data_orion = return_data.get("echart_data")
                    if echart_data_orion:
                        echart = echart_data_orion
                        data_type = echart_data_orion[0].get("data_type")
                        echart_data = echart_data_orion[0].get("data")
                        if data_type == "bar" or data_type == "line":
                            if echart_data:
                                try:
                                    if not echart_data.get("categories") or not echart_data.get("series"):
                                        note += f"{return_type}柱状图or折线图数据错误\n"
                                except BaseException:
                                    print("echart 数据包错误")
                        elif data_type == "pie":
                            if echart_data:
                                for data_dict in echart_data:
                                    if not data_dict.get("value"):
                                        note += f"{return_type}饼图 数据错误\n"
                if return_type == "rag":
                    answer_text = return_data.get("answer_text", "default")
                    if answer_text or answer_text == "default":
                        answer += return_data.get("answer_text")
                    else:
                        note += f"{return_type}获取数据失败\n"
            elif is_last_event:
                flag[return_type] = True
        if all(value for value in flag.values()):
            return True, answer, json.dumps(intent, ensure_ascii=False), json.dumps(echart, ensure_ascii=False)
        else:
            return False, answer, json.dumps(intent, ensure_ascii=False), json.dumps(echart, ensure_ascii=False)


def request_stream(url, method, payload, headers):
    package_info = []
    response = requests.request(url=url, method=method, json=payload, headers=headers, stream=True)

    if response.headers.get("content-type") == "application/json":
        return [response.json()]
    for line in response.iter_lines():
        response_line = line.decode()
        # 处理响应数据
        if not line.startswith(b"data: "):
            error_answer = response_line
        else:
            json_data = line[6:]
            try:
                data_dict = json.loads(json_data)
                package_info.append(data_dict)
            except json.JSONDecodeError:
                data = line.decode("utf-8")
                if data.__contains__("DONE"):
                    continue
                else:
                    print("json loads error:->{}".format(line))
                    continue
    # 等待整个请求完全返回
    response.close()
    if len(json.dumps(package_info, ensure_ascii=False)) > 32762:
        print(f"数据包超长:{package_info}")
    return package_info


if __name__ == '__main__':

    parser = argparse.ArgumentParser(description="处理命令行参数")
    # 并发数量参数化，不填默认为1
    parser.add_argument("--c", type=int, required=False, help="并发数")
    # 运行文件参数化，不填默认为“数据问答测评”
    parser.add_argument("--f", type=str, required=False, help="文件名")
    # 测试集运行条数参数化，不填默认为全部case
    parser.add_argument("--n", type=int, required=False, help="处理多少行数据")

    # 解析参数
    args = parser.parse_args()
    if args.c is not None:
        concurrency = args.c
    else:
        concurrency = 1
    if args.f is not None:
        input_file = args.f
    else:
        input_file = 'recommend.xlsx'
    if args.n is not None:
        max_rows = args.n
    else:
        max_rows = 999999

    print('开始处理数据...')
    print(f'数据来源文件:{input_file}')
    print(f'并发数:{concurrency}')
    print(f'处理行数:{"全部" if max_rows == 999999 else max_rows}')

    xls = pd.read_excel(input_file, sheet_name="Sheet1", keep_default_na=False)

    # 定义新文件夹的名称
    folder_name = "effect_recommend"
    # 检查文件夹是否已存在，如果不存在则创建新文件夹,当前路径下
    if not os.path.exists(folder_name):
        os.makedirs(folder_name)
    #   定义结果生成文件
    res_file = f"{folder_name}/{datetime.datetime.now()}_effect_result.xlsx"
    future_list = []
    sheet_data = []
    data = []
    index = 1
    with concurrent.futures.ThreadPoolExecutor(max_workers=concurrency) as exe:
        print(f"{datetime.datetime.now()}case 开始执行")
        for rows in xls.iterrows():
            if index > max_rows:
                break
            future_list.append(exe.submit(case, rows, index))
            index += 1
    for future in concurrent.futures.as_completed(future_list):
        result = future.result()
        if result is not None and type(result) == dict:
            data.append(result)

    sheet_data = sorted(data, key=lambda x: x['index'])
    df = pd.DataFrame(sheet_data)
    df.to_excel(res_file, index=False)
    print(f"{datetime.datetime.now()}case 执行完成")
    print(f"case结果保存在{res_file}")