import hashlib

from qdrant_client import QdrantClient
from qdrant_client import models
import re

from qdrant_client.http.exceptions import ResponseHandlingException


def get_collection_qa_set(client, collection_name, value_type):
    total_count = client.count(collection_name=collection_name).count
    print(total_count)
    answer_ids = []
    qa_interventions = []

    # 每次请求的数据数量
    limit = 1000

    # 计算总共需要翻页的次数
    num_pages = (total_count + limit - 1) // limit

    # 循环获取数据
    for page in range(num_pages):
        # 计算偏移量
        offset = page * limit


        # 使用scroll方法获取数据
        if value_type == "intervention":
            results = client.scroll(
                collection_name=collection_name,
                scroll_filter=models.Filter(
                    must=[
                        models.FieldCondition(key="metadata.source_type", match=models.MatchValue(value=value_type)),
                    ]
                ),
                offset=offset,
                limit=limit,
                with_payload=True,
                with_vectors=False,
            )
        else:
            results = client.scroll(
                collection_name=collection_name,
                offset=offset,
                limit=limit,
                with_payload=True,
                with_vectors=False,
            )

        # 遍历每个记录，并提取 question_id 和 data_id
        for record in results[0]:
            metadata = record.payload['metadata']
            if value_type == "intervention":
                question_id = metadata['question_id']
                answer_id = metadata['answer_id']

                qa_intervention = {
                    "collection_name": collection_name,
                    "question_id": question_id,
                    "answer_id": answer_id
                }

                answer_ids.append(answer_id)
                qa_interventions.append(qa_intervention)

            elif value_type == "qa_intervention":
                try:
                    section_id = metadata['answer_id']
                    answer_ids.append(section_id)
                except:
                    continue

    return answer_ids, qa_interventions


def get_collection_qa_answer(client, collection_name,value_type, dataset_id=None):
    total_count = client.count(collection_name=collection_name).count
    print(total_count)
    answer_ids = []
    qa_interventions = []

    # 每次请求的数据数量
    limit = 1000

    # 计算总共需要翻页的次数
    num_pages = (total_count + limit - 1) // limit
    count = 0
    q_cnt = 0
    # 循环获取数据
    for page in range(num_pages):
        # 计算偏移量
        offset = page * limit
        results = client.scroll(
            collection_name=collection_name,
            offset=offset,
            limit=limit,
            with_payload=True,
            with_vectors=False,
        )
        #   使用scroll方法获取数据
        if value_type == "intervention":
            if dataset_id:
                results = client.scroll(
                    collection_name=collection_name,
                    scroll_filter=models.Filter(
                        must=[
                            models.FieldCondition(key="metadata.source_type", match=models.MatchValue(value=value_type)),
                            models.FieldCondition(key="metadata.data_id", match=models.MatchValue(value=dataset_id)),

                        ]
                    ),
                    offset=offset,
                    limit=limit,
                    with_payload=True,
                    with_vectors=False,
                )
            else:
                results = client.scroll(
                    collection_name=collection_name,
                    scroll_filter=models.Filter(
                        must=[
                            models.FieldCondition(key="metadata.source_type", match=models.MatchValue(value=value_type)),
                        ]
                    ),
                    offset=offset,
                    limit=limit,
                    with_payload=True,
                    with_vectors=False,
                )
            print(f"question:->{len(results[0])}->{results[0]}")
        # else:
        #     results = client.scroll(
        #         collection_name=collection_name,
        #         offset=offset,
        #         limit=limit,
        #         with_payload=True,
        #         with_vectors=False,
        #     )

        # 遍历每个记录，并提取 question_id 和 data_id
        i=1
        for record in results[0]:
            metadata = record.payload['metadata']
            try:
                question_id = metadata['question_id']
                answer_id = metadata['answer_id']
            except:
                #   有answer，没有对应的query，记录q_cnt
                # print(metadata)
                q_cnt = q_cnt + 1
                continue
            qa_intervention = {
                "collection_name": collection_name,
                "question_id": question_id,
                "answer_id": answer_id
            }

            collection_name = collection_name.replace("questions", "section")
            try:
                tt = get_record_by_id(client, collection_name, answer_id)
                print(f"answer:->{i}->{tt[0]}")
                i+=1
                if tt[0] == []:
                    #   有query，没有answer，打印query_id和对应的answer_id，记录count
                    count =  count + 1
                    print(question_id, answer_id)
            except BaseException:
                print(f"执行{collection_name}的{answer_id}异常了！")
    return count, q_cnt


def get_record_by_id(client, collection_name, record_id):
    results = client.scroll(
        collection_name=collection_name,
        scroll_filter=models.Filter(
            must=[
                models.FieldCondition(
                    key="metadata.answer_id",
                    match=models.MatchValue(value=record_id)
                )
            ]
        ),
        with_payload=True,
        with_vectors=False,
    )

    return results

def get_collection_name(dataset_id):
    #   根据dataset_id获取collection
    QUESTION_COLLECTION_PREFIX = "questions_embeddings_v01_orionstar_text-embedding-bge_"

    index = int.from_bytes(hashlib.md5(dataset_id.encode("utf-8")).digest(), byteorder="big") % 100

    return f"{QUESTION_COLLECTION_PREFIX}{index:0{2}d}"

def get_test_array_collection_name()->list:
    #   需要测试的应用dataset_id
    array_dataset_id=["af235d4da65d1e0e645ee47ed54d03fd","80c1373483bcaa1fc6c013b3d2f6d5f8",
                      "4bddb33d1d19e563bc8484db4dea87bb","68fa0b0ca00a99c4105d15215c0281d6",
                      "ca4c51d4088e74516b88f2ffbdfde1e5","e0223f92ee254fb5aaef12c32c617446",
                      "45d43ace594864a2a676c52c8be46d33","7dcae2133b817549ad522257816d0c34"]

    array_dataset_id=["a86d5c1476e4655d39f4458f30487cc9","a5c140a50fe5a815d6fa0b4eeb2449e7",
                      "90c85e55ef9c1088dfa24235f6b29b6a","266fc5d0a46a993ec2bb6c3e2b5c1134",
                      "b648b01178a48e0255bfa86f240f8ddc","41260cf3680634f9f690fffdeacd1f27",
                      "cdce3363fb0a5f114feb3ef96edd6f20","9e2c88b65958b9aefb76c8b14c326bd0"]
    collection_list=[]
    for dataset_id in array_dataset_id:
        collection_list.append({dataset_id:get_collection_name(dataset_id)})
    # print(collection_list)
    return collection_list

def run_test():
    """
    德生测试环境，使用固定的应用测试QA失效问题
    :return:
    """
    collection_list = get_test_array_collection_name()
    client = QdrantClient(qdrant_url, prefer_grpc=False)
    for q_name in collection_list:
        for k,v in dict(q_name).items():
            # print(k,v)
            count, cnt = get_collection_qa_answer(client=client, collection_name=v,dataset_id=k, value_type="intervention")

        print(q_name, count, cnt)
        print("=======================================================")

# def test_get_answer():
#     client = QdrantClient(qdrant_url, prefer_grpc=False)
#     answer_id_list=["6d1b9107816c13c3c1142a3e9b267a12","9774d2c580e532a2eb9fa0c233d4f54b","54ec053ad5f7a80b7056f97bfcbb5488","dc0c64f8f25bcae0406999b46e2567d1","dda946acfcbc3a221072f12408384091","668a9d44709cd6f8801e0873372ac757","9df1078adb78fa2c3d128d42030faf37","b30655b897351211d34b0698be79eb84","7db0bba16dbbaa7d8fc07fca94eea50c","f179df690a8e595332e3f9b0fbebc937","bcd19b19e513e2ea0da91d5712444cea","d44bce4b85eb666c6c4d97c93cc675b9","8b0485627df5dd5cd321439dd2f440b6","f179df690a8e595332e3f9b0fbebc937","b7fa5f5cd685975f9f923bbe73c9503c","40142fcba964ae3b401a08306fc2d704","dc0c64f8f25bcae0406999b46e2567d1","517c201b5a567c4cb5d441e0c6a14cad","33c3e2a38f9be9400dc030c267ed4d37","40142fcba964ae3b401a08306fc2d704","51055e37dad64cd0a3d7bc905d196265","40142fcba964ae3b401a08306fc2d704"]
#     for answer_id in answer_id_list:
#         tt = get_record_by_id(client, "section_embeddings_v01_orionstar_text-embedding-bge_68", answer_id)
#         print(tt[0])
if __name__ == '__main__':

    # 设置 Qdrant 的地址
    # qdrant_url = "http://10.118.11.240:6334"
    #  德生测试环境
    # qdrant_url = "http://10.100.10.203:30605"
    #   猎户测试环境
    qdrant_url="http://10.118.13.232:6333/"
    # test_get_answer()
    run_test()
    #   分布式qdrant
    # qdrant_url="http://10.100.10.87:6333/"


    #
    #
    # # 创建 Qdrant 客户端
    # client = QdrantClient(qdrant_url, prefer_grpc=False)
    #
    # # get_record_by_id(client, "section_embeddings_v01_orionstar_text-embedding-bge_24", "5b2f688deeb0aa79d87bee90b65a4d56")
    # #
    # # exit(1)
    # # 获取所有的 collection
    # collections = client.get_collections()
    # collections = str(collections).replace("collections=", "")
    #
    # # 使用正则表达式提取集合名称
    # collection_names = re.findall(r"name='(.*?)'", collections)
    #
    # question_collections = []
    # # section_collections = []
    # for name in collection_names:
    #     if name.startswith("question") and ("text-embedding-bge" in name):
    #         question_collections.append(name)
    # question_collections.sort()
    # i=1
    # for q_name in question_collections:
    #     if i%50==0:
    #         print("重新获取连接")
    #         client = QdrantClient(qdrant_url, prefer_grpc=False)
    #     print(q_name)
    #     count, cnt = get_collection_qa_answer(client, q_name, "intervention")
    #     print(q_name, count, cnt)
    #     print("=======================================================")
    #     i+=1
    #
    #
