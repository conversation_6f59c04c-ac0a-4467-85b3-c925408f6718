from qdrant_client.http.exceptions import ResponseHandlingException
# from tool.util import get_time
# from tool.util import get_exe
import re
from qdrant_client import models
from qdrant_client import QdrantClient
import requests
import hashlib
import os
import sys
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print(BASE_DIR)
sys.path.append(BASE_DIR)



def get_collection_qa_set(client, collection_name, value_type):
    total_count = client.count(collection_name=collection_name).count
    print(total_count)
    answer_ids = []
    qa_interventions = []

    # 每次请求的数据数量
    limit = 1000

    # 计算总共需要翻页的次数
    num_pages = (total_count + limit - 1) // limit

    # 循环获取数据
    for page in range(num_pages):
        # 计算偏移量
        offset = page * limit

        # 使用scroll方法获取数据
        if value_type == "intervention":
            results = client.scroll(
                collection_name=collection_name,
                scroll_filter=models.Filter(
                    must=[
                        models.FieldCondition(key="metadata.source_type", match=models.MatchValue(value=value_type)),
                    ]
                ),
                offset=offset,
                limit=limit,
                with_payload=True,
                with_vectors=False,
            )
        else:
            results = client.scroll(
                collection_name=collection_name,
                offset=offset,
                limit=limit,
                with_payload=True,
                with_vectors=False,
            )

        # 遍历每个记录，并提取 question_id 和 data_id
        for record in results[0]:
            metadata = record.payload['metadata']
            if value_type == "intervention":
                question_id = metadata['question_id']
                answer_id = metadata['answer_id']

                qa_intervention = {
                    "collection_name": collection_name,
                    "question_id": question_id,
                    "answer_id": answer_id
                }

                answer_ids.append(answer_id)
                qa_interventions.append(qa_intervention)

            elif value_type == "qa_intervention":
                try:
                    section_id = metadata['answer_id']
                    answer_ids.append(section_id)
                except:
                    continue

    return answer_ids, qa_interventions


def get_collection_qa_answer(client, collection_name, value_type, dataset_id=None):
    total_count = client.count(collection_name=collection_name).count
    # print(total_count)

    # 每次请求的数据数量
    limit = 1000

    # 计算总共需要翻页的次数
    num_pages = (total_count + limit - 1) // limit
    count = 0
    q_cnt = 0
    # 循环获取数据
    for page in range(num_pages):
        # 计算偏移量
        offset = page * limit
        results = client.scroll(
            collection_name=collection_name,
            offset=offset,
            limit=limit,
            with_payload=True,
            with_vectors=False,
        )
        #   使用scroll方法获取数据
        if value_type == "intervention":
            results = client.scroll(
                collection_name=collection_name,
                scroll_filter=models.Filter(
                    must=[
                        models.FieldCondition(key="metadata.source_type", match=models.MatchValue(value=value_type))
                    ]
                ),
                offset=offset,
                limit=limit,
                with_payload=True,
                with_vectors=False,
            )
            # print(f"question:->{len(results[0])}->{results[0]}")

        # 遍历每个记录，并提取 question_id 和 data_id
        i = 1
        for record in results[0]:
            metadata = record.payload['metadata']
            try:
                question_id = metadata['question_id']
                answer_id = metadata['answer_id']
                query = record.payload['page_content']
                similarity_search(client=client, query=query, collection_names=collection_name)
            except:
                #   有answer，没有对应的query，记录q_cnt
                # print(metadata)
                q_cnt = q_cnt + 1
                continue
            qa_intervention = {
                "collection_name": collection_name,
                "question_id": question_id,
                "answer_id": answer_id
            }

            collection_name = collection_name.replace("questions", "section")
            try:
                tt = get_record_by_id(client, collection_name, answer_id)
                # print(f"answer:->{i}->{tt[0]}")
                i += 1
                if tt[0] == []:
                    #   有query，没有answer，打印query_id和对应的answer_id，记录count
                    count = count + 1
                    # print(question_id, answer_id)
            except BaseException:
                print(f"执行{collection_name}的{answer_id}异常了！")
    return count, q_cnt


def get_record_by_id(client, collection_name, record_id):
    results = client.scroll(
        collection_name=collection_name,
        scroll_filter=models.Filter(
            must=[
                models.FieldCondition(
                    key="metadata.answer_id",
                    match=models.MatchValue(value=record_id)
                )
            ]
        ),
        with_payload=True,
        with_vectors=False,
    )

    return results


def get_collection_name(dataset_id):
    #   根据dataset_id获取collection
    QUESTION_COLLECTION_PREFIX = "questions_embeddings_v01_orionstar_text-embedding-bge_"

    index = int.from_bytes(hashlib.md5(dataset_id.encode("utf-8")).digest(), byteorder="big") % 100

    return f"{QUESTION_COLLECTION_PREFIX}{index:0{2}d}"


def similarity_search(client, query, collection_names):
    vector = get_vector(query)
    questions_filter = {
        "is_using": 1,
        "source_type": "intervention",
    }
    response = client.search(limit=3, query_vector=vector, collection_name=collection_names, questions_filter=questions_filter)
    if response:
        pass
        # print("查询结果：", response)
    else:
        print("查询失败")


def get_vector(query):
    url = "http://172.16.102.15:8080/embed"
    payload = {
        "inputs": [
            query
        ],
        "model": "test",
        "reqid": "111111111",
        "is_query": ""
    }
    res = requests.request(method="POST", url=url, json=payload).json()
    return res[0]


if __name__ == '__main__':
    #   分布式qdrant
    # qdrant_url="http://10.100.10.87:6333/"
    qdrant_url = "10.118.13.232:6333/"

    # 创建 Qdrant 客户端
    client = QdrantClient(qdrant_url, prefer_grpc=True)
    segments_info = client.get_collection_segments(collection_name="section_embeddings_v01_orionstar_text-embedding-bge_37")
    print(segments_info)
    # 获取所有的 collection
    collections = client.get_collections()
    collections = str(collections).replace("collections=", "")

    # 使用正则表达式提取集合名称
    collection_names = re.findall(r"name='(.*?)'", collections)

    question_collections = []
    # section_collections = []
    for name in collection_names:
        if name.startswith("question") and ("text-embedding-bge" in name):
            question_collections.append(name)
    question_collections.sort()
    i = 1
    ex = get_exe(20)
    # # stop = get_time(time_res="T+2h")
    # now = get_time()

    # while now < stop:
    #     for q_name in question_collections:
    #         if i % 50 == 0:
    #             # print("重新获取连接")
    #             client = QdrantClient(qdrant_url, prefer_grpc=False)
    #         # print(q_name)
    #         ex.submit(get_collection_qa_answer, client, q_name, "intervention")
    #         # count, cnt = get_collection_qa_answer(client, q_name, "intervention")
    #         # print(q_name, count, cnt)
    #         # print("=======================================================")
    #         i += 1
    #         now = get_time()
