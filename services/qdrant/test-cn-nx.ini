[server]
  host = 0.0.0.0
  port = 23320
  workers = 4

[redis]
  host = autolm.b6uduc.ng.0001.cnw1.cache.amazonaws.com.cn
  port = 6379
  default_db = 0
  config_data_db = 1
  chat_history_db = 2
  embeddings_cache_db = 10

[mysqlcc]
  url = robot-bxm-dev.cswpntxnjjxy.rds.cn-northwest-1.amazonaws.com.cn
  user = ctaialgo
  pass = ezr3xqpxkrxlf2ad7ag78s590vbw787q
  max_connections = 10

[openai]
  key = ***************************************************,***************************************************,***************************************************,***************************************************,***************************************************

[log]
  dir = ./logs

[download]
  download_dir = ./download

[tmppath]
  path = ./tmp/

[filestatus]
  url = https://dev-openapi.ainirobot.com/v1/ctai/ctai_proc_doc_webhook
  key = bz00e26a8ace8843749ae9aaccd37aec26z804153b39d1d95163cb99e41183fad1e

[vectorstore]
  host = *************
  port = 6333
  section_collection_prefix = section_embeddings_v01
  summary_collection_prefix = summary_embeddings_v01
  questions_collection_prefix = questions_embeddings_v01

[embeddings]
  provider = openai
  modelname = text-embedding-ada-002
  apikey = ***************************************************,***************************************************

[mqttserver]
  host = dev-rmq.ainirobot.com
  port = 61613
  subtopic = $share/test-cn/ctai_algo_query_receive_langchain/#
  thread = 1
  user = a0dd26460536ef2950c9c3d882271b6d
  pass = e28cef8dad6a72c558940998d0661ec2

[azure]
  openai_api_base = https://orionai-east-us.openai.azure.com/
  openai_api_version = 2023-03-15-preview
  deployment_name = deployment-dd272c6929ae489a937a3e0e0b1514e7
  openai_api_key = ********************************