# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : query_script.py
<AUTHOR> <EMAIL>
@Time   : 2024-01-24 16:21:49
@Version: v1.0
"""
import argparse
import hashlib
from pathlib import Path


NUM_SPLIT_COLLECTIONS: int = 100
COLLECTION_NAME_NUM_DIGITS: int = 2
COLLECTION_NAME_SEPERATOR: str = "_"


def get_args():
    def file_should_exist(arg):
        if Path(arg).is_file():
            return arg
        else:
            raise FileNotFoundError(f"{arg}")

    parser = argparse.ArgumentParser(description="Convert data_id to collection name.",
                                     formatter_class=argparse.ArgumentDefaultsHelpFormatter)
    parser.add_argument("data_id", default=None, type=str, help="data_id to convert")
    parser.add_argument("-p", "--collection_name_prefix", default=None, type=str,
                        help="collection_name_prefix in autolm-format: "
                             "{collection_prefix}_{embedding_provider}_{embedding_modelname}")
    parser.add_argument("-f", "--config_ini_file", type=file_should_exist, default=None, help=".ini config file to get collection name prefix")

    args = parser.parse_args()
    return args

def get_collection_name_from_data_id(data_id: str, collection_name_prefix: str, *, size=100, num_digits=2, seperator="_"):
    index = int.from_bytes(hashlib.md5(data_id.encode("utf-8")).digest(), byteorder="big") % size
    name = f"{collection_name_prefix}{seperator}{index:0{num_digits}d}"
    # TODO validate the collection name
    return name


def get_collection_name_prefix_from_ini_file(filename: str) -> list[str]:
    import configparser
    config = configparser.ConfigParser()
    config.read(filename)

    provider = config["embeddings"]["provider"]
    modelname = config["embeddings"]["modelname"]
    section_prefix = config["vectorstore"]["section_collection_prefix"]
    summary_prefix = config["vectorstore"]["summary_collection_prefix"]
    questions_prefix = config["vectorstore"]["questions_collection_prefix"]

    return [
        COLLECTION_NAME_SEPERATOR.join([section_prefix, provider, modelname]),
        COLLECTION_NAME_SEPERATOR.join([summary_prefix, provider, modelname]),
        COLLECTION_NAME_SEPERATOR.join([questions_prefix, provider, modelname]),
    ]


def main():
    args = get_args()

    data_id: str = args.data_id

    collection_name_prefix = args.collection_name_prefix
    if collection_name_prefix is not None:
        dst_collection_name = get_collection_name_from_data_id(
            data_id, collection_name_prefix,
            size=NUM_SPLIT_COLLECTIONS, num_digits=COLLECTION_NAME_NUM_DIGITS,
            seperator=COLLECTION_NAME_SEPERATOR)
        print(f"{dst_collection_name}")
    else:
        config_ini_file = args.config_ini_file
        if config_ini_file is None:
            raise ValueError("both collection_name_prefix and config_ini_file is None")
        for p in get_collection_name_prefix_from_ini_file(config_ini_file):
            dst_collection_name = get_collection_name_from_data_id(
                data_id, p,
                size=NUM_SPLIT_COLLECTIONS, num_digits=COLLECTION_NAME_NUM_DIGITS,
                seperator=COLLECTION_NAME_SEPERATOR)
            print(f"{dst_collection_name}")


if __name__ == '__main__':
    main()
