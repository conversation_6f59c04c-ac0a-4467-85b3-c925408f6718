# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : gpt.py
<AUTHOR> <EMAIL>
@Time   : 2023-11-11 18:19:56
@Version: v1.0
"""
import openai

openai.api_key = '***************************************************'  # 替换为您的 OpenAI API 密钥


def chat_with_gpt(prompt):
    response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[
            {"role": "user", "content": prompt}
        ],
        # temperature=0.9
    )

    answer = response['choices'][0]['message']['content']
    print("GPT-4 Answer:-> ".format(answer))
    return answer


def chat_with_gpt_url(prompt):
    import requests
    import json
    messages = [
        {"role": "user", "content": prompt}
    ]
    # api_key = '***************************************************'
    api_key = '***********************************************************************************************'
    api_url = 'https://api.openai.com/v1/chat/completions'
    headers = {"content-type": "application/json", "api_key": api_key}
    response = requests.request(method="post", url=api_url, headers=headers, json=messages)
    answer = response['choices'][0]['message']['content']


if __name__ == '__main__':
    chat_with_gpt_url('你好')
