# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : generation_answer.py
<AUTHOR> <EMAIL>
@Time   : 2023-11-01 18:30:45
@Version: v1.0
"""

from tool.util import *
from tool.logger import log
from services.generation_answer import generation_answer
from concurrent.futures import ThreadPoolExecutor
from concurrent.futures import as_completed
from test_conifg.path_config import SENSITIVE_INPUT_PATH, SENSITIVE_OUTPUT_PATH, DOWNLOAD_PATH
from test_conifg.config import ip, port
from services.generation_answer.generation_answer import request

ex = ThreadPoolExecutor(max_workers=6)


def get_answer(args):
    query_text = str(args.get("line")).strip()
    # log.info("执行的文件【{file_name}】,当前第{index}行，question是：{question}".format(file_name="sensitive.jsonl",
    #                                                                                 index=args.get("index"),
    #                                                                                 question=query_text))
    response_data = generation_answer.request(query_text, gen=True)
    ret = response_data.get("ret", -1)
    msg = response_data.get("msg", "")
    data_illegal_status = -1  # 默认值为-1
    answer_text = ""
    try:
        data_illegal_status = int(response_data["data"]["illegal_status"])
        answer_text = response_data["data"]["answer_text"]
    except (KeyError, ValueError):
        pass
    # print(response_data)
    #   如果模型给了answer，校验question和answer是否包含敏感词，正常能返回answer肯定question没有命中敏感词，但还是添加一层校验

    answer_illegal = ""
    question_word = ""
    # if ret == "0":
    #     answer_illegal = check_container_illegal(answer_text)
    # question_word = check_container_illegal(query_text)
    args.get("sheet").append(
        [args.get("ID", ""), query_text, ret, msg, data_illegal_status, answer_text, question_word, answer_illegal])


def sensitive_check_task():
    """
    读取的文件在input下，类型是文本，txt或者jsonl
    根据question列表获取模型给的回答，并将回答记录在Excel表格，包含question、返回的code、Illegal Status、Answer Text
    :return:
    """
    workbook = openpyxl.Workbook()
    sheet = workbook.active
    sheet.append(["Query Text", "Ret", "Msg", "Illegal Status", "Answer Text", "question_illegal", "answer_illegal"])

    output_file = "{output}{sep}{D}{sep}".format(output=SENSITIVE_OUTPUT_PATH, D=get_time(format="%Y%m%d"), sep=os.sep)
    if not os.path.exists(output_file):
        os.makedirs(output_file)
    file_name = "sensitive_gen_answer_{T}.xlsx".format(T=get_time())

    #   在敏感词文件夹下存放的文本敏感词，修改响应的y.txt名称
    with open('{path}{sep}y.txt'.format(path=SENSITIVE_INPUT_PATH), 'r', encoding='utf-8') as file:
        index = 0
        for line in file:
            # if index>1:break
            index += 1
            agrs = {
                "line": line,
                "index": index,
                "work": workbook,
                "sheet": sheet,
                "file": output_file + file_name
            }
            ex.submit(get_answer, agrs)
        log.info("任务全部发出，执行中")


def upload_sensitive_check_task(input_file, f_type: str, output_f: str):
    """
    根据question列表获取模型给的回答，并将回答记录在Excel表格，包含question、返回的code、Illegal Status、Answer Text
    :return:
    """
    #   通过request测试下是否需要更新key
    request("你好")
    workbook = openpyxl.Workbook()
    sheet = workbook.active
    sheet.append(
        ["ID", "Query Text", "Ret", "Msg", "Illegal Status", "Answer Text", "question_illegal", "answer_illegal"])
    output_path = "{output}{sep}".format(output=DOWNLOAD_PATH, sep=os.sep)
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    #   TODO 判断文件类型是json
    future_list = []
    if f_type == "json":
        with input_file as file:
            index = 0
            for line in file:
                # if index>1:break
                index += 1
                agrs = {
                    "line": line,
                    "index": index,
                    "work": workbook,
                    "sheet": sheet,
                    "file": output_path + output_f
                }
                future = ex.submit(get_answer, agrs)
                future_list.append(future)
            log.info("任务全部发出，执行中")
    else:
        sheet_name_list = list(input_file)  # 获取所有sheet_name
        sheet_index = 1  # 标记当前sheet索引
        for sheet_name in sheet_name_list:
            index = 0
            for row in input_file[sheet_name].iterrows():
                # if index>10:break
                index += 1
                agrs = {
                    #   默认第一列就是question，且只有一列
                    "line": row[1].get("query_text"),
                    "ID": row[1].get("ID"),
                    "index": index,
                    "work": workbook,
                    "sheet": sheet,
                    "file": output_path + output_f
                }
                future = ex.submit(get_answer, agrs)
                future_list.append(future)
            log.info("任务全部发出，执行中")
    tqdm_task = get_tqdm(iter_len=len(future_list))
    for future in as_completed(future_list):
        tqdm_task.update(1)
    tqdm_task.close()
    workbook.save(output_path + output_f)
    log.info("save result file to: {}".format(output_path + output_f))
    path = "/api/download/{}".format(output_f)
    title = "敏感词任务执行完成"
    down_url = "http://{ip}:{port}{path}".format(ip=ip, port=port, path=path)
    send_card_message(title=title, down_url=down_url)


if __name__ == '__main__':
    sensitive_check_task()
