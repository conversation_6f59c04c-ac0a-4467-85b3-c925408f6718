import pandas as pd
import json
from datetime import datetime
import time


#   处理时间计算
def convert_to_milliseconds(time_str):
    """
    将"分:秒:帧"格式的时间转换为毫秒
    帧率为30fps，即一帧约等于33.333毫秒
    """
    try:
        # 分割时间字符串
        seconds, frames = map(int, time_str.split(':'))

        # 计算总毫秒数
        total_ms = (seconds * 1000) + (frames * 1000 // 30)
        return total_ms
    except Exception as e:
        raise ValueError(f"时间格式错误，请使用'分:秒:帧'格式。错误信息：{str(e)}")


def calculate_time_difference(time1, time2):
    """
    计算两个时间点之间的差值（毫秒）
    """
    try:
        ms1 = convert_to_milliseconds(time1)
        ms2 = convert_to_milliseconds(time2)

        # 计算时间差
        difference_ms = abs(ms2 - ms1)
        return difference_ms
    except Exception as e:
        print(f"计算失败：{str(e)}")
        return None


def format_milliseconds(ms):
    """
    将毫秒格式化为可读的时间格式
    """
    minutes = ms // (60 * 1000)
    ms = ms % (60 * 1000)
    seconds = ms // 1000
    remaining_ms = ms % 1000

    return f"{minutes}分{seconds}秒{remaining_ms}毫秒"


def format_seconds(ms):
    """
    将毫秒转换为秒（保留3位小数）
    """
    return f"{ms/1000:.3f}"


def process_excel_times(df):
    """
    批量处理Excel文件中的时间数据
    """
    try:

        # 检查必要的列是否存在
        required_columns = ['T1(人结束说话)', 'T2（VadEnd）', 'T3（PlanEnd）']
        if not all(col in df.columns for col in required_columns):
            print("错误：输入文件中必须包含 'T1(人结束说话)', 'T2（VadEnd）', 'T3（PlanEnd）' 列")
            return

        # 计算时间差
        time_differences_T2T1 = []
        time_differences_T2T1_formatted = []
        time_differences_T2T1_seconds = []
        time_differences_T3T2 = []
        time_differences_T3T2_formatted = []
        time_differences_T3T2_seconds = []
        time_differences_T3T1 = []
        time_differences_T3T1_formatted = []
        time_differences_T3T1_seconds = []

        for index, row in df.iterrows():
            try:
                difference_T2T1 = calculate_time_difference(str(row['T1(人结束说话)']), str(row['T2（VadEnd）']))
                difference_T3T2 = calculate_time_difference(str(row['T2（VadEnd）']), str(row['T3（PlanEnd）']))
                difference_T3T1 = calculate_time_difference(str(row['T3（PlanEnd）']), str(row['T1(人结束说话)']))
                if difference_T2T1 is not None:
                    time_differences_T2T1.append(difference_T2T1)
                    time_differences_T2T1_formatted.append(format_milliseconds(difference_T2T1))
                    time_differences_T2T1_seconds.append(format_seconds(difference_T2T1))
                else:
                    time_differences_T2T1.append(None)
                    time_differences_T2T1_formatted.append(None)
                    time_differences_T2T1_seconds.append(None)
                if difference_T3T2 is not None:
                    time_differences_T3T2.append(difference_T3T2)
                    time_differences_T3T2_formatted.append(format_milliseconds(difference_T3T2))
                    time_differences_T3T2_seconds.append(format_seconds(difference_T3T2))
                else:
                    time_differences_T3T2.append(None)
                    time_differences_T3T2_formatted.append(None)
                    time_differences_T3T2_seconds.append(None)
                if difference_T3T1 is not None:
                    time_differences_T3T1.append(difference_T3T1)
                    time_differences_T3T1_formatted.append(format_milliseconds(difference_T3T1))
                    time_differences_T3T1_seconds.append(format_seconds(difference_T3T1))
                else:
                    time_differences_T3T1.append(None)
                    time_differences_T3T1_formatted.append(None)
                    time_differences_T3T1_seconds.append(None)
            except Exception as e:
                print(f"处理第 {index + 1} 行时发生错误：{str(e)}")
                time_differences_T2T1.append(None)
                time_differences_T2T1_formatted.append(None)
                time_differences_T2T1_seconds.append(None)
                time_differences_T3T2.append(None)
                time_differences_T3T2_formatted.append(None)
                time_differences_T3T2_seconds.append(None)
                time_differences_T3T1.append(None)
                time_differences_T3T1_formatted.append(None)
                time_differences_T3T1_seconds.append(None)

        # 添加结果列
        df['T2T1时间差(毫秒)'] = time_differences_T2T1
        # df['T2T1时间差(秒)'] = time_differences_T2T1_seconds
        # df['T2T1格式化时间差'] = time_differences_T2T1_formatted
        df['T3T2时间差(毫秒)'] = time_differences_T3T2
        # df['T3T2时间差(秒)'] = time_differences_T3T2_seconds
        # df['T3T2格式化时间差'] = time_differences_T3T2_formatted
        df['T3T1时间差(毫秒)'] = time_differences_T3T1
        # df['T3T1时间差(秒)'] = time_differences_T3T1_seconds
        # df['T3T1格式化时间差'] = time_differences_T3T1_formatted
        print("时间计算处理完成!")
        return df
    except Exception as e:
        print(f"处理Excel文件时发生错误：{str(e)}")


# 假设elapse_info列包含的是JSON格式的字符串
# 将JSON字符串转换为字典，然后展开成多个列


def expand_elapse_info(elapse_info):
    try:
        # 将字符串转换为字典
        info_dict = json.loads(elapse_info)
        return pd.Series(info_dict)
    except:
        return pd.Series()


if __name__ == "__main__":
    # 读取Excel文件
    file_path = 'Agent 测试集性能测试_V20250210.1_VAD_END_INTERVAL=450_old.xlsx'
    df = pd.read_excel(file_path)

    # 处理时间计算
    df = process_excel_times(df)
    # 展开elapse_info列
    expanded_df = df['elapse_info'].apply(expand_elapse_info)
    # 获取重复的列名
    duplicate_columns = df.columns.intersection(expanded_df.columns)
    # 从原始df中删除重复的列
    df = df.drop(columns=duplicate_columns)
    # 将展开的列与原始数据框合并
    result_df = pd.concat([df, expanded_df], axis=1)

    # 计算时间差（毫秒）
    result_df['plan_end_timestamp-vad_end_timestamp（毫秒）'] = (
        (pd.to_datetime(result_df['plan_end_timestamp']) -
         pd.to_datetime(result_df['vad_end_timestamp'])).dt.total_seconds() * 1000 +
        (result_df['agent_core_total_time'] - result_df['plan_total_cost_time'])
    )  # 转换为毫秒
    print("info字段拆分完成!")
    #   计算T3T2时间差(毫秒)和plan_end_timestamp-vad_end_timestamp差值
    result_df['主观感受(T3T2)-系统时间(毫秒)'] = result_df['T3T2时间差(毫秒)'] - result_df['plan_end_timestamp-vad_end_timestamp（毫秒）']
    # 保存结果到新的Excel文件，确保包含所有列
    output_path = f'{time.strftime("%Y-%m-%d_%H-%M-%S")}_{file_path.split(".")[0]}_result.xlsx'
    result_df.to_excel(output_path, index=False)
    print(f"结果保存完成! 文件路径: {output_path}")

    # 填充caseid
    df = fill_caseid(df)
    df.to_excel(f'{time.strftime("%Y-%m-%d_%H-%M-%S")}_{file_path.split(".")[0]}_caseid.xlsx', index=False)
    print(f"caseid填充完成! 文件路径: {f'{time.strftime("%Y-%m-%d_%H-%M-%S")}_{file_path.split(".")[0]}_caseid.xlsx'}")
