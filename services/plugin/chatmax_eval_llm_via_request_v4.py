# -*- coding: utf-8 -*-
import argparse
import time, os
from pathlib import Path
from loguru import logger
import pandas as pd
import json
import requests
from tqdm import tqdm
import uuid
import re
import traceback
from copy import deepcopy
from services.gtp.gpt import chat_with_gpt
from test_conifg.path_config import PLUGIN_OUTPUT_PATH
from tool.util import get_exe

exe = get_exe(max_works=5)

PROJECT_DIR = Path(__file__).resolve().parent
TEMPLATES_DIR = PROJECT_DIR / "templates"
QA_TEMPLATE_FILE = str(TEMPLATES_DIR / "template_chatmax_qa_testcase_450_20231023.xlsx")
DOUDI_TEMPLATE_FILE = str(TEMPLATES_DIR / "template_chatmax_doudi_testcase_293_20231023.xlsx")
REACT_TEMPLATE_FILE = str(TEMPLATES_DIR / "template_eval_ReAct_v3.xlsx")
QA_TEMPLATE_FILE = None
DOUDI_TEMPLATE_FILE = None
REACT_TEMPLATE_FILE = None
MODIFY_PROMPT_TEMPLATE_FILE = str("prompt.xlsx")


def get_args():
    def is_file_type(arg):
        if Path(arg).is_file():
            return arg
        else:
            raise FileNotFoundError(f"{arg}")

    parser = argparse.ArgumentParser(description="eval llm via request for chatmax",
                                     formatter_class=argparse.ArgumentDefaultsHelpFormatter)
    parser.add_argument("--host", type=str, default="*************",
                        help="llm server host, albji-A100-train01 IP: ************** / ********* albji-A100-train02 IP: ************* / **********")
    parser.add_argument("--port", type=str, default="8007", help="llm server port")
    parser.add_argument("--model_id", type=str, default=None, help="model id")
    parser.add_argument("--model_name_request", type=str, default='OrionStarPlugin', help="model name to request")
    parser.add_argument("--stop_words", type=str, default='',
                        help='comma separated stop words for llm request, eg. "<|endoftext|>" "<|endoftext|>,<|im_end|>" ...')
    parser.add_argument("--qa_excel_file", type=is_file_type, default=QA_TEMPLATE_FILE, help="qa template file")
    parser.add_argument("--doudi_excel_file", type=is_file_type, default=DOUDI_TEMPLATE_FILE,
                        help="doudi template file")
    parser.add_argument("--react_excel_file", type=is_file_type, default=MODIFY_PROMPT_TEMPLATE_FILE,
                        help="react template file")
    parser.add_argument("--skip_qa", action="store_true", help="skip qa evaluation")
    parser.add_argument("--skip_doudi", action="store_true", help="skip doudi evaluation")
    parser.add_argument("--skip_react", action="store_true", help="skip react evaluation")
    parser.add_argument("--do_request_gpt4_if_needed", action="store_true",
                        help="do request gpt4 if needed, eg. we update the template file and gpt4_answer is empty")

    args = parser.parse_args()
    return args


def do_request_llm(
        url=None,
        api_key=None,
        host="0.0.0.0",
        port=8007,
        prompt="你好",
        stop_words="",  # comma separated stop words
        temperature=1e-6,
        model="orionstar",
        stream=False,
        max_generate_tokens=512,
        return_type="content",  # json, content, response
        num_retries=6,
        verbose=True
):
    if url is None:
        url = f"http://{host}:{port}/v1/chat/completions"
    headers = {"Content-Type": "application/json"}
    if api_key is not None:
        headers.update({"Authorization": api_key})
    req_data = {
        "temperature": temperature,
        "model": model,
        "stream": stream,
        "messages": [{"role": "user", "content": prompt}],
        "max_tokens": max_generate_tokens
    }
    if stop_words != "":
        req_data["stop"] = stop_words.split(",")
    num_tried = 0
    while num_tried < num_retries:
        try:
            response = requests.post(url, headers=headers, json=req_data, timeout=600)
            if return_type == "json":
                return response.json()
            elif return_type == "content":
                content = response.json()["choices"][-1]["message"]["content"]
                if verbose:
                    uuid_str = str(uuid.uuid4())
                    print(f"PROMPT {uuid_str}\n{prompt}", flush=True)
                    print(f"ANSWER {uuid_str}\n{content}", flush=True)
                return content
            else:
                return response
        except Exception as e:
            print(f'{e}', flush=True)
            num_tried += 1
            time.sleep(num_tried)
    print(f"Failed to get response from {url} for prompt:\n{prompt}", flush=True)
    return " "


def request_gpt4(
        prompt,
        url='https://api.openai.com/v1/chat/completions',
        api_key='***************************************************'
):
    api_key = "Bearer " + api_key if not api_key.startswith("Bearer ") else api_key
    return do_request_llm(url=url, api_key=api_key, prompt=prompt, model="gpt-4")


def split_text(text, pattern=r'\||｜', do_strip=True):
    splits = re.split(pattern, text)
    if do_strip:
        splits = [s.strip() for s in splits if s.strip()]
    return splits


# 正常QA评估
def chatmax_qa_eval_model_request(args):
    print("start test chatmax qa testcase!")
    df = pd.read_excel(args.qa_excel_file)
    if "final_qa_prompt" not in df.columns:
        #  ['TestSet', 'Question', 'Keywords', 'Standard answer', 'Actual answer',
        # 'followUp', 'Score', 'Unmatched key', 'Case type', 'RequestId',
        # 'DatasetId', 'DocName', 'Time used', 'DebugInfo', 'final_qa_prompt']
        def get_qa_prompt_from_DebugInfo_column(x):
            return json.loads(str(x))["qa_prompt"]

        df["final_qa_prompt"] = df["DebugInfo"].apply(get_qa_prompt_from_DebugInfo_column)
    actual_answer_list = []
    score_list = []
    unmatched_key_list = []
    tc_llm_list = []
    for _, row in tqdm(df.iterrows(), dynamic_ncols=True, total=len(df)):
        start_time = time.perf_counter()
        actual_answer = do_request_llm(host=args.host, port=args.port, model=args.model_name_request,
                                       prompt=row["final_qa_prompt"], stop_words=args.stop_words)
        tc_ms = int((time.perf_counter() - start_time) * 1000)
        tc_llm_list.append(tc_ms)
        actual_answer_list.append(actual_answer)
        keywords = split_text(str(row["Keywords"]))
        n_matched = 0
        unmatched_keys = []
        for kw in keywords:
            if kw.strip() in actual_answer:
                n_matched += 1
            else:
                unmatched_keys.append(kw)
        score = n_matched / len(keywords)
        score_list.append(score)
        unmatched_keys_str = "[" + ", ".join(unmatched_keys) + "]"
        unmatched_key_list.append(unmatched_keys_str)
    df["Actual answer"] = actual_answer_list
    df["Score"] = score_list
    df["Unmatched key"] = unmatched_key_list
    df["Time used"] = tc_llm_list
    qa_score = df["Score"].mean() * 100
    print(len(df), ",qa score:", qa_score)
    output_excel_file = f'scored_qa_{qa_score:.3f}_{args.model_id}_' + Path(args.qa_excel_file).name
    df.to_excel(output_excel_file, index=False)
    return qa_score


# 正常兜底评估
def chatmax_doudi_eval_model_request(args):
    print("start test chatmax doudi testcase!")
    # 请求模型
    df = pd.read_excel(args.doudi_excel_file)
    actual_answer_list = []
    tc_llm_list = []
    count = 0
    for _, row in tqdm(df.iterrows(), dynamic_ncols=True, total=len(df)):
        start_time = time.perf_counter()
        actual_answer = do_request_llm(host=args.host, port=args.port, model=args.model_name_request,
                                       prompt=row["final_qa_prompt"], stop_words=args.stop_words)
        tc_ms = int((time.perf_counter() - start_time) * 1000)
        tc_llm_list.append(tc_ms)
        actual_answer_list.append(actual_answer)
        if row["Standard answer"].strip() in actual_answer or row["Standard answer"].strip().replace("，",
                                                                                                     ",") in actual_answer:
            count += 1
    accuracy = float(count) / len(df) * 100
    print(len(df), "doudi accuracy:", accuracy)
    df["Actual answer"] = actual_answer_list
    df["Time used"] = tc_llm_list
    output_excel_file = f'scored_doudi_{accuracy:.3f}_{args.model_id}_' + Path(args.doudi_excel_file).name
    df.to_excel(output_excel_file, index=False)
    return accuracy


def load_dict_list_from_excel_file(filename):
    df = pd.read_excel(filename, sheet_name="Sheet1")
    return df.to_dict(orient='records')


def save_dict_list_to_excel_file(filename, dictlist):
    df = pd.DataFrame(dictlist)
    df.to_excel(filename, index=False)


def save_multi_dict_list_to_excel_file(output_excel_file, dictlist_list):
    with pd.ExcelWriter(output_excel_file) as writer:
        for i, dictlist in enumerate(dictlist_list):
            df = pd.DataFrame(dictlist)
            df.to_excel(writer, sheet_name=f"Sheet{i + 1}", index=False)


REGEX_PATTERN_STR = r"Thought\s*\d*\s*:[\s]*(.*?)[\s]*Action\s*\d*\s*:[\s]*(.*?)[\s]*Action\s*\d*\s*Input\s*\d*\s*:[\s]*(.*)"
REGEX_PATTERN = re.compile(REGEX_PATTERN_STR, re.DOTALL)


def parse_ask_plugin_intent_recognition_v2(output, regex_pattern=REGEX_PATTERN):
    action, action_input, thought = "", "", ""
    matched = regex_pattern.search(output)
    if not matched:
        thought_re = r"Thought\s*\d*\s*:[\s]*(.*?)[\s]*"
        match1 = re.search(thought_re, output, re.DOTALL)
        if match1:
            thought = output[len(match1.group(0)):]
        return thought, action, action_input

    thought = matched.group(1).strip()
    action = matched.group(2).strip()
    action_input = matched.group(3).strip()
    return thought, action, action_input


def parse_ask_plugin_intent_recognition(output):
    action = ""
    match_result = re.findall(r"Action: (.*)\n", output)
    if match_result:
        action = match_result[0]
    return action


def safe_load(json_str: str) -> dict:
    try:
        return json.loads(json_str)
    except Exception as e:
        logger.error(f"json loads error. json_str:{json_str}, error:{e}")
        logger.error(traceback.format_exc().replace("\n", ""))
        return {}


def valid_plugin_name(extra_plugin_name: str) -> str:
    return extra_plugin_name.strip("'")


def is_meaningful_str(s):
    # 用来判断 end_phrase, question, intent
    if isinstance(s, str):
        return len(s.strip()) > 0
    else:
        return False


# 判断两个dict是否相等
def is_dict_equal(d1, d2):
    if not isinstance(d1, dict) or not isinstance(d2, dict):
        return False
    if len(d1) != len(d2):
        return False
    for k, v in d1.items():
        if k not in d2:
            return False
        if d2[k] != v:
            return False
    return True


def autoeval_react_3tuple(d, answer_field="llm_answer", eval_meaning_field="meaning_llm_good",
                          eval_param_field="param_llm_good", eval_ask_field="ask_llm_good",eval_cyclical_param_field="cyclical_param_llm_good"):
    d = deepcopy(d)
    d[eval_meaning_field] = 'mean_maybe-none'  # 默认不统计
    d[eval_param_field] = 'param_maybe-none'  # 默认不统计
    d[eval_ask_field] = 'ask_maybe-none'  # 默认不统计
    d[eval_cyclical_param_field] = "cyclical_param_maybe_none"
    answer = d[answer_field]
    thought, action, action_input = parse_ask_plugin_intent_recognition_v2(answer)
    action = valid_plugin_name(action)
    if not all([thought, action, action_input]):
        logger.error(
            f"thought or action or action_input is empty."
            f" thought:{thought}, action:{action}, action_input:{action_input}"
        )
        return d
    action_input_dict = safe_load(action_input)
    if action_input_dict is None:
        logger.error(f"action_input_dict json loads error. action_input:{action_input}")
        return d
    thought_dict = safe_load(thought)
    if not thought_dict:
        logger.error(f"thought_struct json loads error. thought:{thought}")
        return d
    # 分情况判断
    query_vs_tool = d['query_vs_tool']
    if query_vs_tool == 'greeting':
        if (
                (action == 'end_conversation' and is_meaningful_str(action_input_dict.get('end_phrase', ''))) or
                (action == 'ASK_USER_FOR_INTENT' and is_meaningful_str(action_input_dict.get('question', ''))) or
                (action == 'TOOL_OTHER' and is_meaningful_str(action_input_dict.get('intent', '')))
        ):
            d[eval_meaning_field] = 'mean_maybe-yes'
        else:
            d[eval_meaning_field] = 'mean_maybe-no'
    elif query_vs_tool == 'end_conversation':
        if action == 'end_conversation' and is_meaningful_str(action_input_dict.get('end_phrase', '')):
            d[eval_meaning_field] = 'mean_maybe-yes'
        else:
            d[eval_meaning_field] = 'mean_maybe-no'
    elif query_vs_tool == 'intent_not_hit_tool':
        if action == 'TOOL_OTHER' and is_meaningful_str(action_input_dict.get('intent', '')):
            d[eval_meaning_field] = 'mean_maybe-yes'
        else:
            d[eval_meaning_field] = 'mean_maybe-no'
    elif query_vs_tool == 'unclear_intent':
        if action == 'ASK_USER_FOR_INTENT' and is_meaningful_str(action_input_dict.get('question', '')):
            d[eval_meaning_field] = 'mean_maybe-yes'
        else:
            d[eval_meaning_field] = 'mean_maybe-no'
    elif query_vs_tool == 'full_params':
        gt_action = d['query_tool']  # ground truth action
        gt_known_params = safe_load(d['known_params'])  # ground truth known_params
        known_params = thought_dict.get('known_params', {})
        #   判断意图成功
        if gt_action == thought_dict.get('tool_to_use_for_user', ''):
            d[eval_meaning_field] = 'mean_maybe-yes'
        else:
            d[eval_meaning_field] = 'mean_maybe-no'
        #   判断抽参成功
        if is_dict_equal(gt_known_params, known_params):
            d[eval_param_field] = 'param_maybe-yes'
            if action != gt_action:
                d[eval_ask_field] = 'ask_maybe_no'
        else:
            d[eval_param_field] = 'param_maybe-no'
            #   判断反问成功
            if not set(gt_known_params.keys()).issubset(action_input_dict.keys()):
                if action == 'ASK_USER_FOR_REQUIRED_PARAMS' and is_meaningful_str(
                        action_input_dict.get('question', '')):
                    d[eval_ask_field] = 'ask_maybe-yes'
                else:
                    d[eval_ask_field] = 'ask_maybe-no'
    elif query_vs_tool == 'part_params':
        gt_action = d['query_tool']
        gt_known_params = safe_load(d['known_params'])
        known_params = thought_dict.get('known_params', {})
        #   判断意图成功
        if gt_action == thought_dict.get('tool_to_use_for_user', ''):
            d[eval_meaning_field] = 'mean_maybe-yes'
        else:
            d[eval_meaning_field] = 'mean_maybe-no'
        #   判断反问成功
        if action == 'ASK_USER_FOR_REQUIRED_PARAMS' and is_meaningful_str(action_input_dict.get('question', '')):
            d[eval_ask_field] = 'ask_maybe-yes'
        else:
            d[eval_ask_field] = 'ask_maybe-no'
        #   判断抽参成功
        if is_dict_equal(gt_known_params, known_params):
            d[eval_param_field] = 'param_maybe-yes'
        else:
            d[eval_param_field] = 'param_maybe-no'
    elif query_vs_tool == "cyclical_param":
        gt_known_params = safe_load(d['known_params'])
        known_params = thought_dict.get('known_params', {})
        if gt_known_params == known_params:
            d[eval_cyclical_param_field] = "cyclical_param_maybe_yes"
        else:
            d[eval_cyclical_param_field] = "cyclical_param_maybe_no"
            if action == 'ASK_USER_FOR_REQUIRED_PARAMS' and is_meaningful_str(action_input_dict.get('question', '')):
                d[eval_ask_field] = 'ask_maybe-yes'
            else:
                d[eval_ask_field] = 'ask_maybe-no'
    else:
        raise ValueError(f"unknown query_vs_tool:{query_vs_tool}")
    return d


def autoeval_react_answer(dictlist, answer_field="llm_answer", eval_meaning_field="meaning_llm_good",
                          eval_param_field="param_llm_good", eval_ask_field="ask_llm_good",eval_cyclical_param_field="cyclical_param_llm_good"):
    dictlist2 = []
    for d in dictlist:
        d = autoeval_react_3tuple(d, answer_field=answer_field, eval_meaning_field=eval_meaning_field,
                                  eval_param_field=eval_param_field, eval_ask_field=eval_ask_field,eval_cyclical_param_field=eval_cyclical_param_field)
        dictlist2.append(d)
    return dictlist2


def stat_react_autoeval_result(dictlist, eval_meaning_field="meaning_llm_good", eval_param_field="param_llm_good",
                               eval_ask_field="ask_llm_good",eval_cyclical_param_field="cyclical_param_llm_good"):
    query_vs_tool_count_map = {}
    for d in dictlist:
        query_vs_tool = d['query_vs_tool']
        meaning = d[eval_meaning_field]
        ask = d[eval_ask_field]
        param = d[eval_param_field]
        cyclical_param = d[eval_cyclical_param_field]
        if query_vs_tool not in query_vs_tool_count_map:
            query_vs_tool_count_map[query_vs_tool] = {'mean_maybe-yes': 0, 'mean_maybe-no': 0, 'ask_maybe-yes': 0,
                                                      'ask_maybe-no': 0, 'param_maybe-yes': 0, 'param_maybe-no': 0,
                                                      'cyclical_param_maybe_yes':0,'cyclical_param_maybe_no':0}
        try:
            query_vs_tool_count_map[query_vs_tool][meaning] += 1
        except KeyError:
            logger.error(f"meaning:{meaning} not in query_vs_tool_count_map[query_vs_tool]")
        try:
            query_vs_tool_count_map[query_vs_tool][ask] += 1
        except KeyError:
            logger.error(f"ask:{ask} not in query_vs_tool_count_map[query_vs_tool]")
        try:
            query_vs_tool_count_map[query_vs_tool][param] += 1
        except KeyError:
            logger.error(f"param:{param} not in query_vs_tool_count_map[query_vs_tool]")
        try:
            query_vs_tool_count_map[query_vs_tool][cyclical_param] += 1
        except KeyError:
            logger.error(f"cyclical_param:{cyclical_param} not in query_vs_tool_count_map[query_vs_tool]")
    mean_final_maybe_yes = 0
    mean_final_maybe_no = 0
    ask_final_maybe_yes = 0
    ask_final_maybe_no = 0
    param_final_maybe_yes = 0
    param_final_maybe_no = 0
    cyclical_param_final_maybe_yes = 0
    cyclical_param_final_maybe_no = 0
    result = []
    for query_vs_tool, count in query_vs_tool_count_map.items():
        mean_maybe_yes = count['mean_maybe-yes']
        mean_maybe_no = count['mean_maybe-no']
        ask_maybe_yes = count['ask_maybe-yes']
        ask_maybe_no = count['ask_maybe-no']
        param_maybe_yes = count['param_maybe-yes']
        param_maybe_no = count['param_maybe-no']
        cyclical_param_maybe_yes = count['cyclical_param_maybe_yes']
        cyclical_param_maybe_no = count['cyclical_param_maybe_no']
        mean_final_maybe_yes += mean_maybe_yes
        mean_final_maybe_no += mean_maybe_no
        ask_final_maybe_yes += ask_maybe_yes
        ask_final_maybe_no += ask_maybe_no
        param_final_maybe_yes += param_maybe_yes
        param_final_maybe_no += param_maybe_no
        cyclical_param_final_maybe_yes += cyclical_param_maybe_yes
        cyclical_param_final_maybe_no += cyclical_param_maybe_no
        mean_total = mean_maybe_no + mean_maybe_yes
        ask_total = ask_maybe_no + ask_maybe_yes
        param_total = param_maybe_no + param_maybe_yes
        cyclical_param_total = cyclical_param_maybe_no + cyclical_param_maybe_yes
        mean_maybe_yes_ratio = mean_maybe_yes / mean_total * 100 if mean_total > 0 else 0
        mean_maybe_no_ratio = mean_maybe_no / mean_total * 100 if mean_total > 0 else 0
        ask_maybe_yes_ratio = ask_maybe_yes / ask_total * 100 if ask_total > 0 else 0
        ask_maybe_no_ratio = ask_maybe_no / ask_total * 100 if ask_total > 0 else 0
        param_maybe_yes_ratio = param_maybe_yes / param_total * 100 if param_total > 0 else 0
        param_maybe_no_ratio = param_maybe_no / param_total * 100 if param_total > 0 else 0
        cyclical_param_maybe_yes_ratio = cyclical_param_maybe_yes / cyclical_param_total * 100 if cyclical_param_total > 0 else 0
        cyclical_param_maybe_no_ratio = cyclical_param_maybe_no / cyclical_param_total * 100 if cyclical_param_total > 0 else 0

        result.append({'query_vs_tool': query_vs_tool, 'mean_total': mean_total, 'mean_maybe_yes': mean_maybe_yes,
                       'mean_maybe_no': mean_maybe_no, 'mean_maybe_yes_ratio': mean_maybe_yes_ratio,
                       'ask_total': ask_total, 'ask_maybe_yes': ask_maybe_yes, 'ask_maybe_no': ask_maybe_no,
                       'ask_maybe_yes_ratio': ask_maybe_yes_ratio,
                       'param_total': param_total, 'param_maybe_yes': param_maybe_yes, 'param_maybe_no': param_maybe_no,
                       'param_maybe_yes_ratio': param_maybe_yes_ratio,'cyclical_param_total':cyclical_param_total,'cyclical_param_maybe_yes':cyclical_param_maybe_yes,
                       'cyclical_param_maybe_no':cyclical_param_maybe_no,'cyclical_param_maybe_yes_ratio':cyclical_param_maybe_yes_ratio,})

    # final_total = mean_final_maybe_no + mean_final_maybe_yes + ask_final_maybe_no + ask_final_maybe_yes + param_final_maybe_no + param_final_maybe_yes+cyclical_param_final_maybe_no+cyclical_param_final_maybe_yes
    # final_maybe_yes_toal = mean_final_maybe_yes + ask_final_maybe_yes + param_final_maybe_yes
    # final_maybe_no_total = mean_final_maybe_no + ask_final_maybe_no + param_final_maybe_no
    # final_maybe_yes_ratio = final_maybe_yes_toal / final_total * 100 if final_total > 0 else 0
    # final_maybe_no_ratio = final_maybe_no_total / final_total * 100 if final_total > 0 else 0
    final_mean_total = mean_final_maybe_no + mean_final_maybe_yes
    final_ask_total = ask_final_maybe_no + ask_final_maybe_yes
    final_param_total = param_final_maybe_no + param_final_maybe_yes
    final_cyclical_param_total = cyclical_param_final_maybe_no + cyclical_param_final_maybe_yes
    final_mean_yes_ratio = mean_final_maybe_yes / final_mean_total * 100 if final_mean_total > 0 else 0
    final_ask_yes_ratio = ask_final_maybe_yes / final_ask_total * 100 if final_ask_total > 0 else 0
    final_param_yes_ratio = param_final_maybe_yes / final_param_total * 100 if final_param_total > 0 else 0
    final_cyclical_param_yes_ratio = cyclical_param_final_maybe_yes / final_cyclical_param_total * 100 if final_cyclical_param_total > 0 else 0
    result.append({'query_vs_tool': '总计',
                   'mean_total': final_mean_total, 'mean_maybe_yes': mean_final_maybe_yes,'mean_maybe_no': mean_final_maybe_no,'mean_maybe_yes_ratio': final_mean_yes_ratio,
                   'ask_total': final_ask_total,'ask_maybe_yes': ask_final_maybe_yes,'ask_maybe_no': ask_final_maybe_no, 'ask_maybe_yes_ratio': final_ask_yes_ratio,
                   'param_total': final_param_total, 'param_maybe_yes': param_final_maybe_yes,'param_maybe_no': param_final_maybe_no,'param_maybe_yes_ratio': final_param_yes_ratio,
                   'cyclical_param_total':final_cyclical_param_total,'cyclical_param_maybe_yes':cyclical_param_final_maybe_yes,'cyclical_param_maybe_no':cyclical_param_final_maybe_no,'cyclical_param_maybe_yes_ratio':final_cyclical_param_yes_ratio})
    return result


def chatmax_react_eval_model_request(args):
    react_excel_file = args.react_excel_file
    host = args.host
    port = args.port
    model_name_request = args.model_name_request
    model_id = args.model_id
    stop_words = args.stop_words  # comma separated stop words
    do_request_gpt4_if_needed = args.do_request_gpt4_if_needed
    # do_request_gpt4_if_needed = True
    dictlist = load_dict_list_from_excel_file(react_excel_file)
    for d in tqdm(dictlist, dynamic_ncols=True, total=len(dictlist)):
        prompt = d['prompt']
        d['llm_answer'] = do_request_llm(
            host=host,
            port=port,
            model=model_name_request,
            prompt=prompt,
            stop_words=stop_words
        )
        d['is_llm_good'] = 'unknown'
        if do_request_gpt4_if_needed and not is_meaningful_str(d['gpt4_answer']):
            d['gpt4_answer'] = chat_with_gpt(prompt)
            d['is_gpt4_good'] = 'unknown'
    dictlist = autoeval_react_answer(dictlist, answer_field="llm_answer", eval_meaning_field="meaning_llm_good",
                                     eval_param_field="param_llm_good", eval_ask_field="ask_llm_good",eval_cyclical_param_field="cyclical_param_llm_good")
    result = stat_react_autoeval_result(dictlist, eval_meaning_field="meaning_llm_good",
                                        eval_param_field="param_llm_good", eval_ask_field="ask_llm_good",eval_cyclical_param_field="cyclical_param_llm_good")
    df = pd.DataFrame(result)
    logger.info("react autoeval result:")
    pd.set_option('display.max_colwidth', 1000)
    print(df, flush=True)
    score = (result[-1]['mean_total'], result[-1]['ask_total'], result[-1]['param_total'])
    output_excel_file = f'scored_react_{model_id}_' + Path(react_excel_file).name
    if not os.path.exists(PLUGIN_OUTPUT_PATH):
        os.makedirs(PLUGIN_OUTPUT_PATH)
    save_multi_dict_list_to_excel_file(f"{PLUGIN_OUTPUT_PATH}{os.sep}{output_excel_file}", [dictlist, result])
    logger.info(f"save result to {PLUGIN_OUTPUT_PATH}{os.sep}{output_excel_file}, output_excel_file for react: {output_excel_file}")
    return score


if __name__ == '__main__':
    args = get_args()
    logger.info(f"{args}")
    if args.model_id is None:
        timestr = time.strftime("%Y%m%d-%H%M%S", time.localtime())
        args.model_id = f"{args.host}_{args.port}_{args.model_name_request}_{timestr}"

    results = []
    # if not args.skip_qa:
    #     start_time = time.perf_counter()
    #     score = chatmax_qa_eval_model_request(args)
    #     results.append({"task": "qa", "score": score, "seconds": time.perf_counter() - start_time})
    # if not args.skip_doudi:
    #     start_time = time.perf_counter()
    #     score = chatmax_doudi_eval_model_request(args)
    #     results.append({"task": "doudi", "score": score, "seconds": time.perf_counter() - start_time})
    if not args.skip_react:
        start_time = time.perf_counter()
        score = chatmax_react_eval_model_request(args)
        results.append({"task": "react", "score": score, "seconds": time.perf_counter() - start_time})
    results_str = json.dumps(results, ensure_ascii=False)
    logger.info(f"EVAL_RESULT: {results_str}")
