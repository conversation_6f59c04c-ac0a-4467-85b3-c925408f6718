# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : jenkins_task.py
<AUTHOR> <EMAIL>
@Time   : 2024-06-18 13:05:13
@Version: v1.0
"""
import requests
from services.jenkins_task.jenkins_instance import <PERSON><PERSON><PERSON><PERSON>


def build_by_gpt(version="人工执行"):
    url = "http://10.100.10.111:12345/job/by_gpt/buildWithParameters?"
    payload = {
        "token": 123456,
        "version": version
    }
    headers = {
        "Authorization": "Basic cm9vdDoxMTM1NDAzOGY0MjllMDZkNDhmYmY1ZGFjMmI2NDkyMjg5"
    }
    res = requests.post(url=url, params=payload, headers=headers)
    print(res.status_code)
    if res.status_code == 201:
        return True, "success"
    else:
        return False, res.status_code


def build_job(job_name, param):
    j = My<PERSON><PERSON><PERSON>()
    j.stop_job_for_name(job_name=job_name)
    rum_number = j.start_job_for_name(job_name=job_name, param=param, token="123456")
    if rum_number:
        return True, rum_number
    else:
        return False, "失败"
