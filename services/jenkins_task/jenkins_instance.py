# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : jenkins_instance.py
<AUTHOR> <EMAIL>
@Time   : 2024-06-24 11:54:38
@Version: v1.0
"""
from jenkins import Jenkins


class MyJenkins(object):

    def __init__(self):
        self.instance = MyJenkins.connection()

    @staticmethod
    def connection():
        # 创建 Jenkins实例的 handle
        jenkins_url = "http://10.100.10.111:12345/"
        jen = <PERSON>(url=jenkins_url, username="root", password="root")
        return jen

    def get_job_count(self):
        return self.instance.jobs_count()

    def get_job_info(self, job_name):
        return self.instance.get_job_info(job_name)

    def stop_job_for_name(self, job_name):
        info = self.instance.get_running_builds()
        print(info)
        if info:
            run_number = [x["number"] for x in info if x.get("name") == job_name]
            if run_number:
                self.instance.stop_build(name=job_name, number=run_number[0])
        return info

    def start_job_for_name(self, job_name, token=None, param=None):
        if param:
            return self.instance.build_job(name=job_name, parameters=param, token=token)
        else:
            return self.instance.build_job(name=job_name, token=token)

#
# if __name__ == '__main__':
#     j = MyJenkins()
#     payload = {
#         "version": "人工"
#     }
#
#     j.start_job_for_name(job_name="by_gpt",param=payload,token="123456")
#     infos = j.get_job_info("by_gpt")
#     print(infos)
#     j.stop_job_for_name("by_gpt")
