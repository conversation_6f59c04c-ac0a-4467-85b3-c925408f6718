import pandas as pd
import requests
# import uvicorn
import time
# from fastapi import FastAPI
# from pydantic import BaseModel
from llm_test_prompt import *
# app = FastAPI()


def ask_qianwen_chat(content: str, history) -> str:
    message = [{
        "role": "system",
        "content": "你是小豹，是猎户星空开发的人工智能"
    },
        {
            "role": "user",
            "content": content
    }]
    message.extend(history)
    url = 'http://10.100.10.108:8007/v1/chat/completions'
    headers = {
        'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
        'Content-Type': 'application/json'
    }
    data = {
        "messages": message,
        "temperature": 0.4,
        "stream": False,
        "model": "qwen2.5-32B-awq"
    }
    start_time = time.time()
    response = requests.post(url, headers=headers, json=data)
    end_time = round(time.time() - start_time, 3)
    info = response.json()
    token = info["usage"]["completion_tokens"]
    count_time = round(token / end_time, 3)
    return info['choices'][0]['message']['content'], count_time


def ask_doubao_lite(content, history):
    message = [{
        "role": "system",
        "content": "你是小豹，是猎户星空开发的人工智能"
    },
        {
            "role": "user",
            "content": content
    }]
    message.extend(history)
    # 请求url
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    # 请求头
    headers = {
        "Authorization": "Bearer " + "87651b4d-eae5-4ee4-81ae-38e98616620a",
        "Content-Type": "application/json"
    }
    # 请求体
    data = {
        "model": "ep-20241115154755-w2qwx",  # 普通版本
        # "model": "ep-20241121143012-wzp7g",  # 角色版本
        "messages": message,
        "temperature": 0.4,
        "stream": False,
    }
    # todo 需要添加一个参数，来判断是普通版本还是角色版本
    start_time = time.time()
    response = requests.post(url, headers=headers, json=data)
    end_time = round(time.time() - start_time, 3)
    info = response.json()
    token = info["usage"]["completion_tokens"]
    count_time = round(token / end_time, 3)
    return info['choices'][0]['message']['content'], count_time


def ask_doubao_lite_role(content, history):
    message = [{
        "role": "system",
        "content": "你是小豹，是猎户星空开发的人工智能"
    },
        {
            "role": "user",
            "content": content
    }]
    message.extend(history)
    # 请求url
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    # 请求头
    headers = {
        "Authorization": "Bearer " + "87651b4d-eae5-4ee4-81ae-38e98616620a",
        "Content-Type": "application/json"
    }
    # 请求体
    data = {
        # "model": "ep-20241115154755-w2qwx",  # 普通版本
        "model": "ep-20241121143012-wzp7g",  # 角色版本
        "messages": message,
        "temperature": 0.4,
        "stream": False,
    }

    start_time = time.time()
    response = requests.post(url, headers=headers, json=data)
    end_time = round(time.time() - start_time, 3)
    info = response.json()
    token = info["usage"]["completion_tokens"]
    count_time = round(token / end_time, 3)
    return info['choices'][0]['message']['content'], count_time


def ask_doubao_pro(content, history):
    message = [{
        "role": "system",
        "content": "你是小豹，是猎户星空开发的人工智能"
    },
        {
            "role": "user",
            "content": content
    }]
    message.extend(history)
    # 请求url
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    # 请求头
    headers = {
        "Authorization": "Bearer " + "87651b4d-eae5-4ee4-81ae-38e98616620a",
        "Content-Type": "application/json"
    }
    # 请求体
    data = {
        "model": "ep-20241115154547-kqrrf",  # 普通版本
        # "model": "ep-20241121142856-2mbkd", # 角色版本
        "messages": message,
        "temperature": 0.4,
        "stream": False,
    }

    start_time = time.time()
    response = requests.post(url, headers=headers, json=data)
    end_time = round(time.time() - start_time, 3)
    info = response.json()
    token = info["usage"]["completion_tokens"]
    count_time = round(token / end_time, 3)
    return info['choices'][0]['message']['content'], count_time


def ask_doubao_pro_role(content, history):
    message = [{
        "role": "system",
        "content": "你是小豹，是猎户星空开发的人工智能"
    },
        {
            "role": "user",
            "content": content
    }]
    message.extend(history)
    # 请求url
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    # 请求头
    headers = {
        "Authorization": "Bearer " + "87651b4d-eae5-4ee4-81ae-38e98616620a",
        "Content-Type": "application/json"
    }
    # 请求体
    data = {
        # "model": "ep-20241115154547-kqrrf", # 普通版本
        "model": "ep-20241121142856-2mbkd",  # 角色版本
        "messages": message,
        "temperature": 0.4,
        "stream": False,
    }

    start_time = time.time()
    response = requests.post(url, headers=headers, json=data)
    end_time = round(time.time() - start_time, 3)
    info = response.json()
    token = info["usage"]["completion_tokens"]
    count_time = round(token / end_time, 3)
    return info['choices'][0]['message']['content'], count_time


def request_model(content):
    #   pro版本
    message = [{
        "role": "system",
        "content": "你是小豹，是猎户星空开发的人工智能"
    },
        {
            "role": "user",
            "content": content
    }]
    # 请求url
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    # 请求头
    headers = {
        "Authorization": "Bearer " + "87651b4d-eae5-4ee4-81ae-38e98616620a",
        "Content-Type": "application/json"
    }
    # 请求体
    data = {
        "model": "ep-20241115154547-kqrrf",  # 普通版本
        # "model": "ep-20241121142856-2mbkd",  # 角色版本
        "messages": message,
        "temperature": 0.4,
        "stream": False,
    }

    start_time = time.time()
    response = requests.post(url, headers=headers, json=data)
    end_time = round(time.time() - start_time, 3)
    info = response.json()
    token = info["usage"]["completion_tokens"]
    count_time = round(token / end_time, 3)
    return info['choices'][0]['message']['content'], count_time


def get_prompt(model_name):
    prompt_dict = {
        'doubao_lite_user': douhao_lite_user_prompt,
        'doubao_pro_user': douhao_pro_user_prompt,
        'doubao_lite_user_role': douhao_lite_user_role_prompt,
        'doubao_pro_user_role': douhao_pro_user_role_prompt,
        'doubao_lite_ai': douhao_lite_ai_prompt,
        'doubao_pro_ai': douhao_pro_ai_prompt,
        'doubao_lite_ai_role': douhao_lite_ai_role_prompt,
        'doubao_pro_ai_role': douhao_pro_ai_role_prompt,
        'doubao_pro_score': douhao_pro_score_prompt,
        'stop_prompt': stop_prompt
    }
    return prompt_dict[model_name]


def history_str(data, history):
    if not history or pd.isna(history):  # Check for both empty and nan values
        return []
    res = []
    history_list = history.split("\n")
    pm_type = data.get("userRole", "")
    if not pm_type:
        pm_type = data.get("aiRole", "")
    for history in history_list:
        if history.startswith(f"[{pm_type}]"):
            res.append({
                "role": "assistant",
                "content": history
            })
        else:
            res.append({
                "role": "user",
                "content": history
            })
    return res


def chat(data_list, model_name):
    res_list = []
    for i, data in enumerate(data_list):
        # if i != 101:
        #     continue
        #   偶数不测试
        # if i % 2 == 0:
        #     continue
        res = ""
        times = 0
        prompt = get_prompt(model_name)
        pm = prompt.format(**data)
        history = history_str(data, data.get("chatHistories", ""))
        if model_name in ['doubao_lite_user', 'doubao_lite_ai']:
            res, times = ask_doubao_lite(pm, history)
        elif model_name in ['doubao_pro_user', 'doubao_pro_ai']:
            res, times = ask_doubao_pro(pm, history)
        elif model_name in ['doubao_lite_user_role', 'doubao_lite_ai_role']:
            res, times = ask_doubao_lite_role(pm, history)
        elif model_name in ['doubao_pro_user_role', 'doubao_pro_ai_role']:
            res, times = ask_doubao_pro_role(pm, history)
        elif model_name in ['qianwen_user', 'qianwen_ai']:
            res, times = ask_qianwen_chat(pm, history)
        prompt_with_history = pm + "\n###对话历史：\n"
        if not pd.isna(data.get("chatHistories", "")) and data.get("chatHistories", ""):
            prompt_with_history += data.get("chatHistories", "")

        if data.get('aiRole', ""):
            res_list.append({
                "aiRole": data.get("aiRole", ""),
                "backgroundAI": data.get("backgroundAI", ""),
                "aiStagingInfo": data.get("aiStagingInfo", ""),
                "chatHistories": data.get("chatHistories", ""),
                "prompt": prompt_with_history,
                "output": res,
                "time(token/s)": times
            })
        else:
            res_list.append({
                "userRole": data.get("userRole", ""),
                "backgroundUser": data.get("backgroundUser", ""),
                "userStagingInfo": data.get("userStagingInfo", ""),
                "scoreStandard": data.get("scoreStandard", ""),
                "chatHistories": data.get("chatHistories", ""),
                "prompt": prompt_with_history,
                "output": res,
                "time(token/s)": times
            })
        if i != 0 and i % 10 == 0:
            time.sleep(10)
        print(res)
    return res_list


def score(data_list):
    res_list = []
    for i, data in enumerate(data_list):
        # if i > 10:
        #     break
        res = ""
        times = 0
        prompt = get_prompt("doubao_pro_score")
        pm = prompt.format(**data)
        # 请求模型
        res, times = request_model(pm)

        res_list.append({
            "aiRole": data.get("aiRole", ""),
            "backgroundAI": data.get("backgroundAI", ""),
            "userRole": data.get("userRole", ""),
            "backgroundUser": data.get("backgroundUser", ""),
            "chatHistories": data.get("chatHistories", ""),
            "scoreStandard": data.get("scoreStandard", ""),
            "prompt": pm,
            "output": res,
            "预期结果": data.get("预期结果", ""),
            "time(token/s)": times
        })

        if i != 0 and i % 10 == 0:
            time.sleep(10)
        print(res)
    return res_list


def stop(data_list):
    #   判停
    res_list = []
    for i, data in enumerate(data_list):
        # if i not in (15, 21, 40, 43, 44, 45, 46):
        #     continue
        res = ""
        times = 0
        prompt = get_prompt("stop_prompt")
        pm = prompt.format(**data)
        # 请求模型
        res, times = request_model(pm)
        res_list.append({
            "endCondition": data.get("endCondition", ""),
            "chatHistories": data.get("chatHistories", ""),
            "prompt": pm,
            "output": res,
            "预期结果": data.get("预期结果", ""),
            "time(token/s)": times
        })
        if i != 0 and i % 10 == 0:
            time.sleep(10)
        print(res)
    return res_list


def get_res_by_ai():
    file_path = '评测集V4.xlsx'
    df = pd.read_excel(file_path, engine='openpyxl', sheet_name='aiStartPrompt')
    data_list = df.to_dict(orient='records')
    models_list = [
        'doubao_lite_ai',
        'doubao_pro_ai',
        'doubao_lite_ai_role',
        'doubao_pro_ai_role',
        # 'qianwen_ai'
    ]
    for model in models_list:
        result = chat(data_list, model)
        df = pd.DataFrame(result, columns=['aiRole', 'backgroundAI', 'aiStagingInfo', 'chatHistories', 'prompt',
                                           'output', 'time(token/s)'])
        df.to_excel(f'{time.strftime("%Y-%m-%d-%H-%M-%S")}_{model}_output.xlsx', index=False)


def get_res_by_user():
    file_path = '评测集V4.xlsx'
    df = pd.read_excel(file_path, engine='openpyxl', sheet_name='userStartPrompt')
    data_list = df.to_dict(orient='records')
    models_list = [
        'doubao_lite_user',
        'doubao_pro_user',
        'doubao_lite_user_role',
        'doubao_pro_user_role',
        # 'qianwen_user',
    ]
    for model in models_list:
        result = chat(data_list, model)
        df = pd.DataFrame(result, columns=['userRole', 'backgroundUser', 'userStagingInfo', 'scoreStandard',
                                           'chatHistories', 'prompt', 'output', 'time(token/s)'])
        # df.to_excel(f'{model}_output.xlsx', index=False)
        df.to_excel(f'{time.strftime("%Y-%m-%d-%H-%M-%S")}_{model}_output.xlsx', index=False)


def get_res_score():
    file_path = '评测集V4.xlsx'
    df = pd.read_excel(file_path, engine='openpyxl', sheet_name='getScorePrompt')
    data_list = df.to_dict(orient='records')
    models_list = [
        # 'doubao_lite_user',
        # 'doubao_pro_user',
        # 'doubao_lite_user_role',
        # 'doubao_pro_user_role',
        # 'qianwen_user',
    ]
    for model in models_list:
        result = score(data_list)
        df = pd.DataFrame(result, columns=['aiRole', 'backgroundAI', 'userRole', 'backgroundUser',
                                           'chatHistories', 'scoreStandard', 'prompt', 'output', '预期结果', 'time(token/s)'])
        df.to_excel(f'{model}_output.xlsx', index=False)


def stop_res():
    file_path = '评测集V3.xlsx'
    df = pd.read_excel(file_path, engine='openpyxl', sheet_name='stopPrompt')
    data_list = df.to_dict(orient='records')
    models_list = [
        # 'doubao_lite_user',
        'doubao_pro_user',
        # 'doubao_lite_user_role',
        # 'doubao_pro_user_role',
        # 'qianwen_user',
    ]
    for model in models_list:
        result = stop(data_list)
        df = pd.DataFrame(result, columns=['endCondition', 'chatHistories', 'prompt', 'output', '预期结果', 'time(token/s)'])
        df.to_excel(f'{model}_{time.strftime("%Y-%m-%d-%H-%M-%S")}_stop_output.xlsx', index=False)


if __name__ == '__main__':
    get_res_by_user()
    time.sleep(60)
    get_res_by_ai()
    # uvicorn.run(app, host="
    # get_res_score()
    # stop_res()
