# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : load_test.py
<AUTHOR> <EMAIL>
@Time   : 2023-12-06 10:29:16
@Version: v1.0
"""
from auto_speak import run
import threading
from dbutils.pooled_db import PooledDB
import pymysql
import copy
import traceback
from concurrent.futures.thread import ThreadPoolExecutor
import uuid
import pandas as pd
from datetime import datetime
import concurrent.futures
import time
import json
import requests
import os
import sys
import argparse

BASE_DIR = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
print(BASE_DIR)
sys.path.append(BASE_DIR)

lock = threading.Lock()
send_url = "https://open.feishu.cn/open-apis/bot/v2/hook/bf9c14c9-5d6c-4094-9547-a957dabbcefb"

base_url = "https://test-aiteaching-api.cmcm.com"
#   失效后更新token
token = "Bearer Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo2MiwidHlwZSI6InVzZXIiLCJleHAiOjE3NDA0NTE2ODB9.AntE0dot6J1xnXMLefOSERNO3swgREnE0k-E4iHgKz0"
headers = {
    "Authorization": token
}
#   2025-02-21 号优化前

def get_exe(max_works=1):
    exe = ThreadPoolExecutor(max_workers=max_works)
    return exe


def get_now(format=None):
    if format:
        return datetime.now().strftime("%Y%m%d")
    else:
        return datetime.now().strftime("%Y%m%d%H%M%S")


def send_stream(uuid):
    """
    :return:
    """
    url = f"{base_url}/v1/api/teach_plus/get_ai_answer_stream?uuid={uuid}"
    # 记录请求发送的时间（精确到毫秒）
    request_send_time = time.time()
    response = requests.get(url, headers=headers, stream=True)
    first_packet_time = None
    total_character_count = 0
    answer = ""
    intent_list = []
    # 处理响应数据
    # error = 0
    msg = ""
    for line in response.iter_lines():
        if not first_packet_time:
            # 记录第一个数据包返回的时间（精确到毫秒）
            first_packet_time = time.time()
        # 处理响应数据
        try:
            if line.startswith(b"data: "):
                json_data = line[6:]
                try:
                    data_dict = json.loads(json_data)
                    if data_dict.get("is_end"):
                        answer = data_dict.get("message")
                        break
                except json.JSONDecodeError:
                    print("json loads error:->{}".format(line))
                    continue

        except json.JSONDecodeError:
            print("json loads error:->{}".format(line))
            continue
    # 等待整个请求完全返回
    response.close()
    # 记录请求完成的时间（精确到毫秒）
    request_complete_time = time.time()
    # 转换为易读的时分秒格式（精确到毫秒）并保留毫秒的前3位
    request_send_time_str = datetime.fromtimestamp(request_send_time).strftime(
        '%Y-%m-%d %H:%M:%S.%f')[:23]

    first_packet_time_str = datetime.fromtimestamp(first_packet_time).strftime(
        '%Y-%m-%d %H:%M:%S.%f')[:23]
    request_complete_time_str = datetime.fromtimestamp(
        request_complete_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    if first_packet_time_str:
        first_packet_duration = (first_packet_time - request_send_time)
        tokens_time = (request_complete_time - first_packet_time)
        performance_metric = len(answer) / tokens_time

    # 计算并打印首包耗时和总耗时
    first_packet_duration = (first_packet_time - request_send_time)

    total_duration = request_complete_time - request_send_time
    tokens_time = (request_complete_time - first_packet_time)

    # print("性能指标: {:.3f} 字/秒".format(performance_metric))
    # print("=" * 50)

    result = {
        "请求发送的时间": request_send_time_str,
        "第一个数据包返回的时间": first_packet_time_str,
        "请求完成的时间": request_complete_time_str,
        "首包耗时(s)": first_packet_duration,
        "总耗时(s)": total_duration,
        # "输入": len(qa_prompt),
        "输出字数": len(answer),
        "性能指标(字/秒)": performance_metric,
        "answer": answer,
        # "request_id": data["request_id"],
        "msg": msg,
        "progress": json.dumps(intent_list, ensure_ascii=False),
        "session_id": uuid
    }
    return result


def save_print_to_excel(thread, loop, run_number, uuid):
    results = []
    output_path = "{T}{sep}{run_number}{sep}{service}".format(
        sep=os.sep,
        T=get_now("day"),
        service="ai_answer",
        run_number=run_number
    )
    output_file = f"{thread}-{loop}-{get_now()}-ai_answer.xlsx"

    excel_path = f"{output_path}{os.sep}{output_file}"
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    with concurrent.futures.ThreadPoolExecutor(max_workers=thread) as executor:
        futures = []
        index = 0

        for index in range(loop):
            futures.append(executor.submit(send_stream, uuid))
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as exc:
                print(f"Error: {exc}\n{traceback.format_exc()}")
    df = pd.DataFrame(results)
    df.to_excel(excel_path, index=False)
    print(f"save result file to: {output_path}/{output_file}")


def get_all_files_in_dir(file_path: str) -> list:
    """
    递归获取指定文件夹及其子文件夹下的所有文件，不包含隐藏文件
    :param file_path: 文件夹路径
    :return: 文件名列表
    """
    filename_list = []
    mixed_file_list = []
    for root, dirs, files in os.walk(file_path):
        for file_name in files:
            if not file_name.startswith("."):
                if root.__contains__("mixed"):
                    mixed_file_list.append(os.path.join(root, file_name))
                elif not file_name.__contains__("all"):
                    filename_list.append(os.path.join(root, file_name))
    return filename_list, mixed_file_list


def get_info_file(file_name):
    """
    根据文件名获取文件信息
    """
    info = file_name.split("/")[-1].split("-")
    info[3] = info[3].split(".")[0]
    return info


def calc_data(file_path):

    if os.path.exists(file_path):
        #    遍历获取文件夹下所有文件路径
        single_files, mixed_files = get_all_files_in_dir(file_path)
    else:
        raise FileNotFoundError(f'{file_path} does not exist')
    # 读取 Excel 文件并创建 DataFrame
    # 先处理单场景文件
    single_res_list = []
    for sf in single_files:
        df = pd.read_excel(sf)
        res = calc_df(df)
        info = get_info_file(sf)
        api_info = "/v1/api/teach_plus/get_ai_answer_stream"
        add_data = {
            "接口": api_info,
            "since": info[3],
            "并发数": info[0],
            "执行次数": info[1],
        }
        res.update(add_data)
        single_res_list.append(res)
    if single_res_list:
        df_single = pd.DataFrame(single_res_list)
        # 将单场景数据写入 Excel
        df_single.to_excel(f"{file_path}/single_all_result.xlsx", index=False)
        keep_cols = ["since", "并发数", "执行次数", "首包90Line", "首包95line",
                     "总耗时90line", "总耗时95line"]
        df = df_single[keep_cols]
        pd.set_option('display.max_columns', None)  # 显示所有列
        print(f"单场景压测结果:\n{df}\n")


def calc_df(df):
    try:
        # 将字符串时间列转换为 datetime 类型
        start = pd.to_datetime(df["请求发送的时间"].min())
        end = pd.to_datetime(df["请求完成的时间"].max())
        cost = round((end - start).total_seconds(), 1)
        #   获取执行开始时间
        begin_time = df["请求发送的时间"].min()
        #   获取执行结束时间
        end_time = df["请求完成的时间"].max()
        #   计算执行总耗时
        # all_cost=end_time-begin_time
        # print(f"start:{start},end:{end},cost:{cost}\nbegin_time:{begin_time},end_time:{end_time}")
        # 首包耗时
        # first_cost = round(df["首包耗时(s)"].mean(), 2)
        #   有效首包耗时
        # real_first_cost = round(df["有效数据_首包耗时(s)"].mean(), 2)

        #   有效首包耗时最大值
        real_first_max = round(df["首包耗时(s)"].max(), 2)

        #   有效首包耗时最小值
        # real_first_min = round(df["有效数据_首包耗时(s)"].min(), 2)
        #   去除为0的数据
        real_first_min = round(
            df.loc[df["首包耗时(s)"] != 0, "首包耗时(s)"].min(), 2)

        #   有效首包耗时中位数
        real_first_median = round(df["首包耗时(s)"].median(), 2)

        #   总耗时平均值
        # total_cost = round(df["总耗时(s)"].mean(), 2)

        #   总耗时最大值
        total_cost_max = round(df["总耗时(s)"].max(), 2)

        #   总耗时最小值
        total_cost_min = round(df.loc[df["总耗时(s)"] != 0, "总耗时(s)"].min(), 2)

        #   总耗时中位数
        total_cost_median = round(df["总耗时(s)"].median(), 2)
        #   性能指标字/秒
        speed_word = round(df["性能指标(字/秒)"].mean(), 2)
        #   有效数据性能指标字/秒
        # real_speed_word = round(df["有效数字_性能指标(字/秒)"].mean(), 2)
        #   首包耗时计算
        orion_first_cost = sorted(df["首包耗时(s)"].to_list())
        first_line_90 = int(len(orion_first_cost) * 0.90)
        first_line_95 = int(len(orion_first_cost) * 0.95)
        first_line_99 = int(len(orion_first_cost) * 0.99)

        first_cost_90 = round(orion_first_cost[first_line_90], 2)
        first_cost_99 = round(orion_first_cost[first_line_99], 2)
        first_cost_95 = round(orion_first_cost[first_line_95], 2)
        #   总耗时计算
        orion_total_cost = sorted(df["总耗时(s)"].to_list())
        total_line_90 = int(len(orion_total_cost) * 0.90)
        total_line_95 = int(len(orion_total_cost) * 0.95)
        total_line_99 = int(len(orion_total_cost) * 0.99)
        total_cost_90 = round(orion_total_cost[total_line_90], 2)
        total_cost_99 = round(orion_total_cost[total_line_99], 2)
        total_cost_95 = round(orion_total_cost[total_line_95], 2)

        average_row_to_all = {
            "接口": "",
            "since": "",
            "并发数": 0,
            "执行次数": "",
            "总耗时": cost,
            "开始时间": begin_time,
            "结束时间": end_time,
            "首包99line": first_cost_99,
            "首包95line": first_cost_95,
            "首包90Line": first_cost_90,
            "首包中位数": real_first_median,
            "首包最大值": real_first_max,
            "首包最小值": real_first_min,
            "总耗时99line": total_cost_99,
            "总耗时95line": total_cost_95,
            "总耗时90line": total_cost_90,
            "总耗时中位数": total_cost_median,
            "总耗时最大值": total_cost_max,
            "总耗时最小值": total_cost_min,
            "性能指标(字/秒)": speed_word
        }
        return average_row_to_all
    except Exception as e:
        print(f"统计数据失败，原因：{e}")


def run_single_scene(run_number, thread, loop, uuid):
    thread = int(thread)
    loop = int(loop)
    print("thread:{}\nloop:{}".format(thread, loop))
    save_print_to_excel(
        thread, loop, run_number, uuid)
    print("获取提示执行结束")


if __name__ == "__main__":

    # uuid_data = "315cbb5a-7475-4c6c-bdd6-b04425a8f96b"

    #   单场景压测时,设置为single,多场景混合压测时,设置为mixed
    action = "single"

    #   根据环境动态取数据库,同时,只能在dev环境发送消息
    env = "dev"
    #   新增控制发送飞书消息开关,不依赖环境
    is_send_msg = False

    parser = argparse.ArgumentParser(description="处理命令行参数")
    # 单场景压测时,设置为single,多场景混合压测时,设置为mixed
    parser.add_argument("--action", type=str, required=False, help="执行方法")
    # 根据环境动态取数据库,默认dev环境
    parser.add_argument("--env", type=str, required=False, help="执行环境")
    # 压测并发数
    parser.add_argument("--c", type=int, required=False, help="并发数")
    # 压测执行次数
    parser.add_argument("--n", type=int, required=False, help="执行次数")
    # 当天第几次执行
    parser.add_argument("--run_number", type=int,
                        required=False, help="当天第几次执行")

    # 解析参数
    args = parser.parse_args()
    if args.env is not None:
        env = args.env
    else:
        env = "dev"

    if args.action is not None:
        action = args.action
    else:
        action = "single"
    if args.c is not None:
        concurrency = args.c
    else:
        concurrency = 1
    if args.n is not None:
        request_nums = args.n
    else:
        request_nums = 1
    if args.run_number is not None:
        run_number = args.run_number
    else:
        run_number = 1

    res_path = f"./{get_now('day')}{os.sep}{run_number}"
    if not os.path.exists(res_path):
        os.makedirs(res_path)

    start_time = datetime.now()
    start_time_str = start_time.strftime("%Y-%m-%d:%H-%M-%S")
    if action == "single":
        uuid_data = run(token=token, to_stop=True)
        run_single_scene(run_number=run_number, thread=concurrency,
                         loop=request_nums, uuid=uuid_data)

    elif action == "calc":
        #   计算结果
        calc_data(res_path)
    elif action == "send":
        from tool.util import send_card_message
        #   测试群
        load_msg = "压测结束！\n"
        title = "压测结果通知"
        send_card_message(send_url=send_url, load_msg=load_msg, title=title)

    else:
        raise Exception("请输入正确的操作类型")
