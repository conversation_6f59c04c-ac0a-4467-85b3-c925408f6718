import requests
import json
from datetime import datetime
import sys
import random

#   2025-02-21 号优化后
# 服务器地址
base_url = "https://test-aiteaching-api.cmcm.com"
# 要进行对话的task_id
task_id = 346

# 获取token,手工更新
token = "Bearer Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo2MiwidHlwZSI6InVzZXIiLCJleHAiOjE3Mzg4OTk4NzZ9.fLpbEhjpkhuAHWIaMvkz085r99dQJqZPtPcRXf3k6X8"

# headers = {
#     "Authorization": token
# }
# 添加状态码映射
STATUS_MAP = {
    0: "初始化",
    1: " AI 对话",
    2: "User对话",
    3: "判断停止",
    4: "判断进行中",
    5: "阶段评分",
    6: "评分进行中",
    7: "结束状态",
    8: "最终结束状态"
}

# 添加全局变量存储最新info
current_info = None


def print_time_message(role, status, message):
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    status_text = STATUS_MAP.get(status, f"未知状态({status})")
    print(f"{current_time} - [{role}]:{message}")
    print(f"---状态--- {status}:{status_text}")


def check_response(response, api_name):
    if response.status_code != 200:
        print(f"API {api_name} 调用失败: HTTP {response.status_code}")
        raise Exception(f"API {api_name} HTTP错误: {response.status_code}")

    data = response.json()
    if data['code'] != 20000:
        print(f"API {api_name} 返回错误: {data['message']}")
        raise Exception(f"API {api_name} 业务错误: {data['message']}")
    return data


def update_info(data):
    global current_info
    if 'info' in data['data']:
        current_info = data['data']['info']
    else:
        current_info = data['data']  # 对于set_staging_index的特殊处理


def set_staging_index(uuid, index):
    url = f"{base_url}/v1/api/teach_plus/set_staging_index"
    params = {
        'uuid': uuid,
        'index': index
    }

    response = requests.get(url, params=params, headers=headers)
    data = check_response(response, "set_staging_index")

    update_info(data)
    print_time_message("设置阶段", current_info['status'], f"阶段索引: {current_info['staging_index']}")
    return current_info['status']


def init_conversation():
    url = f"{base_url}/v1/api/teach_plus/init_instance"
    payload = {
        "task_id": task_id,
        "is_exam": 0,
        "sop_id": 0,
        "base_prompt_id": 0,
        "base_prompt_name": "default",
        "base_prompt_version": 10000
    }

    response = requests.post(url, json=payload, headers=headers)
    data = check_response(response, "init_instance")

    uuid = data['data']['uuid']
    print(f"创建对话成功，uuid：{uuid}")
    print_time_message("AI", data['data']['status'], data['data']['message_list_ai'][0])
    update_info(data)
    return uuid


def get_ai_answer(uuid):
    url = f"{base_url}/v1/api/teach_plus/get_ai_answer"
    params = {'uuid': uuid}

    response = requests.get(url, params=params, headers=headers)
    data = check_response(response, "get_ai_answer")

    print_time_message("User", data['data']['info']['status'], data['data']['message'])
    update_info(data)
    return data['data']['message']


def send_user_message(uuid, message):
    url = f"{base_url}/v1/api/teach_plus/user_chat_auto_process"
    params = {
        'uuid': uuid,
        'message': message
    }

    response = requests.get(url, params=params, headers=headers)
    data = check_response(response, "user_chat")

    print_time_message("AI", data['data']['info']['status'], data['data']['message'])
    update_info(data)
    return data['data']['info']['status']


def judge_stop(uuid):
    url = f"{base_url}/v1/api/teach_plus/judge_stop"
    params = {'uuid': uuid}

    response = requests.get(url, params=params, headers=headers)
    data = check_response(response, "judge_stop")

    print_time_message("判断停止", data['data']['info']['status'], data['data']['message'])
    update_info(data)
    return data['data']['info']['status']


def get_overall_score(uuid):
    url = f"{base_url}/v1/api/teach_plus/get_overall_score"
    params = {'uuid': uuid}

    response = requests.get(url, params=params, headers=headers)
    data = check_response(response, "get_overall_score")

    score_data = data['data']['message']
    print_time_message("最终停止 - 评分详情：", data['data']['info']['status'],
                       f"总分: {score_data['score']}\n"
                       f"专业性: {score_data['scoreProfessionalism']}\n"
                       f"熟悉度: {score_data['scoreFamiliarity']}\n"
                       f"逻辑性: {score_data['scoreLogic']}\n"
                       f"亲和力: {score_data['scoreAffinity']}\n"
                       f"技能: {score_data['scoreSkills']}\n"
                       f"评语: {score_data['comments']}")
    update_info(data)
    return data['data']['info']['status']


def calc_score(uuid):
    url = f"{base_url}/v1/api/teach_plus/calc_score"
    params = {'uuid': uuid}

    response = requests.get(url, params=params, headers=headers)
    data = check_response(response, "calc_score")

    summary = data['data']['message']['summary']
    print_time_message("阶段评分", data['data']['info']['status'],
                       f"总分: {summary['totalScore']}\n"
                       f"评语: {summary['overallComments']}")
    update_info(data)
    return data['data']['info']['status']


def main(to_stop=False):
    try:
        # 初始化对话（循环外）
        uuid = init_conversation()
        status = current_info['status']
        round_count = 0  # 添加轮次计数
        max_rounds = random.randint(1, 4)  # 最大轮次限制

        # 状态机循环
        while status != 8 and round_count < max_rounds:  # 添加轮次判断
            if status == 2:  # 用户对话状态
                round_count += 1  # 在获取AI回答前增加计数
                print(f"第 {round_count} 轮对话")

                ai_message = get_ai_answer(uuid)
                status = send_user_message(uuid, ai_message)
                #   压测判停接口，直接结束
                # break
            elif status == 3:  # 判断状态
                status = judge_stop(uuid)

            elif status == 5:  # 评分状态
                status = calc_score(uuid)

            elif status == 7:  # 结束状态
                # 检查是否需要进入下一阶段
                staging_index = current_info.get('staging_index', 0)
                staging_info = current_info.get('staging_info', [])

                if staging_index < len(staging_info) - 1:
                    status = set_staging_index(uuid, staging_index + 1)
                else:
                    status = 8  # 如果没有下一阶段，进入最终结束状态

            else:
                print(f"未处理的状态: {status}")
                break

        if status == 3:
            status = judge_stop(uuid)
        if status == 5:
            status = calc_score(uuid)
        if status == 7:
            staging_index = current_info.get('staging_index', 0)
            staging_info = current_info.get('staging_info', [])
            if staging_index < len(staging_info) - 1:
                status = set_staging_index(uuid, staging_index + 1)
        # 最终结束状态的处理
        if round_count >= max_rounds:
            print(f"已达到最大轮次限制({max_rounds}轮)")
            return "轮次超限", uuid

        status = get_overall_score(uuid)
        print("对话已完成")
        return "正常结束", uuid

    except Exception as e:
        print(f"发生错误: {str(e)}")
        return f"异常结束: {str(e)}", ""


def set_token(token):
    global headers
    headers = {
        "Authorization": token
    }


def run(to_stop=False, token="Bearer Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo2MiwidHlwZSI6InVzZXIiLCJleHAiOjE3Mzg4OTk4NzZ9.fLpbEhjpkhuAHWIaMvkz085r99dQJqZPtPcRXf3k6X8"):
    set_token(token)
    results = []
    for i in range(1):
        print(f"\n=== 开始第 {i+1} 次运行 ===")
        result = main(to_stop)
        results.append(result[0])

    print("\n=== 运行统计 ===")
    total = len(results)
    normal_count = results.count("正常结束")
    max_rounds_count = results.count("轮次超限")
    error_count = total - normal_count - max_rounds_count

    print(f"总运行次数: {total}")
    print(f"正常结束: {normal_count} 次")
    print(f"轮次超限: {max_rounds_count} 次")
    print(f"异常结束: {error_count} 次")

    if error_count > 0:
        print("\n异常详情:")
        for i, result in enumerate(results[0]):
            if result.startswith("异常结束"):
                print(f"第 {i+1} 次运行: {result[0]}")
    # print(result[1])
    return result[1] if len(result) > 1 else result[0]


#   需要压测阶段评分接口时，设置为True
run_calc_score = True
# run()
