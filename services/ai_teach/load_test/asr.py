#   备份

# 用于测试豆包ASR的websocket连接

import asyncio
import websockets
import json
import base64
import time
from dataclasses import dataclass
from typing import List
import logging
import audi
import ssl
import pandas as pd
import aiohttp  # 添加此行

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 在main函数之前添加这行，完全禁用证书验证
ssl._create_default_https_context = ssl._create_unverified_context


@dataclass
class ASRPacket:
    type: str
    uid: int
    format: str
    req_id: str
    sequence: int
    audio_data: str


class ASRWebsocketTest:
    def __init__(self):
        # self.base_url = "wss://aiteaching-asr.orionstar.com/ws/asr"
        self.base_url = "wss://test-l40x4-7863.orionstar.com/ws/asr"
        self.results = []
        # 预加载的音频数据
        self.audio_data = self.prepare_audio_data()

    def prepare_audio_data(self) -> str:
        """准备测试用的音频数据"""
        # 这里使用你提供的示例音频数据
        return audi.audio_data_list

    def create_packet(self, is_last: bool) -> dict:
        """创建数据包"""
        if is_last:
            return self.audio_data
        else:
            return {"format": "mp3"}

    async def simulate_asr_session(self, session_id: str):
        """模拟单个ASR会话"""
        result = {
            "session_id": session_id,
            "start_time": time.time(),
            "success": False,
            "packet_time_cost": 0,
            "error": None
        }

        try:
            # 构建WebSocket URL
            params = {
                'uuid': session_id,
                'user_id': '62',
                'time_str': '1tYj93',
                'salt': 'yN0EIctmyAUvvlfOxAVrwUO'
            }
            ws_url = f"{self.base_url}?{'&'.join(f'{k}={v}' for k, v in params.items())}"

            # 使用aiohttp设置代理并禁用SSL验证
            proxy = "http://10.60.129.23:8888"
            ssl_context = ssl._create_unverified_context()  # 创建不验证证书的SSL上下文
            async with aiohttp.ClientSession() as session:
                async with session.ws_connect(ws_url, proxy=proxy, ssl=ssl_context) as ws:
                    start_packet_time = time.time()
                    rec_last = False
                    is_last = False

                    # 创建发送和接收任务
                    send_task = asyncio.create_task(self.send_packets(ws))
                    receive_task = asyncio.create_task(self.receive_responses(ws, session_id, start_packet_time, result))

                    # 等待任务完成
                    await asyncio.gather(send_task, receive_task)

        except websockets.ConnectionClosed as e:
            result["error"] = f"Connection closed: {str(e)}"
            logger.error(f"Session {session_id}: Connection closed - {str(e)}")
        except Exception as e:
            result["error"] = f"Error: {str(e)}"
            logger.error(f"Session {session_id}: Error - {str(e)}")
        finally:
            result["end_time"] = time.time()
            self.results.append(result)

    async def send_packets(self, ws):
        """发送数据包"""
        for packet in audi.audio_data_list:
            #   捕获异常
            try:
                await ws.send_str(json.dumps(packet))
                print("发送成功")
            except BaseException as e:  # 添加异常处理
                logger.error(f"Error sending packet: {str(e)}")  # 打印异常信息
            await asyncio.sleep(0.2)  # 模拟实际发送间隔

    async def receive_responses(self, ws, session_id, start_packet_time, result):
        """接收响应"""
        rec_last = False
        while not rec_last:
            try:
                response = await asyncio.wait_for(ws.receive(), timeout=10)
                if response.type == aiohttp.WSMsgType.TEXT:
                    # 处理文本消息
                    response_data = json.loads(response.data)
                    print(response_data)
                    if response_data.get("is_last"):
                        rec_last = True
                        packet_time = time.time()
                        result["packet_time_cost"] = packet_time - start_packet_time
                        result['response'] = response_data
                        result['success'] = True
            except asyncio.TimeoutError:
                logger.warning(f"Timeout waiting for response in session {session_id}")  # 增加超时警告
                packet_time = time.time()
                result["packet_time_cost"] = packet_time - start_packet_time
                result['response'] = "timeout"
                result['success'] = False
            except Exception as e:
                logger.error(f"Error receiving response: {str(e)}")  # 捕获接收响应时的异常
                result['success'] = False
                result['error'] = str(e)

    async def run_stress_test(self, concurrent_users: int, test_duration: int):
        """运行压力测试"""
        start_time = time.time()
        tasks = set()
        session_counter = 0

        logger.info(f"Starting stress test with {concurrent_users} concurrent users")

        try:
            while time.time() - start_time < test_duration:
                # 维持并发用户数
                while len(tasks) < concurrent_users:
                    session_id = f"{int(time.time())}_{session_counter}"
                    task = asyncio.create_task(
                        self.simulate_asr_session(session_id)
                    )
                    tasks.add(task)
                    session_counter += 1

                # 等待任何一个任务完成
                done, pending = await asyncio.wait(
                    tasks,
                    return_when=asyncio.FIRST_COMPLETED
                )

                tasks = pending

                # 定期打印统计信息
                if session_counter % 50 == 0:
                    self.print_stats()

        finally:
            if tasks:
                await asyncio.wait(tasks)
            self.print_final_report()

    def print_stats(self):
        """打印当前统计信息"""
        if not self.results:
            return

        total = len(self.results)
        success = sum(1 for r in self.results if r["success"])
        avg_latency = sum((r["end_time"] - r["start_time"]) for r in self.results) / total

        logger.info(f"""
当前统计:
总请求数: {total}
成功率: {(success/total)*100:.2f}%
平均延迟: {avg_latency:.2f}秒
        """)

    def print_final_report(self):
        """打印最终报告并保存到Excel"""
        # 将结果保存为JSON
        # json_filename = f"asr_test_results_{int(time.time())}.json"
        # with open(json_filename, "w") as f:
        #     json.dump(self.results, f, indent=2)

        # 准备Excel数据
        excel_data = []
        for result in self.results:
            row = {
                'Session ID': result['session_id'],
                '开始时间': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(result['start_time'])),
                '结束时间': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(result['end_time'])),
                '总耗时(秒)': round(result['end_time'] - result['start_time'], 2),
                '数据包耗时(秒)': round(result.get('packet_time_cost', 0), 2),
                '是否成功': '成功' if result['success'] else '失败',
                '响应': result.get('response', ''),
                '错误信息': result.get('error', '')
            }
            excel_data.append(row)

        # 创建DataFrame并保存为Excel
        df = pd.DataFrame(excel_data)
        excel_filename = f"asr_test_results_{int(time.time())}.xlsx"

        # 计算统计信息
        total = len(self.results)
        success = sum(1 for r in self.results if r['success'])
        success_rate = (success / total) * 100 if total > 0 else 0
        avg_latency = sum((r['end_time'] - r['start_time']) for r in self.results) / total if total > 0 else 0

        # 创建统计信息DataFrame
        stats_data = {
            '指标': ['总请求数', '成功数', '成功率', '平均延迟(秒)'],
            '值': [total, success, f'{success_rate:.2f}%', f'{avg_latency:.2f}']
        }
        stats_df = pd.DataFrame(stats_data)

        # 使用ExcelWriter将多个表写入同一个Excel文件
        with pd.ExcelWriter(excel_filename, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='详细数据', index=False)
            stats_df.to_excel(writer, sheet_name='统计摘要', index=False)

            # 自动调整列宽
            for sheet in writer.sheets.values():
                for idx, col in enumerate(df.columns):
                    max_length = max(
                        df[col].astype(str).apply(len).max(),
                        len(col)
                    )
                    sheet.set_column(idx, idx, max_length + 2)

        logger.info(f"测试结果已保存到: {excel_filename}")


async def main():
    test = ASRWebsocketTest()

    # 运行5分钟测试，10个并发用户
    await test.run_stress_test(
        concurrent_users=1,
        test_duration=10
    )

if __name__ == "__main__":
    asyncio.run(main())
