# 手动添加的 BASE_DIR 代码
import os
import sys
import copy
import requests
import uuid
import concurrent.futures
import pandas as pd
import argparse
import time
import threading
import json
from datetime import datetime
# from pathlib import Path

BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(
    os.path.dirname(os.path.abspath(__file__))))))
sys.path.append(BASE_DIR)
print(BASE_DIR)
try:
    from test_conifg.config import ip, port
    from test_conifg.path_config import DOWNLOAD_PATH
    from tool.util import send_card_message
    from tool.DB import DB
except Exception:
    print("请先配置 config.py")
    exit()


OPENAI_KEY = "***************************************************"
# OPENAI_KEY = "***************************************************"

GPT35_INPUT_MAX = 4000

SYSTEM_PROMPT = """
###角色
你是一位经验丰富的酒店行业数据分析师,你有着丰富的酒店行业数据分析知识和经验。
同时你也是一位经验丰富的数据问答效果评测工程师,你也可以根据提供的评测标准针对酒店数据问答结果进行评测。

**评测标准user**
1.回答内容是否正确识别提问意图,满分3分
    1)如果识别错误,得0分,打分结束,该回答内容直接得0分
    2)如果识别正确,得3分,继续下一步打分
2.洞察内容是否正确,满分6分
    1)如果无洞察,得0分,打分结束,该回答内容直接得3分
    2)如果有洞察,但时间维度错误,得0分,打分结束,该回答内容直接得3分
    3)如果有洞察,且时间维度正确,但数据错误,得2分,打分结束,该回答内容直接得5分
    4)如果有洞察,且时间维度正确,得2分,数据正确再得4分,继续下一步打分
3.图表数据是否正确,满分1分
    1)图表正确得1分,打分结束,该回答内容得10分
    2)图表错误得0分,打分结束,该回答内容得9分
    3)图标数据为空得1分,打分结束,该回答内容得10分


**输出示例**
```
{
    "desc":"评测标准A的评测说明",
    "score":10
}
```


"""

USER_PROMPT = """

你需要执行如下任务

针对如下**输入数据**,严格按照**评测标准A**和**评测标准B**,遵循**输出示例**，给出**输出数据**。
注意结果只输出JSON,不要输出其他内容

**输入数据**
1. 用户提问的问题```{query}```
2. 正确答案的值```{compare_answer}```
3. 用户的文字答案```{answer}```
4. 用户的图表答案```{echart_data}```


"""

API_URL = "http://self-hosting.chatmax.net:100/v1/ctai/ai_chat"


def RequestChatCompletion(query, compare_answer, answer, echart_data, return_type="content", num_retries=3,
                          verbose=False):
    content1 = SYSTEM_PROMPT.replace("{query}", query).replace("{compare_answer}", compare_answer).replace(
        "{answer}", answer).replace("{echart_data}", echart_data)
    content2 = USER_PROMPT.replace("{query}", query).replace("{compare_answer}", compare_answer).replace(
        "{answer}", answer).replace("{echart_data}", echart_data)
    messages = [
        {"role": "system", "content": content1},
        {"role": "user", "content": content2}
    ]
    message_str = json.dumps(messages, ensure_ascii=False)

    url_completion = "https://api.openai.com/v1/chat/completions"
    payload = json.dumps({
        "model": "gpt-4",  # "gpt-3.5-turbo",  #"gpt-4",
        "messages": messages,
        "temperature": 0.0,
    })
    headers = {
        'Authorization': 'Bearer {}'.format(OPENAI_KEY),
        'Content-Type': 'application/json'
    }

    num_tried = 0
    while num_tried < num_retries:
        try:
            response = requests.request("POST", url_completion, headers=headers, data=payload)
            if return_type == "json":
                return response.json()
            elif return_type == "content":
                content = response.json()["choices"][-1]["message"]["content"]
                # if verbose:
                #     uuid_str = str(uuid.uuid4())
                return message_str, content
            else:
                return message_str, response
        except Exception as e:
            print(f'请求GPT4错误：{e}', flush=True)
            num_tried += 1
            time.sleep(num_tried)

    return '', ''


def send_stream(query, session_id, request_id):
    # data = DAI_DATA_TEMPLATE.copy()
    #   为使用多线程使用深拷贝
    data = copy.deepcopy(DAI_DATA_TEMPLATE)
    # session_id = f'{uuid.uuid4()}'
    # request_id = f'{uuid.uuid4()}'
    data['session_id'] = session_id
    data['request_id'] = request_id
    data['query'] = query
    headers = {"content-Type": "application/json"}
    request_time = datetime.now()
    response = requests.post(API_URL, headers=headers, json=data, stream=True)
    if response.status_code != 200:
        raise Exception(f"状态码错误:{response.status_code}")

    answer = ''
    echart_data = []
    content = ""
    first_packet_time_2 = None
    for line in response.iter_lines():
        # 处理响应数据
        if line.startswith(b"data: "):
            json_data = line[6:]
            try:
                data_dict = json.loads(json_data)
                if data_dict['code'] != 0:
                    # print(f'返回不为0：{json.dumps(data_dict,ensure_ascii=False)}')
                    raise Exception(
                        f"接口报错:{json.dumps(data_dict, ensure_ascii=False)}")
                    continue
                data = data_dict['data']
                if data['return_type'] == 'insight':
                    content = data['return_data']['insight']['text']
                    if content and not first_packet_time_2:
                        first_packet_time_2 = datetime.now()
                    answer += content
                elif data['return_type'] == 'chart' and data['return_data']['chart']['status'] == "success":
                    echart_data = data['return_data']['chart']['echart_data']
                elif data['return_type'] == 'tips':
                    first_packet_time_2 = datetime.now()
                    answer += data['return_data']['tips']
                else:
                    pass
                    # print(json.dumps(data['return_data'],ensure_ascii=False))
            except json.JSONDecodeError:
                # print("json loads error:->{}".format(line))
                continue
    if len(answer) == 0 and len(echart_data) == 0:
        raise Exception("无数据")
    request_complete_time = datetime.now()
    first_cost_time = (first_packet_time_2 - request_time).total_seconds()
    total_cost_time = (request_complete_time - request_time).total_seconds()
    # print(f'洞察数据:{answer}')
    # print(f'图表数据:{json.dumps(echart_data,ensure_ascii=False)}')
    return answer, echart_data, session_id, first_cost_time, total_cost_time


locker = threading.Lock()


def calcOneLine(index, query, compare_answer, expect_time):
    # print(f'原始提问:{query}')
    # print(f'期望答案:{compare_answer}')
    answer = ''
    echart_data = []
    gpt4_input = ''
    gpt4_output = ''

    gpt4_1_score = -1
    gpt4_1_reson = ''
    gpt4_2_score = -1
    # gpt4_2_reson = ''
    session_id = ''
    first_cost_time = 0
    total_cost_time = 0
    # 尝试次数
    try_count = 0
    while try_count < 3:
        try:
            session_id = f'{uuid.uuid4()}'
            request_id = f'{uuid.uuid4()}'
            answer, echart_data, session_id, first_cost_time, total_cost_time = send_stream(
                query, session_id, request_id)
            try:
                gpt4_input, gpt4_output = RequestChatCompletion(f'{query}', f'{compare_answer}', f'{answer}',
                                                                json.dumps(echart_data, ensure_ascii=False))
                # gpt4_input,gpt4_output = '',''

            except Exception as e:
                gpt4_1_reson = f'GPT4请求异常:{e}'
        except Exception as e:
            answer = f'DAI请求异常:{e}'
        try:
            if gpt4_output is None or len(gpt4_output) == 0:
                if len(gpt4_1_reson) == 0:
                    gpt4_1_reson = 'GPT4没有评分。'
                gpt4_1_score = 0
                gpt4_2_score = 0
            else:
                gpt4_output = json.loads(gpt4_output)
                gpt4_1_score = gpt4_output['A']['score']
                gpt4_1_reson = gpt4_output['A']['desc']
                gpt4_2_score = gpt4_output['B']['score']
                # gpt4_2_reson = gpt4_output['B']['desc']
                break
        except Exception as e:
            gpt4_1_reson = f'解析GPT4结果异常:{e}'
            gpt4_1_score = 0
            gpt4_2_score = 0
        try_count += 1
    with locker:
        print(f'==============第【{index + 1}】条数据处理完毕==============')
        print(f'提问:{query}')
        print(f'标准答案:{compare_answer}')
        print(f'机器人答案:{answer}')
        print(f'机器人数据:{json.dumps(echart_data, ensure_ascii=False)}')
        print(f'session_id:{session_id}')
        print(f'猎户标准评分:{gpt4_1_score}')
        print(f'猎户标准评分原因:{gpt4_1_reson}')
        print(f'华住标准评分:{1 if gpt4_1_score == 10 else 0}')
        print(f'有效首包耗时:{first_cost_time}')
        print(f'总耗时:{total_cost_time}')

    return {
        '序号': index + 1,
        '提问': query,
        '标准答案': compare_answer,
        '机器人答案': answer,
        '机器人数据': echart_data,
        'session_id': session_id,
        '查看日志': f'http://10.100.10.57:96/api/dai/log?page=1&page_size=20&session_id={session_id}',
        '猎户标准评分': gpt4_1_score,
        '猎户标准评分原因': gpt4_1_reson,
        '华住标准评分': 1 if gpt4_1_score == 10 else 0,
        'GPT评分原始输入': f'{gpt4_input}',
        'GPT评分原始输出': f'{gpt4_output}',
        '预期时间': expect_time,
        '首包耗时': first_cost_time,
        '完整耗时': total_cost_time
    }


def diff_expect_time(date_list):
    ok_number = 0
    # print(f"{datetime.now()}开始执行校验期望时间和预期时间")
    for row in date_list:
        session_id = row.get("session_id")
        actual_time, status, sql_dict = get_log_info(session_id)
        expect_time = row.get("预期时间")

        if isinstance(expect_time, str):
            expect_time = json.loads(expect_time)
        row["实际时间"] = json.dumps(actual_time, indent=2, ensure_ascii=False)
        if compare_dicts_structure_time(actual_time, expect_time):
            row["时间对比结果"] = "通过"
            ok_number += 1
        else:
            row["时间对比结果"] = "不通过"
        row["state"] = status
        for k in sql_dict:
            row[k] = sql_dict[k]
    ok_rate = "{:.2f}".format(ok_number / len(date_list) * 100)
    # print(f"{datetime.now()}校验期望时间和时间系统识别时间对比结束\n")
    return date_list, ok_rate


def get_log_info(session_id):
    #   根据session_id获取数据库中的数据
    query_sql = "select trace_logs from trace_logs where session_id='{}'".format(
        session_id)
    sql_dict = {}
    res = DB.query_all(sql=query_sql, env="dev_log")
    if res:
        res_str = res[0].get("trace_logs")
        # start_index = res_str.find("====指标抽取时间====")
        #   抽取时间
        start_len = "====时间抽取结果====".__len__()
        start_index = res_str.find("====时间抽取结果====")
        end_index = res_str.find("====执行指标sql结果====")
        actual = res_str[start_index + start_len:end_index]
        try:
            dict_time = json.loads(actual.replace("'", "\""))
            # print(f"最终获取到的time数据:{dict_time}\n类型:{type(dict_time)}")
        except BaseException:
            dict_time = {"data": actual}
            # print(f"load数据异常->{actual}")

        #   抽取是否走指标
        status_len = "====是否走指标====".__len__()
        status_start_index = res_str.find("====是否走指标====")
        status_end_index = res_str.find("====时间抽取结果====")
        status = res_str[status_start_index + status_len:status_end_index]

        #   指标sql
        goal_sql_str = "====指标sql===="
        sql_len = goal_sql_str.__len__()
        sql_start_index = res_str.find(goal_sql_str)
        sql_end_index = res_str.find("====开始执行指标计算====")
        if sql_end_index != -1:
            goal_sql = res_str[sql_start_index + sql_len:sql_end_index]
            sql_dict["指标sql"] = goal_sql

        #   ====获取指标远期sql_维度====sql
        forward_sql_str = "====获取指标远期sql_维度===="
        sql_len = forward_sql_str.__len__()
        sql_start_index = res_str.find(forward_sql_str)
        sql_end_index = res_str.find("====获取指标远期sql_维度结果====")
        if sql_end_index != -1:
            forward_sql = res_str[sql_start_index + sql_len:sql_end_index]
            sql_dict["指标远期sql_维度"] = forward_sql

        #   ====获取指标远期sql====
        forward_goal_sql_str = "====获取指标远期sql===="
        sql_len = forward_goal_sql_str.__len__()
        sql_start_index = res_str.find(forward_goal_sql_str)
        sql_end_index = res_str.find("====获取指标远期sql结果====")
        if sql_end_index != -1:
            forward_goal_sql = res_str[sql_start_index + sql_len:sql_end_index]
            sql_dict["指标远期sql"] = forward_goal_sql

        #   ====获取指标实时sql====
        real_time_goal_sql_str = "====获取指标实时sql===="
        sql_len = real_time_goal_sql_str.__len__()
        sql_start_index = res_str.find(real_time_goal_sql_str)
        sql_end_index = res_str.find("====获取指标实时sql结果====")
        if sql_end_index != -1:
            real_time_goal_sql = res_str[sql_start_index +
                                         sql_len:sql_end_index]
            sql_dict["指标实时sql"] = real_time_goal_sql
        return dict_time, status, sql_dict
    else:
        print("没有从库里查询到日志数据")
        return {"data": "没有查到log"}, "无", {}


def compare_dicts_structure_time(res, exp):
    for key in exp:
        if key not in res:
            return False
        if key == "time_dimension":
            value1 = res["five_time_dimension"]
        else:
            value1 = res[key]
        value2 = exp[key]

        if isinstance(value1, dict) and isinstance(value2, dict):
            # 递归比较嵌套字典
            if not compare_dicts_structure_time(value1, value2):
                return False
        elif type(value1) != type(value2):
            return False
        elif isinstance(value2, list) and isinstance(value1, list):
            if key != "month_on_month_time":
                for i in range(value2.__len__()):
                    if value2[i] not in value1:
                        return False
        elif value1 != value2:
            return False
    return True


if __name__ == "__main__":

    parser = argparse.ArgumentParser(description="处理命令行参数")
    # 并发数量参数化，不填默认为1
    parser.add_argument("--c", type=int, required=False, help="并发数")
    # 运行文件参数化，不填默认为“数据问答测评”
    parser.add_argument("--f", type=str, required=False, help="文件名")
    # 测试集运行条数参数化，不填默认为全部case
    parser.add_argument("--n", type=int, required=False, help="处理多少行数据")
    # 是否执行时间对比，1 执行，0 不执行
    parser.add_argument("--t", type=int, required=False, help="执行时间对比")
    # 版本信息
    parser.add_argument("--v", type=str, required=False, help="当前服务部署版本信息")

    # 解析参数
    args = parser.parse_args()
    if args.c is not None:
        concurrency = args.c
    else:
        concurrency = 1
    if args.f is not None:
        input_file = args.f
    else:
        input_file = '数据问答测评.xlsx'
    if args.n is not None:
        max_rows = args.n
    else:
        max_rows = 999999
    if args.t:
        diff_time = 1
    else:
        diff_time = 0
    if args.v:
        service_version = args.v
    else:
        service_version = "手动执行"

    print('开始处理数据...')
    print(f'数据来源文件:{input_file}')
    print(f'并发数:{concurrency}')
    print(f'处理行数:{"全部" if max_rows == 999999 else max_rows}')

    # df = pd.read_excel('数据问答测评.xlsx')
    df = pd.read_excel(input_file, engine='openpyxl')
    print("----------------------开始校验期望时间格式----------------------")
    i = 1
    #   校验预期时间是否符合json格式
    for rows in df.iterrows():
        exp = rows[1].get("期望时间")
        try:
            load_exp = json.loads(exp)
            # print(f"load 后数据{load_exp}")
        except BaseException:
            print(f"第{i}条,{rows[1].get('query')}\nload 异常:{exp}")
        i += 1
    print("----------------------期望时间校验通过!----------------------")
    # if i>1:
    #     raise BaseException("预期时间不是json格式")
    # 遍历每一行
    data = []
    futureList = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=concurrency) as executor:
        for index, row in df.iterrows():
            if index >= max_rows:
                break
            # 获取第一列和第二列的数据
            futureList.append(executor.submit(
                calcOneLine, index, row[0], row[1], row[4]))

    for future in concurrent.futures.as_completed(futureList):
        result = future.result()
        if result is not None and isinstance(result, dict):
            data.append(result)
    print('===============gpt评测任务处理完毕============\n\n')
    print("-------------------------------开始处理时间抽取校验任务-------------------------------")
    if diff_time:
        data, ok_rate = diff_expect_time(data)
        #   筛选时间抽取错误数据
        error_data = [x for x in data if x["时间对比结果"] == "不通过"]
        for e_data in error_data:
            print(f"--------------------序号{e_data.get('序号')}时间抽取校验不通过的数据----------------------"
                  f"\n序号:{e_data.get('序号')}\nquery:{e_data.get('提问')}\n预期时间:{e_data.get('预期时间')}\n实际时间:{e_data.get('实际时间')}\n\n")
        print(
            "-------------------------------处理时间抽取校验任务结束-------------------------------\n\n")
    #   筛选GPT没有评分数据
    no_gpt_res_list = [x for x in data if str(
        x["猎户标准评分原因"]).__contains__("GPT4")]
    for no_res in no_gpt_res_list:
        print(f"--------------------序号{no_res.get('序号')}GPT4没有给出评分----------------------"
              f"\n序号:{no_res.get('序号')}\nquery:{no_res.get('提问')}\n实际答案:{no_res.get('机器人答案')}\n预期答案:{no_res.get('标准答案')}\n\n")

    data = sorted(data, key=lambda x: x['序号'])
    df = pd.DataFrame(data)
    #   计算猎户平均分
    course_lh = "{:.2f}".format(df["猎户标准评分"].mean() * 10)
    #   计算华住平均分 评分_华住
    course_HZ = "{:.2f}".format(df["华住标准评分"].mean() * 100)
    #   计算猎户总分
    course_lh_sum = df["猎户标准评分"].sum()
    #   计算华住总 评分_华住
    course_HZ_sum = df["华住标准评分"].sum()
    #   新增加总分行数据
    sum_row = {"查看日志": "总分数", "猎户标准评分": f"{course_lh_sum}",
               "华住标准评分": f"{course_HZ_sum}"}
    #   新增加平均值行数据
    if diff_time:
        avg_row = {"查看日志": "平均分数", "猎户标准评分": f"{course_lh}%", "华住标准评分": f"{course_HZ}%",
                   "时间对比结果": f"{ok_rate}%"}
    else:
        avg_row = {"查看日志": "平均分数", "猎户标准评分": f"{course_lh}%",
                   "华住标准评分": f"{course_HZ}%"}
    df = pd.concat([df, pd.DataFrame([sum_row, avg_row])], ignore_index=True)
    # 获取当前日期和时间
    now = datetime.now()
    file_name = '评测结果-' + now.strftime("%Y-%m-%d_%H-%M-%S") + '.xlsx'
    df.to_excel(file_name, index=False)
    output_path = "{output}{sep}".format(output=DOWNLOAD_PATH, sep=os.sep)
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    res_file = DOWNLOAD_PATH + os.sep + file_name
    df.to_excel(res_file, index=False)

    #   计算平均首包耗时
    first_avg = "{:.2f}".format(df["首包耗时"].mean())
    #   计算平均总耗时
    total_avg = "{:.2f}".format(df["完整耗时"].mean())

    print("---------------------------------本次任务整体结果---------------------------------")
    print(f"当前部署版本信息:{service_version}")
    print(f"总共执行case数:{len(data)}\n\n效果评测得分情况:")
    print(f"猎户标准评分sum:{course_lh_sum},华住标准评分sum:{course_HZ_sum}")
    print(f"猎户标准评分avg:{course_lh}%,华住标准评分avg:{course_HZ}%\n")
    print(f"首包耗时avg:{first_avg}")
    print(f"总耗时avg:{total_avg}")
    if diff_time:
        print(f"时间抽取得分情况:\n时间抽取校验失败数:{len(error_data)}\n成功率:{ok_rate}%\n\n")

        #   二期群
        task_url = 'https://open.feishu.cn/open-apis/bot/v2/hook/570117ef-3b70-464d-8544-effe3ce1e0c9'
        #   聚言测试群
        # task_url = "https://open.feishu.cn/open-apis/bot/v2/hook/bf9c14c9-5d6c-4094-9547-a957dabbcefb"
        path = "/api/download/{}".format(file_name)
        title = "效果评测结果通知!"
        down_url = "http://{ip}:{port}{path}".format(
            ip=ip, port=port, path=path)
        if float(course_lh) < 80:
            msg = (
                f"当前部署版本信息:{service_version}\n总共执行case数:{len(data)}\n\n效果评测得分情况(ps:gpt评分仅供参考):\n猎户标准评分avg:{course_lh}%\n"
                f"华住标准评分avg:{course_HZ}%\n\n时间抽取得分情况:\n时间抽取校验失败数:{len(error_data)}\n时间抽取成功率:{ok_rate}%\n\n平均首包耗时:{first_avg}秒\n"
                f"平均总耗时:{total_avg}秒\n\n请注意本次结果低于80分！！!\n\n")
            send_card_message(send_url=task_url, title=title,
                              down_url=down_url, at_all=msg)
        elif no_gpt_res_list:
            msg = (
                f"当前部署版本信息:{service_version}\n总共执行case数:{len(data)}\n\n效果评测得分情况(ps:gpt评分仅供参考):\n猎户标准评分avg:{course_lh}%\n"
                f"华住标准评分avg:{course_HZ}%\n\n时间抽取得分情况:\n时间抽取校验失败数:{len(error_data)}\n时间抽取成功率:{ok_rate}%\n\n"
                f"平均首包耗时:{first_avg}秒\n平均总耗时:{total_avg}秒\n\n"
                f"本次结果出现{len(no_gpt_res_list)}条GPT4没有打分，需要测试同学对其重新评分!")
            send_card_message(send_url=task_url, title=title,
                              down_url=down_url, at_msg=msg)
        else:
            msg = (
                f"当前部署版本信息:{service_version}\n总共执行case数:{len(data)}\n\n效果评测得分情况(ps:gpt评分仅供参考):\n猎户标准评分avg:{course_lh}%\n"
                f"华住标准评分avg:{course_HZ}%\n\n时间抽取得分情况:\n时间抽取校验失败数:{len(error_data)}\n时间抽取成功率:{ok_rate}%\n\n平均首包耗时:{first_avg}秒\n平均总耗时:{total_avg}秒\n\n")
            send_card_message(send_url=task_url, title=title,
                              down_url=down_url, msg_data=msg)
