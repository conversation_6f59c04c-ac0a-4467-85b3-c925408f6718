import os
import sys
#   优化代码
try:
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    print(BASE_DIR)
    sys.path.append(BASE_DIR)
except Exception:
    pass
from tool.util import send_message
import requests
import json
from datetime import datetime
from typing import List, Dict


class FeedbackMonitor:
    def __init__(self):
        self.base_url = "http://10.100.10.108:4002"
        self.headers = {
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Cookie': 'user_id=2887a4f4-a2e3-4d5f-a32c-ca6d187f76df',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36'
        }
        # 使用日期作为文件名
        today = datetime.now().strftime("%Y-%m-%d")
        self.alert_history_file = f"feedback_alert_history_{today}.json"
        self.alerted_records = self.load_alert_history()

    def load_alert_history(self) -> Dict:
        """加载当天的历史告警记录"""
        if os.path.exists(self.alert_history_file):
            with open(self.alert_history_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}

    def save_alert_history(self):
        """保存告警历史到当天的文件"""
        with open(self.alert_history_file, 'w', encoding='utf-8') as f:
            json.dump(self.alerted_records, f, ensure_ascii=False)

    def get_today_feedback(self) -> List[Dict]:
        """获取当天的反馈数据"""
        today = datetime.now().strftime("%Y-%m-%d")
        url = f"{self.base_url}/api/instance_view_list"

        params = {
            'date_from': today,
            'date_end': today,
            'has_feedback': 'yes',
            'page_size': 100,
            'page_index': 0
        }

        try:
            response = requests.get(url, headers=self.headers, params=params, verify=False)
            response.raise_for_status()
            return response.json().get('data', []).get('list', [])
        except Exception as e:
            print(f"获取数据失败: {str(e)}")
            return []

    # def send_alert(self, record: Dict):
    #     """发送告警
    #     这里可以根据实际需求实现告警方式（钉钉、邮件等）
    #     """
    #     alert_message = (
    #         f"新反馈告警\n"
    #         f"时间: {record.get('instanceCreatedAt')}\n"
    #         f"用户: {record.get('userName')}\n"
    #         f"反馈内容: {record.get('feedback')}\n"
    #     )
    #     print(alert_message)
        # TODO: 实现实际的告警发送逻辑

    def check_new_feedback(self):
        """检查新的反馈并发送告警"""
        feedback_records = self.get_today_feedback()
        new_feedback_messages = []

        for record in feedback_records:
            record_id = record.get('instanceUuid')
            # 检查是否已经告警过
            if record_id not in self.alerted_records:
                task_time = datetime.fromisoformat(record.get('instanceCreatedAt').replace('Z', '+00:00'))
                task_time = task_time.replace(hour=task_time.hour + 8)
                formatted_time = task_time.strftime("%Y-%m-%d %H:%M:%S")
                message = (
                    f"时间: {formatted_time}\n"
                    f"用户: {record.get('userName')}\n"
                    f"反馈内容: {record.get('feedback')}\n"
                    f"------------------------\n"
                )
                print(message)
                new_feedback_messages.append(message)
                self.alerted_records[record_id] = record

        # 如果有新反馈，整合后一次性发送
        if new_feedback_messages:
            combined_message = f"陪练任务新反馈汇总：({len(new_feedback_messages)}条)\n" + "".join(new_feedback_messages)
            send_url = "https://open.feishu.cn/open-apis/bot/v2/hook/d9d77858-20d7-4b14-8b22-655a2556b880"
            send_message(send_url=send_url, msg=combined_message)

        # 保存更新后的告警历史
        self.save_alert_history()


def main():
    monitor = FeedbackMonitor()
    monitor.check_new_feedback()


if __name__ == "__main__":
    main()
