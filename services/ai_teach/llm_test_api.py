from typing import List

import requests
import uvicorn
import time
from fastapi import FastAPI
from pydantic import BaseModel, Field
from llm_test_prompt import *

app = FastAPI()


def ask_qianwen_chat(content: str, history) -> str:
    message = [{
        "role": "system",
        "content": "你是小豹，是猎户星空开发的人工智能"
    },
        {
            "role": "user",
            "content": content
        }]
    message.extend(history)
    url = 'http://10.100.10.108:8007/v1/chat/completions'
    headers = {
        'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
        'Content-Type': 'application/json'
    }
    data = {
        "messages": message,
        "temperature": 0.4,
        "stream": False,
        "model": "qwen2.5-32B-awq"
    }
    start_time = time.time()
    response = requests.post(url, headers=headers, json=data)
    end_time = round(time.time() - start_time, 3)
    info = response.json()
    token = info["usage"]["completion_tokens"]
    count_time = round(token / end_time, 3)
    return info['choices'][0]['message']['content'], count_time


def ask_doubao_lite(content, history):
    message = [{
        "role": "system",
        "content": "你是小豹，是猎户星空开发的人工智能"
    },
        {
            "role": "user",
            "content": content
        }]
    message.extend(history)
    # 请求url
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    # 请求头
    headers = {
        "Authorization": "Bearer " + "87651b4d-eae5-4ee4-81ae-38e98616620a",
        "Content-Type": "application/json"
    }
    # 请求体
    data = {
        "model": "ep-20241115154755-w2qwx",  # 普通版本
        # "model": "ep-20241121143012-wzp7g",  # 角色版本
        "messages": message,
        "temperature": 0.4,
        "stream": False,
    }
    start_time = time.time()
    response = requests.post(url, headers=headers, json=data)
    end_time = round(time.time() - start_time, 3)
    info = response.json()
    token = info["usage"]["completion_tokens"]
    count_time = round(token / end_time, 3)
    return info['choices'][0]['message']['content'], count_time


def ask_doubao_lite_role(content, history):
    message = [{
        "role": "system",
        "content": "你是小豹，是猎户星空开发的人工智能"
    },
        {
            "role": "user",
            "content": content
        }]
    message.extend(history)
    # 请求url
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    # 请求头
    headers = {
        "Authorization": "Bearer " + "87651b4d-eae5-4ee4-81ae-38e98616620a",
        "Content-Type": "application/json"
    }
    # 请求体
    data = {
        # "model": "ep-20241115154755-w2qwx",  # 普通版本
        "model": "ep-20241121143012-wzp7g",  # 角色版本
        "messages": message,
        "temperature": 0.4,
        "stream": False,
    }

    start_time = time.time()
    response = requests.post(url, headers=headers, json=data)
    end_time = round(time.time() - start_time, 3)
    info = response.json()
    token = info["usage"]["completion_tokens"]
    count_time = round(token / end_time, 3)
    return info['choices'][0]['message']['content'], count_time


def ask_doubao_pro(content, history):
    message = [{
        "role": "system",
        "content": "你是小豹，是猎户星空开发的人工智能"
    },
        {
            "role": "user",
            "content": content
        }]
    message.extend(history)
    # 请求url
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    # 请求头
    headers = {
        "Authorization": "Bearer " + "87651b4d-eae5-4ee4-81ae-38e98616620a",
        "Content-Type": "application/json"
    }
    # 请求体
    data = {
        "model": "ep-20241115154547-kqrrf",  # 普通版本
        # "model": "ep-20241121142856-2mbkd", # 角色版本
        "messages": message,
        "temperature": 0.4,
        "stream": False,
    }

    start_time = time.time()
    response = requests.post(url, headers=headers, json=data)
    end_time = round(time.time() - start_time, 3)
    info = response.json()
    token = info["usage"]["completion_tokens"]
    count_time = round(token / end_time, 3)
    return info['choices'][0]['message']['content'], count_time


def ask_doubao_pro_role(content, history):
    message = [{
        "role": "system",
        "content": "你是小豹，是猎户星空开发的人工智能"
    },
        {
            "role": "user",
            "content": content
        }]
    message.extend(history)
    # 请求url
    url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    # 请求头
    headers = {
        "Authorization": "Bearer " + "87651b4d-eae5-4ee4-81ae-38e98616620a",
        "Content-Type": "application/json"
    }
    # 请求体
    data = {
        # "model": "ep-20241115154547-kqrrf", # 普通版本
        "model": "ep-20241121142856-2mbkd",  # 角色版本
        "messages": message,
        "temperature": 0.4,
        "stream": False,
    }

    start_time = time.time()
    response = requests.post(url, headers=headers, json=data)
    end_time = round(time.time() - start_time, 3)
    info = response.json()
    token = info["usage"]["completion_tokens"]
    count_time = round(token / end_time, 3)
    return info['choices'][0]['message']['content'], count_time


def get_prompt(model_name):
    prompt_dict = {
        'doubao_lite_user': douhao_lite_user_prompt,
        'doubao_pro_user': douhao_pro_user_prompt,
        'doubao_lite_user_role': douhao_lite_user_role_prompt,
        'doubao_pro_user_role': douhao_pro_user_role_prompt,
        'doubao_lite_ai': douhao_lite_ai_prompt,
        'doubao_pro_ai': douhao_pro_ai_prompt,
        'doubao_lite_ai_role': douhao_lite_ai_role_prompt,
        'doubao_pro_ai_role': douhao_pro_ai_role_prompt,
        # 'qianwen_user': qianwen_user_prompt,
        # 'qianwen_ai': qianwen_ai_prompt
    }
    return prompt_dict[model_name]


def history_str(data, history):
    res = []
    history_list = history.split("\n")
    pm_type = data.get("userRole", "")
    if not pm_type:
        pm_type = data.get("aiRole", "")
    for history in history_list:
        if history.startswith(f"[{pm_type}]"):
            res.append({
                "role": "assistant",
                "content": history
            })
        else:
            res.append({
                "role": "user",
                "content": history
            })
    return res


def chat(data_list, model_name):
    res_list = []
    for data in data_list:
        res = ""
        times = 0
        prompt = get_prompt(model_name)
        pm = prompt.format(**data)
        history = history_str(data, data.get("chatHistories", ""))
        if model_name in ['doubao_lite_user', 'doubao_lite_ai']:
            res, times = ask_doubao_lite(pm, history)
        elif model_name in ['doubao_pro_user', 'doubao_pro_ai']:
            res, times = ask_doubao_pro(pm, history)
        elif model_name in ['doubao_lite_user_role', 'doubao_lite_ai_role']:
            res, times = ask_doubao_lite_role(pm, history)
        elif model_name in ['doubao_pro_user_role', 'doubao_pro_ai_role']:
            res, times = ask_doubao_pro_role(pm, history)
        elif model_name in ['qianwen_user', 'qianwen_ai']:
            res, times = ask_qianwen_chat(pm, history)
        if data.get('aiRole', ""):
            res_list.append({
                "prompt": pm + "\n###对话历史：\n" + data.get("chatHistories", ""),
                "output": res,
                "role": "ai",
                "model": model_name,
                "time(token/s)": times
            })
        else:
            res_list.append({
                "prompt": pm + "\n###对话历史：\n" + data.get("chatHistories", ""),
                "output": res,
                "role": "user",
                "model": model_name,
                "time(token/s)": times
            })
        print(res)
    return res_list


class DataList(BaseModel):
    userRole: str = ""
    backgroundUser: str = ""
    userStagingInfo: str = ""
    aiRole: str = ""
    backgroundAI: str = ""
    aiStagingInfo: str = ""
    chatHistories: str = ""


class QueryModelInput(BaseModel):
    data_list: List[DataList]
    model_name: str = Field(..., description="模型名称", enum=['doubao_lite_user',
                                                               'doubao_pro_user',
                                                               'doubao_lite_user_role',
                                                               'doubao_pro_user_role',
                                                               'doubao_lite_ai',
                                                               'doubao_pro_ai',
                                                               'doubao_lite_ai_role',
                                                               'doubao_pro_ai_role',
                                                               'qianwen_user',
                                                               'qianwen_ai'])


@app.post("/chat")
async def query_model(input_data: QueryModelInput):
    data_list = input_data.data_list
    model_name = input_data.model_name
    result = chat(data_list, model_name)
    return result


if __name__ == '__main__':
    uvicorn.run(app, host='************', port=9000)
