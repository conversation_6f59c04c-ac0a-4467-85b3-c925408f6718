import requests
import time
from llm_test_prompt import *


def history_str(data, history):
    res = []
    history_list = history.split("\n")
    pm_type = data.get("userRole", "")
    if not pm_type:
        pm_type = data.get("aiRole", "")
    for history in history_list:
        if history.startswith(f"[{pm_type}]"):
            res.append({
                "role": "assistant",
                "content": history
            })
        else:
            res.append({
                "role": "user",
                "content": history
            })
    return res


def send_openai_request(context, history):
    config = {
        "host": "http://proxy-ai.smartsales.vip/v1/chat/completions",
        "model_name": "gpt-4o",
        "key": "********************************************************************************************************************************************************************"
    }
    headers = {
        "Authorization": f"Bearer {config['key']}",
        "Content-Type": "application/json"
    }
    message = [{
        "role": "system",
        "content": "你是小豹，是猎户星空开发的人工智能"
    },
        {
            "role": "user",
            "content": context
        }]
    message.extend(history)
    data = {
        "model": config["model_name"],
        "messages": message,
        "temperature": 0.7,
        "max_tokens": 150
    }

    try:
        start_time = time.time()
        response = requests.post(config["host"], headers=headers, json=data)
        end_time = round(time.time() - start_time, 3)
        data = response.json()
        token = data["usage"]["completion_tokens"]
        count_time = round(token / end_time, 3)
        print(f"data:{data['choices'][0]['message']['content']}, token_time:{count_time}")
        return data['choices'][0]['message']['content'], count_time
    except requests.exceptions.RequestException as e:
        return {"error": str(e)}


def chat(data_list, prompt_type):
    res_list = []
    if prompt_type == "user":
        prompt = douhao_lite_user_prompt
    else:
        prompt = douhao_lite_ai_prompt
    for i, data in enumerate(data_list):
        history = history_str(data, data.get("chatHistories", ""))
        context = prompt.format(**data)
        res, times = send_openai_request(context, history)
        if prompt_type == "user":
            res_list.append({
                "userRole": data.get("userRole", ""),
                "backgroundUser": data.get("backgroundUser", ""),
                "userStagingInfo": data.get("userStagingInfo", ""),
                "scoreStandard": data.get("scoreStandard", ""),
                "chatHistories": data.get("chatHistories", ""),
                "prompt": context + "\n###对话历史：\n" + data.get("chatHistories", ""),
                "output": res,
                "time(token/s)": times
            })
        else:
            res_list.append({
                "aiRole": data.get("aiRole", ""),
                "backgroundAI": data.get("backgroundAI", ""),
                "aiStagingInfo": data.get("aiStagingInfo", ""),
                "chatHistories": data.get("chatHistories", ""),
                "prompt": context + "\n###对话历史：\n" + data.get("chatHistories", ""),
                "output": res,
                "time(token/s)": times
            })
        if i != 0 and i % 10 == 0:
            time.sleep(10)
    return res_list


if __name__ == "__main__":
    import pandas as pd

    prompt_type = "ai"
    file_path = '评测集V2.xlsx'
    if prompt_type == "user":
        df = pd.read_excel(file_path, engine='openpyxl', sheet_name='userStartPrompt')
    else:
        df = pd.read_excel(file_path, engine='openpyxl', sheet_name='aiStartPrompt')
    data_list = df.to_dict(orient='records')
    res_list = chat(data_list, prompt_type)
    if prompt_type == "user":
        df = pd.DataFrame(res_list, columns=['userRole', 'backgroundUser', 'userStagingInfo', 'scoreStandard',
                                             'chatHistories', 'prompt', 'output', 'time(token/s)'])
    else:
        df = pd.DataFrame(res_list, columns=['aiRole', 'backgroundAI', 'aiStagingInfo', 'chatHistories', 'prompt',
                                             'output', 'time(token/s)'])
    df.to_excel(f'gpt_{prompt_type}_output.xlsx', index=False)
