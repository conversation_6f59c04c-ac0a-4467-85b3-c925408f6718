# 插入性能结果详细表
batch_insert_model_performance:
  insert into lhtest.model_performance (task_id, task_name,request_time,first_package_time,request_complete_time,first_cost_time,
  total_cost_time,input_len,output_len,input_token_len,output_token_len,token_speed,Inference_speed,thread,env,scene_description,GPU,model_name,error_answer,answer) values (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)
# 查询计算性能指标
query_batch_performance:
  select 
  ROUND(avg(token_speed),2) as token_speed,
  ROUND(avg(Inference_speed),2) as Inference_speed,
  ROUND(AVG(first_cost_time), 2) AS first_package_cost_time,
  ROUND(avg(total_cost_time),2) as total_cost_time,
  thread,
  ROUND(input_token_len, 2)      AS input_token_len,
  ROUND(output_token_len, 2)     AS output_token_len,
  task_name,
  task_id,scene_description,env,GPU,model_name
  FROM (select
  mp1.Inference_speed,
  mp1.token_speed,
  mp1.first_cost_time,
  total_cost_time,
  mp1.thread,
  mp1.input_token_len,
  mp1.output_token_len,
  mp1.task_name,
  mp1.task_id,mp1.env,mp1.scene_description,mp1.GPU,mp1.model_name
  FROM lhtest.model_performance mp1
  JOIN (
  SELECT task_id,
  MIN(first_cost_time) AS min_first_cost_time,
  MAX(first_cost_time) AS max_first_cost_time,
  MIN(token_speed) AS min_token_speed,
  MAX(token_speed) AS max_token_speed
  FROM lhtest.model_performance
  GROUP BY task_id
  ) mp2 ON mp1.task_id = mp2.task_id
  WHERE id >{key_id}
  AND mp1.first_cost_time > mp2.min_first_cost_time
  AND mp1.first_cost_time < mp2.max_first_cost_time
  AND mp1.token_speed > mp2.min_token_speed
  AND mp1.token_speed < mp2.max_token_speed
  ) AS subquery
  GROUP BY task_id
  ORDER BY task_id DESC;

# 查询当前最大Id
query_max_id:
  select max(id) as id from lhtest.model_performance

# 插入性能聚合数据
batch_insert_performance_summary:
  insert into lhtest.performance_summary (token_speed,speed,first_package_cost_time,total_cost_time,task_thread,
  input_token_len,output_token_len,task_name,task_id,scene_description,env,GPU,model_name) values (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)

# 查询性能记录报错信息
query_error:
  select * from model_performance where
  id >{max_id} and output_len=0
# 查询性能概述
query_performance_summary:
  select * from performance_summary

# 插入基于文档问答结果记录
insert_doc_qa:
  insert into lhtest.doc_qa_sore(task_id,question,context,standard_answers,actual_answer,key_word,sore,is_quote,matched_source_ids,actual_source_ids) values (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)


# 网信办请求记录查询
query_wxb_log:
  SELECT org.org_name,
  log.query_sensitive_ret,
  log.answer_sensitive_ret,
  log.query_text,
  log.answer_text,
  log.query_sensitive_info,
  log.answer_sensitive_info,
  log.debug_info,
  log.create_time_db
  FROM ctai_log.v_pmop_ctai_query_log log
  INNER JOIN ctai_center.v_pmop_ctai_org org
  ON log.ctorg_id = org.ctorg_id
  WHERE org.org_name LIKE '<EMAIL>'
  and log.is_recommend = 0
  and log.query_text != ''
  and log.create_time between '{start}'  and '{end}'

wxb:
  SELECT org.org_name,
  log.query_sensitive_ret,
  log.answer_sensitive_ret,
  log.query_text,
  log.answer_text,
  log.query_sensitive_info,
  log.answer_sensitive_info,
  log.debug_info,
  log.create_time_db
  FROM ctai_log.v_pmop_ctai_query_log log
  INNER JOIN ctai_center.v_pmop_ctai_org org
  ON log.ctorg_id = org.ctorg_id
  WHERE org.org_name LIKE '<EMAIL>'
  and log.is_recommend = 0
  and log.query_text != ''
  and log.create_time between '1699201372000000'  and '1699287772000000';