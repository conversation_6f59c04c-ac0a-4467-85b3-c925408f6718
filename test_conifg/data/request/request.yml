# 对话接口
gen_answer:
  method: POST
  headers: { 'Content-Type': 'application/json', 'Orionstar-Api-Key': "" }
  online:
    url: https://api.chatmax.net/capi/v1/chatmax/query_text_chat
    payload: { "character_id": "2940ced24f36c819e55a7d228907ad32","query_text": "你好","session_id": "","pad_debug_info": 0,"stream": 0,"recommend": 0,"lang": "zh_CN" }
  test:
    url: https://test-api-chatmax.orionstar.com/capi/v1/chatmax/query_text_chat
    payload: { "character_id": "8c87bfa242f530076ff97dd37293cf07","query_text": "你好","session_id": "","pad_debug_info": 0,"stream": 0, "recommend": 0,"lang": "zh_CN" }

# 修改技能-删除
modify_plugin:
  method: POST
  url: https://test-api-chatmax.orionstar.com/capi/v1/chatmax/modify_ctai_algo_plugin
  headers: { 'Content-Type': 'application/json', 'Orionstar-Api-Key': "" }
  payload: { "ctaplugin_id": "a54d74273bd0756669e7972e32a05533","delete_assoc_character_id": "87eb890804cead643fd3e02c1fdbe781","delete_assoc_ctdataset_id": "36a1e8ab74ded3148c9ba0b95eaeccd5" }


# 修改技能-添加
add_plugin:
  method: POST
  url: https://test-api-chatmax.orionstar.com/capi/v1/chatmax/modify_ctai_character
  headers: { 'Content-Type': 'application/json', 'Orionstar-Api-Key': "" }
  payload: { "ctaplugin_id": "a54d74273bd0756669e7972e32a05533","add_assoc_character_id": "87eb890804cead643fd3e02c1fdbe781","add_assoc_ctdataset_id": "36a1e8ab74ded3148c9ba0b95eaeccd5" }

open_api_monitor:
  test:
    url: https://test-openapi-chatmax.orionstar.com/v1/ctai/query_text_chat
    headers: { "Orionstar-Api-Key": "bz552c9d6032548d9966799860912bc22fz1b874a1996ec55dbba49cbc78bdbb0c0","Content-Type": "application/json","Accept": "application/json" }

  online:
    url: https://openapi.chatmax.net/v1/ctai/query_text_chat
    headers: { "Orionstar-Api-Key": "bz459268a86dae21f889513be3496c952fz5a3d284f3aaa4ac0cd8523aecefaddd7","Content-Type": "application/json","Accept": "application/json" }

upload_file:
  test:
    url: https://test-api-chatmax.orionstar.com/capi/v1/chatmax/upload_ctai_doc
    payload: { "character_id": "8c87bfa242f530076ff97dd37293cf07" }

  online:
    url: https://api.chatmax.net/capi/v1/chatmax/upload_ctai_doc
    payload: { "character_id": "2940ced24f36c819e55a7d228907ad32" }
