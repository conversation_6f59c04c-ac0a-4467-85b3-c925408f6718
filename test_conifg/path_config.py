# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : test_env.py
<AUTHOR> <EMAIL>
@Time   : 2023-10-29 20:12:29
@Version: v1.0
"""
import os.path

#   config目录
ORIOGIN_PATH = os.path.split(os.path.realpath(__file__))[0]
#   工程目录
PROJECT_PATH = os.path.split(ORIOGIN_PATH)[0]
#   家目录
HOME_PATH = os.path.split(PROJECT_PATH)[0]
#   sql语句目录
SQL_PATH = os.path.join(ORIOGIN_PATH, "sql/sql.yml")
#   环境配置路径
ENV_PATH = os.path.join(ORIOGIN_PATH, "env")
#   ENV_CONFIG_PATH
ENV_CONFIG_PATH = os.path.join(ENV_PATH, "envconfig.ini")
#   log目录
LOG_PATH = os.path.join(HOME_PATH, "logs")
#   上传文件目录
UPLOAD_PATH = os.path.join(HOME_PATH, "upload_file")
#   下载文件目录
DOWNLOAD_PATH = os.path.join(HOME_PATH, "download_file")
#   data目录
DATA_PATH = os.path.join(ORIOGIN_PATH, "data")
#   request 请求相关数据
REQUEST_DATA = os.path.join(DATA_PATH, "request/request.yml")
#   document 相关路径
DOCUMENT_PATH = os.path.join(PROJECT_PATH, "document_file")
#   测试数据输入
PROMPT_JSON = os.path.join(DOCUMENT_PATH, "input")
#   敏感词相关
SENSITIVE_INPUT_PATH = os.path.join(DOCUMENT_PATH, "input/sensitive")
SENSITIVE_OUTPUT_PATH = os.path.join(DOCUMENT_PATH, "output/sensitive")
#   模型结果相关
MODEL_OUTPUT_PATH = os.path.join(DOCUMENT_PATH, "output/model_result")
#   插件效果相关
PLUGIN_OUTPUT_PATH = os.path.join(DOCUMENT_PATH, "output/plugin_result")
