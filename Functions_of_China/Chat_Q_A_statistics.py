# 国内版本问答_非并发统计耗时、字数、性能指标
# kzy
# _*_ coding:utf-8 _*_

import requests
import json
import time
from datetime import datetime

url = 'https://openapi.chatmax.net/v1/ctai/query_text_chat'

headers = {
    'Content-Type': 'application/json',
    'orionstar-api-Key': 'bz8674c4a216000d8534729a78cab19accz7cace280d9185848ce52266f58886d8a'
}


def extract_answer_text(response):
    response_content = b''
    for chunk in response.iter_content(chunk_size=1024):
        response_content += chunk

    try:
        response_lines = response_content.decode('utf-8').splitlines()
    except UnicodeDecodeError:
        response_lines = response_content.decode('ISO-8859-1').splitlines()

    for line in response_lines:
        json_start = line.find('{')
        json_end = line.rfind('}')
        if json_start != -1 and json_end != -1:
            json_str = line[json_start:json_end + 1]
            try:
                data_dict = json.loads(json_str)
                if 'data' in data_dict and 'answer_text' in data_dict['data']:
                    answer_text = data_dict['data']['answer_text']
                    if answer_text:
                        if isinstance(answer_text, str):
                            yield answer_text
                        elif isinstance(answer_text, list):
                            yield from answer_text
            except json.JSONDecodeError:
                continue


# Function to count characters from the answer_text list
def count_characters(answer_text_list):
    total_character_count = 0
    for text in answer_text_list:
        if isinstance(text, str):  # Check if it's a string (not an empty list)
            total_character_count += len(text)
    return total_character_count


with open('/Users/<USER>/Downloads/pyshuju/Question_time.json', 'r', encoding='utf-8') as file:
    for line in file:
        # 记录请求发送的时间（精确到毫秒）
        request_send_time = time.time()

        data = {
            "query_text": line.strip(),
            "session_id": "sessionId1691058454354",
            "stream": "1"
        }

        response = requests.post(url, headers=headers, data=json.dumps(data), stream=True)

        # 处理响应数据
        first_packet_time = None
        all_responses = []  # Store all responses for a single request

        for answer_text in extract_answer_text(response):
            all_responses.append(answer_text)

            if not first_packet_time:
                # 记录第一个数据包返回的时间（精确到毫秒）
                first_packet_time = time.time()

        # 等待整个请求完全返回
        response.close()

        # 记录请求完成的时间（精确到毫秒）
        request_complete_time = time.time()

        # 转换为易读的时分秒格式（精确到毫秒）并保留毫秒的前3位
        request_send_time_str = datetime.fromtimestamp(request_send_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
        first_packet_time_str = datetime.fromtimestamp(first_packet_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
        request_complete_time_str = datetime.fromtimestamp(request_complete_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]

        # 计算并打印首包耗时和总耗时
        first_packet_duration = (first_packet_time - request_send_time) * 1000
        total_duration = request_complete_time - request_send_time

        # 打印请求发送的时间，第一个数据包返回的时间，和请求完成的时间
        print("请求发送的时间:", request_send_time_str)
        print("第一个数据包返回的时间:", first_packet_time_str)
        print("请求完成的时间:", request_complete_time_str)

        print("首包耗时: {:.3f} 毫秒".format(first_packet_duration))
        print("总耗时: {:.3f} 秒".format(total_duration))

        # 打印每次请求的返回字符数和answer_text
        total_character_count = count_characters(all_responses)
        print("返回字符数:", total_character_count)
        # print("Answer Text:", all_responses)


        # 计算性能指标并打印
        query_text_length = len(line.strip())
        performance_metric = (total_character_count + query_text_length) / total_duration
        print("性能指标:", performance_metric,"字/秒")
        print()