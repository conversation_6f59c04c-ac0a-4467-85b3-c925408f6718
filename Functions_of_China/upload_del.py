# 文件上传后查看是否上传成功，成功后进行删除
#kzy
# _*_ coding:utf-8 _*_
import os
import requests
import json

upload_url = 'https://dev-openapi.ainirobot.com/v1/ctai/upload_ctai_doc'
delete_url = 'https://dev-openapi.ainirobot.com/v1/ctai/delete_ctai_doc'
headers = {
    'orionstar-api-Key': 'bzdbbc52a3928dc253cb512e76fda86758z0911b4dcfe758b87f6e88fe06951559f'
}  # 认证信息

folder_path = '/Users/<USER>/Downloads/数据文件/team'  # 需要上传的文件夹路径，该文件夹下包含需要上传的文件
files = [f for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f)) and f.endswith('.txt')]

# 遍历文件列表进行上传和删除操作
for file_name in files:
    file_path = os.path.join(folder_path, file_name)

    # 构建请求数据
    data = {
        'check_only': '0',
    }

    files = {
        'ctai_doc': (None, f'{{"doc_name":"{file_name}"}}', 'application/json'),
        'content': (file_name, open(file_path, 'rb'), 'application/txt')
    }

    response = requests.post(upload_url, headers=headers, data=data, files=files)
    upload_response = response.json()

    ctdoc_id = upload_response.get('data', {}).get('ctdoc_id', '')

    if ctdoc_id:
        # 构建删除请求数据
        delete_data = {
            'ctdoc_id': ctdoc_id
        }

        delete_headers = {
            'Content-Type': 'application/json',
            'orionstar-api-Key': 'bzdbbc52a3928dc253cb512e76fda86758z0911b4dcfe758b87f6e88fe06951559f'
        }

        # 发送删除请求并解析删除响应的JSON数据
        delete_response = requests.post(delete_url, headers=delete_headers, json=delete_data).json()

        print(upload_response)
        print(delete_response)
