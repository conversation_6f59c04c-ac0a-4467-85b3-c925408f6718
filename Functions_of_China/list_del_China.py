#国内版本—查询list，然后进行删除功能
#kzy
# _*_ coding:utf-8 _*_
import requests

list_url = 'https://test-openapi-chatmax.orionstar.com/v1/ctai/ctai_doc_list'
del_url = 'https://test-openapi-chatmax.orionstar.com/v1/ctai/delete_ctai_doc'

headers = {
    'orionstar-api-Key': 'bz40a9774195bbd65713ebe84f64a28b57zf3e50eec5fd4abf3ae58540b433eeb05',
    'Content-Type': 'application/json',
}

list_data = {
    "page": "1",
    "page_rows": "200",
    "sort_type":"create_time_desc"
}

response = requests.post(list_url, headers=headers, json=list_data)

json_data = response.json()

ctai_doc_list = json_data.get('data', {}).get('ctai_doc_list', [])

all_end_succ = all(doc.get('ctai_doc', {}).get('process_status') == 'end_succ' for doc in ctai_doc_list)

if all_end_succ:
    ctdoc_ids = [doc.get('ctdoc_id') for doc in ctai_doc_list]

    for ctdoc_id in ctdoc_ids:
        del_data = {
            "ctdoc_id": ctdoc_id
        }

        del_response = requests.post(del_url, headers=headers, json=del_data)

        if del_response.json().get('ret') == '0':
            print(f"Document with ctdoc_id {ctdoc_id} was deleted successfully.")
        else:
            print(f"Failed to delete document with ctdoc_id {ctdoc_id}.")
else:
    print("Not all process_status are 'end_succ'.")
