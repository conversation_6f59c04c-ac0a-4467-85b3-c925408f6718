#国内版本上传、查询和删除融合功能
#kzy
# _*_ coding:utf-8 _*_
import os
import requests
import json
import time

# API上传、查询和删除的URL
upload_url = 'https://test-openapi-chatmax.orionstar.com/v1/ctai/upload_ctai_doc'
list_url = 'https://test-openapi-chatmax.orionstar.com/v1/ctai/ctai_doc_list'
del_url = 'https://test-openapi-chatmax.orionstar.com/v1/ctai/delete_ctai_doc'

# API请求头信息
headers = {
    'orionstar-api-Key': 'bz40a9774195bbd65713ebe84f64a28b57zf3e50eec5fd4abf3ae58540b433eeb05',
}

# 文件夹路径和获取所有txt文件
folder_path = '/Users/<USER>/Downloads/数据文件/team'
files = [f for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f)) and f.endswith('.txt')]

# 存储上传文件的ID列表
ctdoc_ids = []

# 并发上传文件的数量
concurrent_uploads = 10
for i in range(0, len(files), concurrent_uploads):
    batch_files = files[i:i + concurrent_uploads]

    # 按照批次上传文件
    for file_name in batch_files:
        file_path = os.path.join(folder_path, file_name)

        # 请求上传API所需数据
        data = {
            'check_only': '0',
        }

        files = {
            'ctai_doc': (None, f'{{"doc_name":"{file_name}"}}', 'application/json'),
            'content': (file_name, open(file_path, 'rb'), 'application/txt')
        }

        # 发送上传请求
        response = requests.post(upload_url, headers=headers, data=data, files=files)
        upload_response = response.json()

        # 提取上传文件的ID并存储到ctdoc_ids列表中
        ctdoc_id = upload_response.get('data', {}).get('ctdoc_id', '')
        if ctdoc_id:
            ctdoc_ids.append(ctdoc_id)
        print(upload_response)

# 上传完成后等待10秒

time.sleep(10)

# 轮询检查上传的文件处理状态
while True:
    list_data = {
        "page": "1",
        "page_rows": "10",
        "sort_type": "create_time_desc"
    }

    # 查询上传文件状态
    response = requests.post(list_url, headers=headers, json=list_data)
    json_data = response.json()

    # 获取所有上传文件的状态列表
    ctai_doc_list = json_data.get('data', {}).get('ctai_doc_list', [])
    all_end_succ = all(doc.get('ctai_doc', {}).get('process_status') == 'end_succ' for doc in ctai_doc_list)

    # 如果所有文件的处理状态都是'end_succ'，则退出循环
    if all_end_succ:
        break

    # 否则等待10秒继续轮询
    print("Not all process_status are 'end_succ'. Polling again in 10 seconds...")
    time.sleep(10)

# 完成文件处理后，根据ID删除文件
list_headers = {
    'orionstar-api-Key': 'bz40a9774195bbd65713ebe84f64a28b57zf3e50eec5fd4abf3ae58540b433eeb05',
    'Content-Type': 'application/json',
}

for ctdoc_id in ctdoc_ids:
    # 构建删除API请求数据
    del_data = {
        "ctdoc_id": ctdoc_id
    }

    # 发送删除请求
    del_response = requests.post(del_url, headers=list_headers, json=del_data)

    # 检查删除结果并打印
    if del_response.json().get('ret') == '0':
        print(f"Document with ctdoc_id {ctdoc_id} was deleted successfully.")
    else:
        print(f"Failed to delete document with ctdoc_id {ctdoc_id}.")
