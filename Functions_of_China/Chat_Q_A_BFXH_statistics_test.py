# -*- coding:utf-8 -*-
# kzy
#国内版test环境并发+并发耗时功能

import requests
import json
import time
from datetime import datetime
import concurrent.futures
import pandas as pd

url = 'https://test-openapi-chatmax.orionstar.com/v1/ctai/query_text_chat'

headers = {
    'Content-Type': 'application/json',
    'orionstar-api-Key': 'bz40a9774195bbd65713ebe84f64a28b57zf3e50eec5fd4abf3ae58540b433eeb05'
}


def extract_answer_text(response):
    response_content = b''
    first_packet_time = None
    second_packet_time = None

    for chunk in response.iter_content(chunk_size=1024):
        response_content += chunk

        if not first_packet_time:
            first_packet_time = time.time()

        try:
            response_lines = response_content.decode('utf-8').splitlines()
        except UnicodeDecodeError:
            response_lines = response_content.decode('ISO-8859-1').splitlines()

        all_responses = []

        for line in response_lines:
            json_start = line.find('{')
            json_end = line.rfind('}')
            if json_start != -1 and json_end != -1:
                json_str = line[json_start:json_end + 1]
                try:
                    data_dict = json.loads(json_str)
                    if 'data' in data_dict and 'answer_text' in data_dict['data']:
                        answer_text = data_dict['data']['answer_text']
                        if answer_text:
                            if isinstance(answer_text, str):
                                all_responses.append(answer_text)
                            elif isinstance(answer_text, list):
                                all_responses.extend(answer_text)
                    if not second_packet_time:
                        second_packet_time = time.time()
                except json.JSONDecodeError:
                    continue

    return all_responses, first_packet_time, second_packet_time


def count_characters(answer_text_list):
    total_character_count = 0
    for text in answer_text_list:
        if isinstance(text, str):  # 检查是否为字符串（而不是空列表）
            total_character_count += len(text)
    return total_character_count


def process_single_request(query_text, iteration, concurrency):
    request_send_time = time.time()

    data = {
        "query_text": query_text.strip(),
        "session_id": "orinoutsidad234c7dd8a237a4e50fdcaf230b1816",
        "stream": "1"
    }

    response = requests.post(url, headers=headers, data=json.dumps(data), stream=True)

    all_responses, first_packet_time, second_packet_time = extract_answer_text(response)

    if second_packet_time:
        first_packet_duration = (second_packet_time - request_send_time) * 1000
    else:
        first_packet_duration = 0

    response.close()

    request_complete_time = time.time()

    request_send_time_str = datetime.fromtimestamp(request_send_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]

    if second_packet_time:
        first_packet_time_str = "{:.3f}".format(first_packet_duration) + " 毫秒"
    else:
        first_packet_time_str = "无第二个数据包"

    request_complete_time_str = datetime.fromtimestamp(request_complete_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]

    total_duration = request_complete_time - request_send_time
    second_packet_time=datetime.fromtimestamp(second_packet_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:23]
    print("问题：", query_text.strip())
    print("循环次数：", concurrency)
    print("第几次并发：", iteration)
    print("请求发送的时间:", request_send_time_str)
    print("第一个数据包返回的时间:", second_packet_time)
    print("请求完成的时间:", request_complete_time_str)

    print("首包耗时:", first_packet_time_str)
    print("总耗时: {:.3f} 秒".format(total_duration))

    total_character_count = count_characters(all_responses)
    query_text_length = len(query_text.strip())
    print("返回字符数+输入总字符数:", total_character_count + query_text_length)
    performance_metric = (total_character_count + query_text_length) / total_duration
    print("性能指标:", performance_metric, "字/秒")
    print()

    return [concurrency, iteration, query_text.strip(), request_send_time_str,second_packet_time, request_complete_time_str,
            first_packet_duration, total_duration, total_character_count, performance_metric]


def write_to_excel(data):
    current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    excel_filename = f"/Users/<USER>/Downloads/pyshuju/guoneibantest_{current_time}.xlsx"

    df = pd.DataFrame(data,
                      columns=["循环次数", "第几次并发", "问题", "请求发送时间","首包返回时间", "请求完成时间",
                               "首包耗时(ms)", "总耗时(s)", "返回字符数+输入总字符数", "性能指标(字/秒)"])
    df.to_excel(excel_filename, index=False)
    print(f"数据已写入Excel文件：{excel_filename}")


if __name__ == '__main__':
    with open('/Users/<USER>/Downloads/pyshuju/Question.json', 'r', encoding='utf-8') as file:
        lines = file.readlines()

        data_to_write = []

        for concurrency in range(1):
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                line_batches = [lines[i:i + 1] for i in range(0, len(lines), 1)]

                for iteration, line_batch in enumerate(line_batches):
                    futures = [executor.submit(process_single_request, line, iteration + 1, concurrency + 1) for
                               line in line_batch]

                    concurrent.futures.wait(futures)

                    iteration_data = []
                    for future in futures:
                        iteration_data.append(future.result())

                    data_to_write.extend(iteration_data)

        write_to_excel(data_to_write)
