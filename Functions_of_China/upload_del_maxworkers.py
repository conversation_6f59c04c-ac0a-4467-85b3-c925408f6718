#通过文件上传接口，并发上传
#kzy
# _*_ coding:utf-8 _*_
import os
import time
import requests
from concurrent.futures import ThreadPoolExecutor

upload_url = 'https://dev-openapi.ainirobot.com/v1/ctai/upload_ctai_doc'
delete_url = 'https://dev-openapi.ainirobot.com/v1/ctai/delete_ctai_doc'

headers = {
    'orionstar-api-Key': 'bzdbbc52a3928dc253cb512e76fda86758z0911b4dcfe758b87f6e88fe06951559f'
}

folder_path = '/Users/<USER>/Downloads/数据文件/team'
files = [f for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f)) and f.endswith('.txt')]

def upload_file(file_name):
    file_path = os.path.join(folder_path, file_name)

    data = {
        'check_only': '0',
    }

    files = {
        'ctai_doc': (None, f'{{"doc_name":"{file_name}"}}', 'application/json'),
        'content': (file_name, open(file_path, 'rb'), 'application/txt')
    }

    response = requests.post(upload_url, headers=headers, data=data, files=files)
    upload_response = response.json()

    ctdoc_id = upload_response.get('data', {}).get('ctdoc_id', '')

    if ctdoc_id:
        delete_data = {
            'ctdoc_id': ctdoc_id
        }

        delete_headers = {
            'Content-Type': 'application/json',
            'orionstar-api-Key': 'bzdbbc52a3928dc253cb512e76fda86758z0911b4dcfe758b87f6e88fe06951559f'
        }
        time.sleep(4)
        delete_response = requests.post(delete_url, headers=delete_headers, json=delete_data).json()
        print(upload_response)
        print(delete_response)

# 设置线程池大小为40
max_workers = 40

with ThreadPoolExecutor(max_workers=max_workers) as executor:
    executor.map(upload_file, files)
