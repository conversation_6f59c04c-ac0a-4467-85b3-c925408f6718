import pandas as pd
from sentence_transformers import SentenceTransformer, util
import torch
import warnings

warnings.filterwarnings("ignore", category=UserWarning, module="urllib3")

model_name = 'paraphrase-MiniLM-L6-v2'
model = SentenceTransformer(model_name)


file_path = "/Users/<USER>/Downloads/duibi.xlsx"
df = pd.read_excel(file_path)

similarities = []

for index, row in df.iterrows():
    sentence1 = str(row['Actual'])            #新模型回答的答案放在这行下
    sentence2 = str(row['Standard'])             #标准答案放在这行下


    embeddings1 = model.encode(sentence1, convert_to_tensor=True)
    embeddings2 = model.encode(sentence2, convert_to_tensor=True)

    cosine_score = util.pytorch_cos_sim(embeddings1, embeddings2)
    similarity = cosine_score.item()

    similarity = max(0, min(1, similarity))

    similarities.append(similarity)

df['Similarity'] = similarities

average_similarity = sum(similarities) / len(similarities)

df['Average_Similarity'] = average_similarity

# 保存带有相似度得分和平均分数的Excel文件
df.to_excel(file_path, index=False)
