from paragraph_similarity.data_loader import load_paragraphs_from_excel
from paragraph_similarity.text_preprocessor import preprocess_text
from paragraph_similarity.openai_integration import generate_text_with_openai
from paragraph_similarity.bert_integration import classify_text_with_bert

if __name__ == "__main__":
    file_path = "/Users/<USER>/Downloads/111.xlsx"  # 替换为你的Excel文件路径
    paragraphs_df = load_paragraphs_from_excel(file_path)

    paragraphs1 = paragraphs_df['Column1'].apply(preprocess_text)
    paragraphs2 = paragraphs_df['Column2'].apply(preprocess_text)

    api_key = "***************************************************"  # 替换为你的OpenAI API密钥
    generated_text = generate_text_with_openai(api_key, "请对比以下的段落，并给出相似分'")

    model_name = "bert-base-uncased"
    text = "请对比以下的段落，并给出相似分"
    predicted_class = classify_text_with_bert(text, model_name)

    print("Generated Text from Openai Evals:", generated_text)
    print("BERT Predicted Class:", predicted_class)
