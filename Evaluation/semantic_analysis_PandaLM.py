from transformers import AutoTokenizer, AutoModelForCausalLM
import torch
from sklearn.metrics.pairwise import cosine_similarity

# 加载PandaLM-7B-v1模型和分词器
tokenizer = AutoTokenizer.from_pretrained("WeOpenML/PandaLM-7B-v1", use_fast=False)
model = AutoModelForCausalLM.from_pretrained("WeOpenML/PandaLM-7B-v1")

# 输入文本
text1 = "这是文本1"
text2 = "这是文本2"

# 对文本进行编码
inputs1 = tokenizer(text1, return_tensors="pt", padding=True, truncation=True)
inputs2 = tokenizer(text2, return_tensors="pt", padding=True, truncation=True)

# 生成模型的输出
with torch.no_grad():
    output1 = model.generate(**inputs1)
    output2 = model.generate(**inputs2)

# 将生成的输出转换为嵌入表示
embedding1 = model.get_input_embeddings()(output1)
embedding2 = model.get_input_embeddings()(output2)

# 计算余弦相似度
similarity = cosine_similarity(embedding1, embedding2)

print("文本1和文本2的相似度:", similarity[0][0])
