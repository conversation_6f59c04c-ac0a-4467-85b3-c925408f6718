from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

def calculate_cosine_similarity(paragraphs1, paragraphs2):
    tfidf_vectorizer = TfidfVectorizer()
    tfidf_matrix1 = tfidf_vectorizer.fit_transform(paragraphs1)
    tfidf_matrix2 = tfidf_vectorizer.transform(paragraphs2)
    similarity_matrix = cosine_similarity(tfidf_matrix1, tfidf_matrix2)
    return similarity_matrix
