# -*- coding: utf-8 -*-
"""
@Project: jytest
@File   : main.py
<AUTHOR> <EMAIL>
@Time   : 2023-10-30 10:18:08
@Version: v1.0
"""

from flask_cors import CORS  # 导入包
from app import create_app
from flask_migrate import Migrate
from test_conifg.config import port
import os
print("run")
if os.path.exists('.env'):
    print('Importing environment from .env...')
    for line in open('.env'):
        var = line.strip().split('=')
        if len(var) == 2:
            os.environ[var[0]] = var[1]

# from flask_script import Manager, Shell
app = create_app(os.getenv('FLASK_CONFIG') or 'default.cfg')
# db_model = create_db_model(app)
# try:
#     scheduler.start()
# except Exception as e:
#     pass
manager = Migrate(app)
CORS(app, supports_credentials=True)  # 设置参数


def make_shell_context():
    return dict(app=app)


# manager.add_command("shell", Shell(make_context=make_shell_context))

#
# @manager.command
# def list_routes():
#     output = []
#     for rule in app.url_map.iter_rules():
#
#         options = {}
#         for arg in rule.arguments:
#             options[arg] = "[{0}]".format(arg)
#
#         methods = ','.join(rule.methods)
#         url = url_for(rule.endpoint, **options)
#         # line = urllib.unquote("{:50s} {:20s} {}".format(rule.endpoint, methods, url))
#         # output.append(line)
#         output.append(url)
#
#     for line in sorted(output):
#         print(line)


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=port)
    # manager.run()
