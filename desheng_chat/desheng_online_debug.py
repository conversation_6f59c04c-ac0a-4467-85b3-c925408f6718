#德生线上环境查询debug信息


import requests

url = 'http://192.168.1.254:30342/v1/ctai/ctai_query_log_list'    #德生线上环境地址
headers = {
    'orionstar-api-key': 'bzb26a30f4714a25cd2ea2bca5c6d7f80ez97f2da905f8e5de7732c396b9c3a22a2',      #不要外传
    'Content-Type': 'application/json'
}
data = {
    # "query_text": "摩托车",       #精准查询
    "filter_query_text": "好烦呢" ,    #模糊查询
    "page": 1,
    "page_rows": 20,
    # "start_creat_time":1692627607,    #从2023年8月21号10点10分开始
    # "end_creat_time":1693232407    #到2023年8月28号10点10分结束
}

response = requests.post(url, headers=headers, json=data)
print(response.text)   