#德生线上批量请求获取debug信息
import requests
import time
import openpyxl

url = 'http://192.168.1.254:30342/v1/ctai/ctai_query_log_list'  # 德生线上环境地址
headers = {
    'orionstar-api-key': 'bzb26a30f4714a25cd2ea2bca5c6d7f80ez97f2da905f8e5de7732c396b9c3a22a2',  # 不要外传
    'Content-Type': 'application/json'
}

data = {
    # "query_text": "摩托车和全地形车在不同领域的应用有何不同？",       # 精准查询
    # "filter_query_text": "摩托车"     # 模糊查询
    "page_rows": 1,
    # "start_create_time": 1692453643,  # 2023年8月19号22点00分
    # "end_create_time": 1693195243  # 2023年8月28号12点00分
    # "start_create_time": 1693195293,  # 2023年8月28号12点00分
    # "end_create_time": 1693267293  # 2023年8月29号08点00分

}

increment_step = 1  # 每次递增的步长
max_page = 10  # 递增上限

wb = openpyxl.Workbook()
ws = wb.active
ws.append(["Page", "Response"])

response_data_list = []  # 存储响应数据的列表

for page in range(1, max_page + 1, increment_step):
    data["page"] = page

    response = requests.post(url, headers=headers, json=data)
    print(f"Page: {page}, Response: {response.text}")

    if not response.text:
        print("Response is empty, ending the loop.")
        break

    response_data = response.json()

    if not response_data['data']['ctai_query_log_list']:
        print("ctai_query_log_list is empty, ending the loop.")
        break

    response_data_list.append(response.text)  # 将响应数据添加到列表中

    # time.sleep(1)  # 避免请求过于频繁，可以根据需要调整等待时间

for page, response_text in enumerate(response_data_list, start=1):
    ws.append([page, response_text])

excel_file_path = "/Users/<USER>/Downloads/pyshuju/pyexcel/result_debug_28-29.xlsx"
wb.save(excel_file_path)
print(f"Excel file saved at {excel_file_path}")
