#德生测试环境查询debug信息


import requests

url = 'http://openapi.chatmax-self-hosting.ai/v1/ctai/ctai_query_log_list'    #德生线上环境地址
headers = {
    'orionstar-api-key': 'bz75dd2778b2aab058ef782280bc6ab786zd7144fefd48198fe4716f4fd358c3f6b',      #不要外传
    'Content-Type': 'application/json'
}
data = {
    # "query_text": "《实施以社会保障卡为载体的居民服务“一卡通”建设东莞市民卡工作方案》将如何提升政务服务效能和打造东莞数字政府建设新名片？",       #精准查询
    "filter_query_text": "将如何提升政务服务效能和打造东莞数字政府建设新名片？" ,    #模糊查询
    # "page": 1,
    # "page_rows": 20,
    # "start_creat_time":1692627607,    #2023年8月21号10点10分
    # "end_creat_time":1693232407    #2023年8月28号10点10分
}

response = requests.post(url, headers=headers, json=data)
print(response.text)   