{"add_bos_token": false, "add_eos_token": false, "auto_map": {"AutoTokenizer": ["tokenization_baichuan.BaiChuanTokenizer", null]}, "bos_token": {"__type": "AddedToken", "content": "<s>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false}, "clean_up_tokenization_spaces": false, "eos_token": {"__type": "AddedToken", "content": "</s>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false}, "model_max_length": 1000000000000000019884624838656, "pad_token": null, "sp_model_kwargs": {}, "special_tokens_map_file": "./resource/tokenizers/tokenizer-002/special_tokens_map.json", "tokenizer_class": "BaiChuanTokenizer", "unk_token": {"__type": "AddedToken", "content": "<unk>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false}}